#include "AnalysisSettingsDialog.h"
#include "../core/AnalysisSettings.h"
#include <QApplication>
#include <QScreen>

AnalysisSettingsDialog::AnalysisSettingsDialog(AnalysisSettings *settings, QWidget *parent)
    : QDialog(parent)
    , m_analysisSettings(settings)
    , m_settingsModified(false)
{
    setWindowTitle("Analysis Settings");
    setModal(true);
    
    // Set dialog size based on screen size
    QScreen *screen = QApplication::primaryScreen();
    QRect screenGeometry = screen->availableGeometry();
    int width = qMin(900, static_cast<int>(screenGeometry.width() * 0.8));
    int height = qMin(700, static_cast<int>(screenGeometry.height() * 0.8));
    resize(width, height);
    
    setupUI();
    connectSignals();
    loadCurrentSettings();
    updatePresetsList();
}

AnalysisSettingsDialog::~AnalysisSettingsDialog()
{
}

void AnalysisSettingsDialog::setupUI()
{
    QVBoxLayout *mainLayout = new QVBoxLayout(this);
    
    // Create main splitter
    m_mainSplitter = new QSplitter(Qt::Horizontal);
    mainLayout->addWidget(m_mainSplitter);
    
    // Setup preset management on the left
    setupPresetManagement();
    
    // Setup tabbed settings on the right
    m_tabWidget = new QTabWidget;
    m_mainSplitter->addWidget(m_tabWidget);
    
    setupAudioAnalysisTab();
    setupClassificationTab();
    setupReliabilityTab();
    setupPerformanceTab();
    
    // Set splitter proportions (30% presets, 70% settings)
    m_mainSplitter->setSizes({300, 600});
    
    // Dialog buttons
    QHBoxLayout *buttonLayout = new QHBoxLayout;
    m_resetBtn = new QPushButton("Reset to Defaults");
    m_applyBtn = new QPushButton("Apply");
    m_cancelBtn = new QPushButton("Cancel");
    m_okBtn = new QPushButton("OK");
    
    buttonLayout->addWidget(m_resetBtn);
    buttonLayout->addStretch();
    buttonLayout->addWidget(m_applyBtn);
    buttonLayout->addWidget(m_cancelBtn);
    buttonLayout->addWidget(m_okBtn);
    
    mainLayout->addLayout(buttonLayout);
}

void AnalysisSettingsDialog::setupPresetManagement()
{
    m_presetGroup = new QGroupBox("Analysis Presets");
    m_mainSplitter->addWidget(m_presetGroup);
    
    QVBoxLayout *presetLayout = new QVBoxLayout(m_presetGroup);
    
    // Preset selection
    presetLayout->addWidget(new QLabel("Select Preset:"));
    m_presetComboBox = new QComboBox;
    presetLayout->addWidget(m_presetComboBox);
    addTooltip(m_presetComboBox, "Choose a preset configuration for analysis settings");
    
    // Preset management buttons
    QHBoxLayout *presetButtonLayout = new QHBoxLayout;
    m_savePresetBtn = new QPushButton("Save");
    m_deletePresetBtn = new QPushButton("Delete");
    m_newPresetBtn = new QPushButton("New");
    m_duplicatePresetBtn = new QPushButton("Duplicate");
    
    presetButtonLayout->addWidget(m_savePresetBtn);
    presetButtonLayout->addWidget(m_deletePresetBtn);
    presetButtonLayout->addWidget(m_newPresetBtn);
    presetButtonLayout->addWidget(m_duplicatePresetBtn);
    presetLayout->addLayout(presetButtonLayout);
    
    addTooltip(m_savePresetBtn, "Save current settings as a preset");
    addTooltip(m_deletePresetBtn, "Delete the selected custom preset");
    addTooltip(m_newPresetBtn, "Create a new preset with default settings");
    addTooltip(m_duplicatePresetBtn, "Duplicate the selected preset");
    
    // Preset description
    presetLayout->addWidget(new QLabel("Description:"));
    m_presetDescriptionEdit = new QTextEdit;
    m_presetDescriptionEdit->setMaximumHeight(100);
    m_presetDescriptionEdit->setReadOnly(true);
    presetLayout->addWidget(m_presetDescriptionEdit);
    
    presetLayout->addStretch();
}

void AnalysisSettingsDialog::setupAudioAnalysisTab()
{
    m_audioAnalysisTab = new QWidget;
    m_tabWidget->addTab(m_audioAnalysisTab, "Audio Analysis");
    
    QVBoxLayout *tabLayout = new QVBoxLayout(m_audioAnalysisTab);
    
    // Audio Processing Parameters
    QGroupBox *audioGroup = new QGroupBox("Audio Processing Parameters");
    QFormLayout *audioLayout = new QFormLayout(audioGroup);
    
    m_sampleRateSpin = new QSpinBox;
    m_sampleRateSpin->setRange(8000, 96000);
    m_sampleRateSpin->setSuffix(" Hz");
    addTooltip(m_sampleRateSpin, "Target sample rate for audio analysis. Higher rates provide better frequency resolution but increase processing time.");
    
    m_bitDepthSpin = new QSpinBox;
    m_bitDepthSpin->setRange(16, 32);
    m_bitDepthSpin->setSuffix(" bit");
    addTooltip(m_bitDepthSpin, "Audio bit depth for processing. Higher bit depth provides better dynamic range.");
    
    m_mfccCoefficientsSpin = new QSpinBox;
    m_mfccCoefficientsSpin->setRange(8, 24);
    addTooltip(m_mfccCoefficientsSpin, "Number of MFCC coefficients for feature extraction. More coefficients capture more detail but increase processing time.");
    
    m_windowSizeSpin = new QDoubleSpinBox;
    m_windowSizeSpin->setRange(0.010, 0.100);
    m_windowSizeSpin->setSingleStep(0.005);
    m_windowSizeSpin->setSuffix(" sec");
    m_windowSizeSpin->setDecimals(3);
    addTooltip(m_windowSizeSpin, "Analysis window size in seconds. Smaller windows provide better time resolution, larger windows provide better frequency resolution.");
    
    m_hopLengthSpin = new QDoubleSpinBox;
    m_hopLengthSpin->setRange(0.1, 0.9);
    m_hopLengthSpin->setSingleStep(0.1);
    m_hopLengthSpin->setDecimals(1);
    addTooltip(m_hopLengthSpin, "Hop length as fraction of window size. Smaller values provide better time resolution but increase processing time.");
    
    m_spectralOverlapSpin = new QDoubleSpinBox;
    m_spectralOverlapSpin->setRange(0.0, 0.9);
    m_spectralOverlapSpin->setSingleStep(0.1);
    m_spectralOverlapSpin->setDecimals(1);
    addTooltip(m_spectralOverlapSpin, "Spectral analysis overlap ratio. Higher overlap provides smoother analysis but increases processing time.");
    
    audioLayout->addRow("Sample Rate:", m_sampleRateSpin);
    audioLayout->addRow("Bit Depth:", m_bitDepthSpin);
    audioLayout->addRow("MFCC Coefficients:", m_mfccCoefficientsSpin);
    audioLayout->addRow("Window Size:", m_windowSizeSpin);
    audioLayout->addRow("Hop Length:", m_hopLengthSpin);
    audioLayout->addRow("Spectral Overlap:", m_spectralOverlapSpin);
    
    tabLayout->addWidget(audioGroup);

    // Frequency Range Settings
    QGroupBox *freqGroup = new QGroupBox("Frequency Range Limits");
    QFormLayout *freqLayout = new QFormLayout(freqGroup);

    m_dialogueFreqMinSpin = new QSpinBox;
    m_dialogueFreqMinSpin->setRange(80, 8000);
    m_dialogueFreqMinSpin->setSuffix(" Hz");
    addTooltip(m_dialogueFreqMinSpin, "Minimum frequency for dialogue detection. Human speech fundamentals typically start around 80-300 Hz.");

    m_dialogueFreqMaxSpin = new QSpinBox;
    m_dialogueFreqMaxSpin->setRange(1000, 20000);
    m_dialogueFreqMaxSpin->setSuffix(" Hz");
    addTooltip(m_dialogueFreqMaxSpin, "Maximum frequency for dialogue detection. Speech intelligibility is mainly below 3400 Hz.");

    m_musicFreqMinSpin = new QSpinBox;
    m_musicFreqMinSpin->setRange(20, 1000);
    m_musicFreqMinSpin->setSuffix(" Hz");
    addTooltip(m_musicFreqMinSpin, "Minimum frequency for music detection. Full spectrum analysis typically starts at 20 Hz.");

    m_musicFreqMaxSpin = new QSpinBox;
    m_musicFreqMaxSpin->setRange(5000, 22050);
    m_musicFreqMaxSpin->setSuffix(" Hz");
    addTooltip(m_musicFreqMaxSpin, "Maximum frequency for music detection. Full spectrum analysis goes up to Nyquist frequency.");

    m_sfxFreqMinSpin = new QSpinBox;
    m_sfxFreqMinSpin->setRange(20, 2000);
    m_sfxFreqMinSpin->setSuffix(" Hz");
    addTooltip(m_sfxFreqMinSpin, "Minimum frequency for sound effects detection.");

    m_sfxFreqMaxSpin = new QSpinBox;
    m_sfxFreqMaxSpin->setRange(2000, 22050);
    m_sfxFreqMaxSpin->setSuffix(" Hz");
    addTooltip(m_sfxFreqMaxSpin, "Maximum frequency for sound effects detection.");

    freqLayout->addRow("Dialogue Min:", m_dialogueFreqMinSpin);
    freqLayout->addRow("Dialogue Max:", m_dialogueFreqMaxSpin);
    freqLayout->addRow("Music Min:", m_musicFreqMinSpin);
    freqLayout->addRow("Music Max:", m_musicFreqMaxSpin);
    freqLayout->addRow("SFX Min:", m_sfxFreqMinSpin);
    freqLayout->addRow("SFX Max:", m_sfxFreqMaxSpin);

    tabLayout->addWidget(freqGroup);

    // Noise and Silence Detection
    QGroupBox *noiseGroup = new QGroupBox("Noise Floor & Silence Detection");
    QFormLayout *noiseLayout = new QFormLayout(noiseGroup);

    m_noiseFloorSpin = new QDoubleSpinBox;
    m_noiseFloorSpin->setRange(-80.0, -20.0);
    m_noiseFloorSpin->setSuffix(" dB");
    m_noiseFloorSpin->setDecimals(1);
    addTooltip(m_noiseFloorSpin, "Noise floor threshold in dB. Signals below this level are considered noise.");

    m_silenceThresholdSpin = new QDoubleSpinBox;
    m_silenceThresholdSpin->setRange(-60.0, -10.0);
    m_silenceThresholdSpin->setSuffix(" dB");
    m_silenceThresholdSpin->setDecimals(1);
    addTooltip(m_silenceThresholdSpin, "Silence detection threshold in dB. Signals below this level are considered silence.");

    m_silenceMinDurationSpin = new QDoubleSpinBox;
    m_silenceMinDurationSpin->setRange(0.1, 10.0);
    m_silenceMinDurationSpin->setSuffix(" sec");
    m_silenceMinDurationSpin->setDecimals(1);
    addTooltip(m_silenceMinDurationSpin, "Minimum duration for silence detection. Shorter quiet periods are ignored.");

    noiseLayout->addRow("Noise Floor:", m_noiseFloorSpin);
    noiseLayout->addRow("Silence Threshold:", m_silenceThresholdSpin);
    noiseLayout->addRow("Min Silence Duration:", m_silenceMinDurationSpin);

    tabLayout->addWidget(noiseGroup);
    tabLayout->addStretch();
}

void AnalysisSettingsDialog::setupClassificationTab()
{
    m_classificationTab = new QWidget;
    m_tabWidget->addTab(m_classificationTab, "Classification Model");

    QVBoxLayout *tabLayout = new QVBoxLayout(m_classificationTab);

    // Model Selection
    QGroupBox *modelGroup = new QGroupBox("Model Settings");
    QFormLayout *modelLayout = new QFormLayout(modelGroup);

    m_modelSelectionCombo = new QComboBox;
    m_modelSelectionCombo->addItems({"Standard", "Enhanced", "Lightweight", "Experimental"});
    addTooltip(m_modelSelectionCombo, "Select the classification model. Standard provides good balance, Enhanced offers better accuracy, Lightweight is faster.");

    m_confidenceThresholdSpin = new QDoubleSpinBox;
    m_confidenceThresholdSpin->setRange(0.1, 1.0);
    m_confidenceThresholdSpin->setSingleStep(0.1);
    m_confidenceThresholdSpin->setDecimals(1);
    addTooltip(m_confidenceThresholdSpin, "Minimum confidence threshold for classification decisions. Higher values reduce false positives but may miss some content.");

    modelLayout->addRow("Model Selection:", m_modelSelectionCombo);
    modelLayout->addRow("Confidence Threshold:", m_confidenceThresholdSpin);

    tabLayout->addWidget(modelGroup);

    // Content Detection
    QGroupBox *contentGroup = new QGroupBox("Content Detection");
    QVBoxLayout *contentLayout = new QVBoxLayout(contentGroup);

    m_speakerDiarizationCheck = new QCheckBox("Enable Speaker Diarization");
    addTooltip(m_speakerDiarizationCheck, "Identify and separate different speakers in dialogue. Improves organization but increases processing time.");

    m_musicDetectionCheck = new QCheckBox("Enable Music Detection");
    addTooltip(m_musicDetectionCheck, "Detect and classify music content. Essential for proper track organization.");

    m_sfxDetectionCheck = new QCheckBox("Enable SFX Detection");
    addTooltip(m_sfxDetectionCheck, "Detect and classify sound effects. Helps separate effects from dialogue and music.");

    contentLayout->addWidget(m_speakerDiarizationCheck);
    contentLayout->addWidget(m_musicDetectionCheck);
    contentLayout->addWidget(m_sfxDetectionCheck);

    tabLayout->addWidget(contentGroup);

    // Feature Extraction
    QGroupBox *featureGroup = new QGroupBox("Feature Extraction");
    QFormLayout *featureLayout = new QFormLayout(featureGroup);

    m_enableSpectralFeaturesCheck = new QCheckBox("Enable Spectral Features");
    addTooltip(m_enableSpectralFeaturesCheck, "Extract spectral features (MFCC, spectral centroid, etc.). Essential for most classification tasks.");

    m_enableTemporalFeaturesCheck = new QCheckBox("Enable Temporal Features");
    addTooltip(m_enableTemporalFeaturesCheck, "Extract temporal features (zero-crossing rate, tempo, etc.). Useful for rhythm and speech pattern analysis.");

    m_temporalSmoothingSpin = new QDoubleSpinBox;
    m_temporalSmoothingSpin->setRange(0.1, 5.0);
    m_temporalSmoothingSpin->setSuffix(" sec");
    m_temporalSmoothingSpin->setDecimals(1);
    addTooltip(m_temporalSmoothingSpin, "Temporal smoothing window size. Larger windows provide more stable results but less temporal precision.");

    m_contextWindowSpin = new QDoubleSpinBox;
    m_contextWindowSpin->setRange(0.5, 10.0);
    m_contextWindowSpin->setSuffix(" sec");
    m_contextWindowSpin->setDecimals(1);
    addTooltip(m_contextWindowSpin, "Context window size for classification. Larger windows consider more surrounding audio for better accuracy.");

    featureLayout->addRow(m_enableSpectralFeaturesCheck);
    featureLayout->addRow(m_enableTemporalFeaturesCheck);
    featureLayout->addRow("Temporal Smoothing:", m_temporalSmoothingSpin);
    featureLayout->addRow("Context Window:", m_contextWindowSpin);

    tabLayout->addWidget(featureGroup);
    tabLayout->addStretch();
}

void AnalysisSettingsDialog::setupReliabilityTab()
{
    m_reliabilityTab = new QWidget;
    m_tabWidget->addTab(m_reliabilityTab, "Processing Reliability");

    QVBoxLayout *tabLayout = new QVBoxLayout(m_reliabilityTab);

    // Error Handling
    QGroupBox *errorGroup = new QGroupBox("Error Handling");
    QFormLayout *errorLayout = new QFormLayout(errorGroup);

    m_errorHandlingCombo = new QComboBox;
    m_errorHandlingCombo->addItems({"Skip", "Retry", "Mark as Unknown", "Use Fallback"});
    addTooltip(m_errorHandlingCombo, "Strategy for handling corrupted or problematic audio data during analysis.");

    m_lowConfidenceBehaviorCombo = new QComboBox;
    m_lowConfidenceBehaviorCombo->addItems({"Mark", "Skip", "Use Best Guess", "Request Manual Review"});
    addTooltip(m_lowConfidenceBehaviorCombo, "Behavior when classification confidence is below threshold.");

    errorLayout->addRow("Error Handling:", m_errorHandlingCombo);
    errorLayout->addRow("Low Confidence:", m_lowConfidenceBehaviorCombo);

    tabLayout->addWidget(errorGroup);

    // Quality Assurance
    QGroupBox *qaGroup = new QGroupBox("Quality Assurance");
    QVBoxLayout *qaLayout = new QVBoxLayout(qaGroup);

    m_enableValidationRulesCheck = new QCheckBox("Enable Validation Rules");
    addTooltip(m_enableValidationRulesCheck, "Apply consistency rules to classification results (e.g., minimum segment duration, logical transitions).");

    m_enableQualityAssuranceCheck = new QCheckBox("Enable Quality Assurance Checks");
    addTooltip(m_enableQualityAssuranceCheck, "Perform additional verification steps to ensure classification accuracy. Increases processing time.");

    qaLayout->addWidget(m_enableValidationRulesCheck);
    qaLayout->addWidget(m_enableQualityAssuranceCheck);

    tabLayout->addWidget(qaGroup);
    tabLayout->addStretch();
}

void AnalysisSettingsDialog::setupPerformanceTab()
{
    m_performanceTab = new QWidget;
    m_tabWidget->addTab(m_performanceTab, "Performance & Accuracy");

    QVBoxLayout *tabLayout = new QVBoxLayout(m_performanceTab);

    // Speed vs Accuracy Balance
    QGroupBox *balanceGroup = new QGroupBox("Speed vs Accuracy Balance");
    QVBoxLayout *balanceLayout = new QVBoxLayout(balanceGroup);

    QLabel *balanceLabel = new QLabel("Processing Priority:");
    balanceLayout->addWidget(balanceLabel);

    m_speedAccuracySlider = new QSlider(Qt::Horizontal);
    m_speedAccuracySlider->setRange(0, 100);
    m_speedAccuracySlider->setValue(50);
    m_speedAccuracySlider->setTickPosition(QSlider::TicksBelow);
    m_speedAccuracySlider->setTickInterval(25);
    addTooltip(m_speedAccuracySlider, "Balance between processing speed and accuracy. Left = faster processing, Right = higher accuracy.");

    QHBoxLayout *sliderLabels = new QHBoxLayout;
    sliderLabels->addWidget(new QLabel("Speed"));
    sliderLabels->addStretch();
    m_speedAccuracyLabel = new QLabel("Balanced");
    m_speedAccuracyLabel->setAlignment(Qt::AlignCenter);
    sliderLabels->addWidget(m_speedAccuracyLabel);
    sliderLabels->addStretch();
    sliderLabels->addWidget(new QLabel("Accuracy"));

    balanceLayout->addWidget(m_speedAccuracySlider);
    balanceLayout->addLayout(sliderLabels);

    tabLayout->addWidget(balanceGroup);

    // Advanced Processing Options
    QGroupBox *advancedGroup = new QGroupBox("Advanced Processing Options");
    QVBoxLayout *advancedLayout = new QVBoxLayout(advancedGroup);

    m_enableMultiPassCheck = new QCheckBox("Enable Multi-Pass Analysis");
    addTooltip(m_enableMultiPassCheck, "Perform multiple analysis passes for improved accuracy. Significantly increases processing time.");

    m_enableCrossValidationCheck = new QCheckBox("Enable Cross-Validation");
    addTooltip(m_enableCrossValidationCheck, "Use cross-validation to verify classification results. Provides higher confidence but increases processing time.");

    advancedLayout->addWidget(m_enableMultiPassCheck);
    advancedLayout->addWidget(m_enableCrossValidationCheck);

    tabLayout->addWidget(advancedGroup);

    // Batch Processing Settings
    QGroupBox *batchGroup = new QGroupBox("Batch Processing Settings");
    QFormLayout *batchLayout = new QFormLayout(batchGroup);

    m_batchChunkSizeSpin = new QSpinBox;
    m_batchChunkSizeSpin->setRange(1024, 65536);
    m_batchChunkSizeSpin->setSuffix(" samples");
    addTooltip(m_batchChunkSizeSpin, "Batch processing chunk size. Larger chunks use more memory but may be more efficient.");

    m_batchOverlapSpin = new QDoubleSpinBox;
    m_batchOverlapSpin->setRange(0.0, 0.5);
    m_batchOverlapSpin->setSingleStep(0.05);
    m_batchOverlapSpin->setDecimals(2);
    addTooltip(m_batchOverlapSpin, "Overlap ratio between batch chunks. Higher overlap provides better continuity but increases processing time.");

    batchLayout->addRow("Chunk Size:", m_batchChunkSizeSpin);
    batchLayout->addRow("Overlap Ratio:", m_batchOverlapSpin);

    tabLayout->addWidget(batchGroup);

    // Performance Settings
    QGroupBox *perfGroup = new QGroupBox("Performance Settings");
    QFormLayout *perfLayout = new QFormLayout(perfGroup);

    m_chunkSkipFactorSpin = new QSpinBox;
    m_chunkSkipFactorSpin->setRange(1, 10);
    addTooltip(m_chunkSkipFactorSpin, "Skip every N chunks for faster processing. Higher values increase speed but may reduce accuracy.");

    m_maxConcurrentSpin = new QSpinBox;
    m_maxConcurrentSpin->setRange(1, 16);
    addTooltip(m_maxConcurrentSpin, "Maximum number of concurrent analysis threads. More threads can speed up processing on multi-core systems.");

    m_enableFastModeCheck = new QCheckBox("Enable Fast Mode");
    addTooltip(m_enableFastModeCheck, "Enable fast processing mode with reduced accuracy for quick previews.");

    perfLayout->addRow("Chunk Skip Factor:", m_chunkSkipFactorSpin);
    perfLayout->addRow("Max Concurrent:", m_maxConcurrentSpin);
    perfLayout->addRow(m_enableFastModeCheck);

    tabLayout->addWidget(perfGroup);
    tabLayout->addStretch();
}

void AnalysisSettingsDialog::connectSignals()
{
    // Preset management
    connect(m_presetComboBox, QOverload<const QString &>::of(&QComboBox::currentTextChanged),
            this, &AnalysisSettingsDialog::onPresetChanged);
    connect(m_savePresetBtn, &QPushButton::clicked, this, &AnalysisSettingsDialog::onSavePreset);
    connect(m_deletePresetBtn, &QPushButton::clicked, this, &AnalysisSettingsDialog::onDeletePreset);
    connect(m_newPresetBtn, &QPushButton::clicked, this, &AnalysisSettingsDialog::onNewPreset);
    connect(m_duplicatePresetBtn, &QPushButton::clicked, this, &AnalysisSettingsDialog::onDuplicatePreset);

    // Dialog buttons
    connect(m_resetBtn, &QPushButton::clicked, this, &AnalysisSettingsDialog::onResetToDefaults);
    connect(m_applyBtn, &QPushButton::clicked, this, &AnalysisSettingsDialog::onApplySettings);
    connect(m_cancelBtn, &QPushButton::clicked, this, &QDialog::reject);
    connect(m_okBtn, &QPushButton::clicked, [this]() {
        onApplySettings();
        accept();
    });

    // Settings change tracking
    connect(m_speedAccuracySlider, &QSlider::valueChanged, [this](int value) {
        QString text;
        if (value < 25) text = "Speed Priority";
        else if (value < 75) text = "Balanced";
        else text = "Accuracy Priority";
        m_speedAccuracyLabel->setText(text);
        onSettingChanged();
    });

    // Connect all setting controls to change tracking
    // (This would include all the spin boxes, checkboxes, etc.)
    // For brevity, showing pattern for a few controls
    connect(m_sampleRateSpin, QOverload<int>::of(&QSpinBox::valueChanged), this, &AnalysisSettingsDialog::onSettingChanged);
    connect(m_confidenceThresholdSpin, QOverload<double>::of(&QDoubleSpinBox::valueChanged), this, &AnalysisSettingsDialog::onSettingChanged);
    connect(m_speakerDiarizationCheck, &QCheckBox::toggled, this, &AnalysisSettingsDialog::onSettingChanged);
}

void AnalysisSettingsDialog::addTooltip(QWidget *widget, const QString &tooltip)
{
    widget->setToolTip(tooltip);
    widget->setWhatsThis(tooltip);
}

void AnalysisSettingsDialog::loadCurrentSettings()
{
    if (!m_analysisSettings) return;

    // Audio Analysis Parameters
    m_sampleRateSpin->setValue(m_analysisSettings->sampleRate());
    m_bitDepthSpin->setValue(m_analysisSettings->bitDepth());
    m_mfccCoefficientsSpin->setValue(m_analysisSettings->mfccCoefficients());
    m_windowSizeSpin->setValue(m_analysisSettings->windowSize());
    m_hopLengthSpin->setValue(m_analysisSettings->hopLength());
    m_spectralOverlapSpin->setValue(m_analysisSettings->spectralOverlap());

    // Frequency Range Settings
    m_dialogueFreqMinSpin->setValue(m_analysisSettings->dialogueFreqMin());
    m_dialogueFreqMaxSpin->setValue(m_analysisSettings->dialogueFreqMax());
    m_musicFreqMinSpin->setValue(m_analysisSettings->musicFreqMin());
    m_musicFreqMaxSpin->setValue(m_analysisSettings->musicFreqMax());
    m_sfxFreqMinSpin->setValue(m_analysisSettings->sfxFreqMin());
    m_sfxFreqMaxSpin->setValue(m_analysisSettings->sfxFreqMax());

    // Noise and Silence Detection
    m_noiseFloorSpin->setValue(m_analysisSettings->noiseFloorThreshold());
    m_silenceThresholdSpin->setValue(m_analysisSettings->silenceThreshold());
    m_silenceMinDurationSpin->setValue(m_analysisSettings->silenceMinDuration());

    // Classification Model Settings
    m_modelSelectionCombo->setCurrentText(m_analysisSettings->modelSelection());
    m_confidenceThresholdSpin->setValue(m_analysisSettings->confidenceThreshold());
    m_speakerDiarizationCheck->setChecked(m_analysisSettings->enableSpeakerDiarization());
    m_musicDetectionCheck->setChecked(m_analysisSettings->enableMusicDetection());
    m_sfxDetectionCheck->setChecked(m_analysisSettings->enableSFXDetection());

    // Feature Extraction
    m_enableSpectralFeaturesCheck->setChecked(m_analysisSettings->enableSpectralFeatures());
    m_enableTemporalFeaturesCheck->setChecked(m_analysisSettings->enableTemporalFeatures());
    m_temporalSmoothingSpin->setValue(m_analysisSettings->temporalSmoothingWindow());
    m_contextWindowSpin->setValue(m_analysisSettings->contextWindowSize());

    // Processing Reliability
    m_errorHandlingCombo->setCurrentText(m_analysisSettings->errorHandlingStrategy());
    m_lowConfidenceBehaviorCombo->setCurrentText(m_analysisSettings->lowConfidenceBehavior());
    m_enableValidationRulesCheck->setChecked(m_analysisSettings->enableValidationRules());
    m_enableQualityAssuranceCheck->setChecked(m_analysisSettings->enableQualityAssurance());

    // Performance and Accuracy Trade-offs
    m_speedAccuracySlider->setValue(static_cast<int>(m_analysisSettings->speedAccuracyBalance() * 100));
    m_enableMultiPassCheck->setChecked(m_analysisSettings->enableMultiPassAnalysis());
    m_enableCrossValidationCheck->setChecked(m_analysisSettings->enableCrossValidation());
    m_batchChunkSizeSpin->setValue(m_analysisSettings->batchChunkSize());
    m_batchOverlapSpin->setValue(m_analysisSettings->batchOverlapRatio());
    m_chunkSkipFactorSpin->setValue(m_analysisSettings->chunkSkipFactor());
    m_maxConcurrentSpin->setValue(m_analysisSettings->maxConcurrentAnalysis());
    m_enableFastModeCheck->setChecked(m_analysisSettings->enableFastMode());

    m_settingsModified = false;
}

void AnalysisSettingsDialog::onPresetChanged(const QString &presetName)
{
    if (presetName.isEmpty() || !m_analysisSettings) return;

    if (m_analysisSettings->loadPreset(presetName)) {
        loadCurrentSettings();
        updatePresetDescription();
    }
}

void AnalysisSettingsDialog::onSavePreset()
{
    QString currentPreset = m_presetComboBox->currentText();
    if (currentPreset.isEmpty()) return;

    if (m_analysisSettings->isBuiltInPreset(currentPreset)) {
        QMessageBox::information(this, "Cannot Save",
            "Built-in presets cannot be modified. Use 'Duplicate' to create a custom version.");
        return;
    }

    applyCurrentSettings();
    if (m_analysisSettings->savePreset(currentPreset)) {
        QMessageBox::information(this, "Preset Saved",
            QString("Preset '%1' has been saved.").arg(currentPreset));
        m_settingsModified = false;
    }
}

void AnalysisSettingsDialog::onDeletePreset()
{
    QString currentPreset = m_presetComboBox->currentText();
    if (currentPreset.isEmpty()) return;

    if (m_analysisSettings->isBuiltInPreset(currentPreset)) {
        QMessageBox::information(this, "Cannot Delete",
            "Built-in presets cannot be deleted.");
        return;
    }

    int ret = QMessageBox::question(this, "Delete Preset",
        QString("Are you sure you want to delete preset '%1'?").arg(currentPreset),
        QMessageBox::Yes | QMessageBox::No);

    if (ret == QMessageBox::Yes) {
        if (m_analysisSettings->deletePreset(currentPreset)) {
            updatePresetsList();
            QMessageBox::information(this, "Preset Deleted",
                QString("Preset '%1' has been deleted.").arg(currentPreset));
        }
    }
}

void AnalysisSettingsDialog::onNewPreset()
{
    // Implementation for creating new preset
    // This would open a dialog to get preset name and description
}

void AnalysisSettingsDialog::onDuplicatePreset()
{
    // Implementation for duplicating current preset
    // This would open a dialog to get new preset name
}

void AnalysisSettingsDialog::onResetToDefaults()
{
    if (m_analysisSettings) {
        m_analysisSettings->resetToDefaults();
        loadCurrentSettings();
    }
}

void AnalysisSettingsDialog::onApplySettings()
{
    applyCurrentSettings();
    m_settingsModified = false;
}

void AnalysisSettingsDialog::onSettingChanged()
{
    m_settingsModified = true;
}

void AnalysisSettingsDialog::updatePresetsList()
{
    if (!m_analysisSettings) return;

    QString currentPreset = m_presetComboBox->currentText();
    m_presetComboBox->clear();
    m_presetComboBox->addItems(m_analysisSettings->getAvailablePresets());

    // Restore selection if possible
    int index = m_presetComboBox->findText(currentPreset);
    if (index >= 0) {
        m_presetComboBox->setCurrentIndex(index);
    }
}

void AnalysisSettingsDialog::updatePresetDescription()
{
    QString currentPreset = m_presetComboBox->currentText();
    if (!currentPreset.isEmpty() && m_analysisSettings) {
        QString description = m_analysisSettings->getPresetDescription(currentPreset);
        m_presetDescriptionEdit->setPlainText(description);
    }
}

void AnalysisSettingsDialog::applyCurrentSettings()
{
    if (!m_analysisSettings) return;

    // Apply all settings from UI to AnalysisSettings object
    m_analysisSettings->setSampleRate(m_sampleRateSpin->value());
    m_analysisSettings->setBitDepth(m_bitDepthSpin->value());
    m_analysisSettings->setMfccCoefficients(m_mfccCoefficientsSpin->value());
    m_analysisSettings->setWindowSize(m_windowSizeSpin->value());
    m_analysisSettings->setHopLength(m_hopLengthSpin->value());
    m_analysisSettings->setSpectralOverlap(m_spectralOverlapSpin->value());

    // Continue with all other settings...
    // (This would include all the other parameters)
}

QVariantMap AnalysisSettingsDialog::getCurrentSettings() const
{
    // Return current UI settings as variant map
    QVariantMap settings;
    // Implementation would collect all UI values
    return settings;
}

void AnalysisSettingsDialog::setCurrentSettings(const QVariantMap &settings)
{
    // Apply settings from variant map to UI
    // Implementation would set all UI values from the map
}
