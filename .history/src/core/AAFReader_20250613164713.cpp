#include "AAFReader.h"
#include "../python/PythonBridge.h"
#include "LibAAFWrapper.h"
#include "MemoryManager.h"
#include <QFileInfo>
#include <QDir>
#include <QTimer>
#include <QTime>

AAFReader::AAFReader(QObject *parent)
    : QObject(parent)
    , m_isLoaded(false)
    , m_duration(0.0)
    , m_frameRate(25.0)
    , m_timecodeFormat("25 NDF")
    , m_pythonBridge(nullptr)
    , m_libAAFWrapper(nullptr)
    , m_memoryManager(nullptr)
{
    qDebug() << "AAFReader initialized";
}

AAFReader::~AAFReader()
{
    closeFile();
}

void AAFReader::setPythonBridge(PythonBridge *pythonBridge)
{
    m_pythonBridge = pythonBridge;
    qDebug() << "Python bridge set for AAFReader (deprecated)";
}

void AAFReader::setLibAAFWrapper(LibAAFWrapper *libAAFWrapper)
{
    m_libAAFWrapper = libAAFWrapper;
    qDebug() << "LibAAF wrapper set for AAFReader";
}

void AAFReader::setMemoryManager(MemoryManager *manager)
{
    m_memoryManager = manager;
    qDebug() << "Memory manager set for AAFReader";
}

bool AAFReader::loadFile(const QString &filePath)
{
    qDebug() << "Loading AAF file:" << filePath;
    
    // Clear previous data
    clearData();
    
    // Validate file exists
    QFileInfo fileInfo(filePath);
    if (!fileInfo.exists()) {
        QString error = QString("File does not exist: %1").arg(filePath);
        qWarning() << error;
        emit this->error(error);
        emit fileLoadCompleted(false, error);
        return false;
    }
    
    // Validate file extension
    if (!getSupportedExtensions().contains(fileInfo.suffix().toLower())) {
        QString error = QString("Unsupported file format: %1").arg(fileInfo.suffix());
        qWarning() << error;
        emit this->error(error);
        emit fileLoadCompleted(false, error);
        return false;
    }
    
    // Set current file
    m_currentFile = fileInfo.absoluteFilePath();
    emit currentFileChanged();
    
    // Parse file
    emit parsingProgress(10, "Starting AAF file parsing...");
    
    if (!parseAAFFile(m_currentFile)) {
        QString error = "Failed to parse AAF file";
        qWarning() << error;
        emit this->error(error);
        emit fileLoadCompleted(false, error);
        return false;
    }
    
    emit parsingProgress(100, "AAF file loaded successfully");

    // Simulate memory allocation for AAF data processing
    if (m_memoryManager) {
        qint64 fileSize = QFileInfo(m_currentFile).size();
        qint64 estimatedMemory = qMax(fileSize / 10, qint64(50 * 1024 * 1024)); // At least 50MB
        QString chunkId = QString("aaf_data_%1").arg(QFileInfo(m_currentFile).baseName());
        m_memoryManager->allocateChunk(chunkId, estimatedMemory);
        qDebug() << "Allocated" << (estimatedMemory / (1024*1024)) << "MB for AAF data processing";
    }

    m_isLoaded = true;
    emit isLoadedChanged();
    emit fileLoadCompleted(true, "File loaded successfully");

    qDebug() << "AAF file loaded successfully:" << m_currentFile;
    return true;
}

bool AAFReader::loadFileFromUrl(const QUrl &fileUrl)
{
    if (fileUrl.isLocalFile()) {
        return loadFile(fileUrl.toLocalFile());
    }
    
    QString error = "Remote AAF files not yet supported";
    qWarning() << error;
    emit this->error(error);
    emit fileLoadCompleted(false, error);
    return false;
}

void AAFReader::closeFile()
{
    if (m_isLoaded) {
        qDebug() << "Closing AAF file:" << m_currentFile;
        clearData();
        m_isLoaded = false;
        emit isLoadedChanged();
    }
}

QVariantMap AAFReader::getTrackInfo(const QString &trackName)
{
    QVariantMap trackInfo;
    
    if (!m_isLoaded) {
        qWarning() << "No AAF file loaded";
        return trackInfo;
    }
    
    // For now, return mock track information
    trackInfo["name"] = trackName;
    trackInfo["type"] = "Audio";
    trackInfo["channels"] = 2;
    trackInfo["sampleRate"] = 48000;
    trackInfo["duration"] = 120.0; // seconds
    trackInfo["regionCount"] = 5;
    
    return trackInfo;
}

QVariantList AAFReader::getTrackRegions(const QString &trackName)
{
    QVariantList regions;
    
    if (!m_isLoaded) {
        qWarning() << "No AAF file loaded";
        return regions;
    }
    
    // Filter regions by track name
    for (const QVariant &regionVar : m_regions) {
        QVariantMap region = regionVar.toMap();
        if (region["track"].toString() == trackName) {
            regions.append(region);
        }
    }
    
    return regions;
}

QVariantList AAFReader::getAllRegions()
{
    if (!m_isLoaded) {
        qWarning() << "No AAF file loaded";
        return QVariantList();
    }
    
    return m_regions;
}

bool AAFReader::validateAAFFile(const QString &filePath)
{
    QFileInfo fileInfo(filePath);
    
    // Check file exists
    if (!fileInfo.exists()) {
        return false;
    }
    
    // Check file extension
    if (!getSupportedExtensions().contains(fileInfo.suffix().toLower())) {
        return false;
    }
    
    // Check file size (basic validation)
    if (fileInfo.size() < 1024) { // Less than 1KB is probably not a valid AAF
        return false;
    }
    
    // TODO: Add more sophisticated AAF format validation
    return true;
}

QStringList AAFReader::getSupportedExtensions()
{
    return QStringList() << "aaf" << "AAF";
}

bool AAFReader::parseAAFFile(const QString &filePath)
{
    emit parsingProgress(10, "Initializing AAF parser...");

    AAFFileInfo aafInfo;
    bool parseSuccess = false;

    // Try LibAAF first (preferred method)
    if (m_libAAFWrapper) {
        qDebug() << "AAFReader: LibAAF wrapper available, attempting to parse AAF file:" << filePath;
        emit parsingProgress(20, "Reading AAF file with LibAAF...");

        aafInfo = m_libAAFWrapper->parseAAFFile(filePath);
        parseSuccess = aafInfo.isValid;

        qDebug() << "AAFReader: LibAAF parseAAFFile returned - isValid:" << aafInfo.isValid
                 << "trackCount:" << aafInfo.trackCount << "regionCount:" << aafInfo.regionCount
                 << "duration:" << aafInfo.duration << "frameRate:" << aafInfo.frameRate;

        if (!parseSuccess) {
            QString error = m_libAAFWrapper->getLastError();
            qWarning() << "LibAAF parsing failed. Error:" << error;
        }
    }

    // Fallback to Python bridge if LibAAF failed or not available
    if (!parseSuccess && m_pythonBridge) {
        qDebug() << "AAFReader: Falling back to Python bridge for AAF parsing";
        emit parsingProgress(25, "Reading AAF file with pyaaf2...");

        aafInfo = m_pythonBridge->parseAAFFile(filePath);
        parseSuccess = aafInfo.isValid;

        qDebug() << "AAFReader: Python bridge parseAAFFile returned - isValid:" << aafInfo.isValid
                 << "trackCount:" << aafInfo.trackCount << "regionCount:" << aafInfo.regionCount
                 << "duration:" << aafInfo.duration << "frameRate:" << aafInfo.frameRate;

        if (!parseSuccess) {
            QString error = m_pythonBridge->getLastError();
            qWarning() << "Python bridge parsing also failed. Error:" << error;
        }
    }

    // Final fallback to mock data if both methods failed
    if (!parseSuccess) {
        qWarning() << "Both LibAAF and Python bridge parsing failed";

        // Check if mock data is disabled via environment variable or setting
        QByteArray disableMock = qgetenv("WAAFER_DISABLE_MOCK_DATA");
        if (disableMock == "1" || disableMock.toLower() == "true") {
            qWarning() << "Mock data disabled - AAF parsing failed and no fallback available";
            return false;
        }

        qWarning() << "Using mock data as fallback (set WAAFER_DISABLE_MOCK_DATA=1 to disable)";
        return createMockData();
    }

    emit parsingProgress(40, "Extracting track information...");

    // Set AAF file information
    m_duration = aafInfo.duration;
    m_frameRate = aafInfo.frameRate;
    m_timecodeFormat = aafInfo.timecodeFormat;

    emit durationChanged();
    emit frameRateChanged();
    emit timecodeFormatChanged();

    emit parsingProgress(60, "Processing tracks...");

    if (!extractTracks()) {
        return false;
    }

    emit parsingProgress(80, "Processing regions...");

    if (!extractRegions()) {
        return false;
    }

    emit parsingProgress(90, "Finalizing data structures...");

    // Set file info
    QFileInfo fileInfo(filePath);
    m_fileInfo["fileName"] = fileInfo.fileName();
    m_fileInfo["filePath"] = fileInfo.absoluteFilePath();
    m_fileInfo["fileSize"] = fileInfo.size();
    m_fileInfo["lastModified"] = fileInfo.lastModified();
    m_fileInfo["trackCount"] = aafInfo.trackCount;
    m_fileInfo["regionCount"] = aafInfo.regionCount;
    m_fileInfo["duration"] = m_duration;
    m_fileInfo["frameRate"] = m_frameRate;
    m_fileInfo["timecodeFormat"] = m_timecodeFormat;

    return true;
}

bool AAFReader::extractTracks()
{
    m_tracks.clear();
    QVariantList trackList;
    bool extractSuccess = false;

    // Try LibAAF first
    if (m_libAAFWrapper) {
        qDebug() << "AAFReader: Extracting tracks using LibAAF from file:" << m_currentFile;
        trackList = m_libAAFWrapper->getAAFTracks(m_currentFile);
        extractSuccess = !trackList.isEmpty();

        qDebug() << "AAFReader: LibAAF returned" << trackList.size() << "tracks";

        if (!extractSuccess) {
            QString error = m_libAAFWrapper->getLastError();
            if (!error.isEmpty()) {
                qWarning() << "AAFReader: LibAAF track extraction error:" << error;
            }
        }
    }

    // Fallback to Python bridge if LibAAF failed
    if (!extractSuccess && m_pythonBridge) {
        qDebug() << "AAFReader: Falling back to Python bridge for track extraction";
        trackList = m_pythonBridge->getAAFTracks(m_currentFile);
        extractSuccess = !trackList.isEmpty();

        qDebug() << "AAFReader: Python bridge returned" << trackList.size() << "tracks";

        if (!extractSuccess) {
            QString error = m_pythonBridge->getLastError();
            if (!error.isEmpty()) {
                qWarning() << "AAFReader: Python bridge track extraction error:" << error;
            }
        }
    }

    // Process extracted tracks
    if (extractSuccess) {
        for (const QVariant &trackVar : trackList) {
            QVariantMap track = trackVar.toMap();
            QString trackName = track.value("name").toString();
            if (trackName.isEmpty()) {
                trackName = QString("Track_%1").arg(m_tracks.size() + 1);
            }
            m_tracks.append(trackName);
            qDebug() << "AAFReader: Added track:" << trackName
                     << "number:" << track.value("number", 0).toInt()
                     << "clips:" << track.value("clipCount", 0).toInt();
        }
    }

    // If no tracks found or Python bridge not available, create default tracks
    if (m_tracks.isEmpty()) {
        qWarning() << "Using fallback track creation";

        // Check if mock data is disabled
        QByteArray disableMock = qgetenv("WAAFER_DISABLE_MOCK_DATA");
        if (disableMock == "1" || disableMock.toLower() == "true") {
            qDebug() << "Mock data disabled - not creating fallback tracks";
            // Don't create any tracks if mock data is disabled
        } else {
            int trackCount = m_fileInfo.value("trackCount", 5).toInt();
            if (trackCount == 0) trackCount = 5; // Default to 5 tracks

            for (int i = 0; i < trackCount; ++i) {
                m_tracks.append(QString("❌ MOCK_Track_%1").arg(i + 1));
            }
            qDebug() << "Created" << m_tracks.size() << "mock tracks";
        }
    }

    emit tracksChanged();
    qDebug() << "Extracted" << m_tracks.size() << "tracks";
    return true;
}

bool AAFReader::extractRegions()
{
    m_regions.clear();
    QVariantList regionList;
    bool extractSuccess = false;

    // Try LibAAF first
    if (m_libAAFWrapper) {
        qDebug() << "AAFReader: Extracting regions using LibAAF from file:" << m_currentFile;
        regionList = m_libAAFWrapper->getAAFRegions(m_currentFile);
        extractSuccess = !regionList.isEmpty();

        qDebug() << "AAFReader: LibAAF returned" << regionList.size() << "regions";

        if (!extractSuccess) {
            QString error = m_libAAFWrapper->getLastError();
            if (!error.isEmpty()) {
                qWarning() << "AAFReader: LibAAF region extraction error:" << error;
            }
        }
    }

    // Fallback to Python bridge if LibAAF failed
    if (!extractSuccess && m_pythonBridge) {
        qDebug() << "AAFReader: Falling back to Python bridge for region extraction";
        regionList = m_pythonBridge->getAAFRegions(m_currentFile);
        extractSuccess = !regionList.isEmpty();

        qDebug() << "AAFReader: Python bridge returned" << regionList.size() << "regions";

        if (!extractSuccess) {
            QString error = m_pythonBridge->getLastError();
            if (!error.isEmpty()) {
                qWarning() << "AAFReader: Python bridge region extraction error:" << error;
            }
        }
    }

    // Process extracted regions
    if (extractSuccess) {
        for (const QVariant &regionVar : regionList) {
            QVariantMap region = regionVar.toMap();

            // Ensure required fields are present
            if (!region.contains("id")) {
                region["id"] = QString("region_%1").arg(m_regions.size() + 1);
            }
            if (!region.contains("name") || region["name"].toString().isEmpty()) {
                region["name"] = QString("Region %1").arg(m_regions.size() + 1);
            }
            if (!region.contains("track") || region["track"].toString().isEmpty()) {
                region["track"] = m_tracks.isEmpty() ? "Track 1" : m_tracks[0];
            }

            // Add default classification fields if missing
            if (!region.contains("classification")) {
                region["classification"] = "Unclassified";
            }
            if (!region.contains("confidence")) {
                region["confidence"] = 0.0;
            }
            if (!region.contains("contentType")) {
                region["contentType"] = "Unknown";
            }

            // Map LibAAF fields to WAAFer expected fields
            if (region.contains("position") && !region.contains("startTime")) {
                region["startTime"] = region["position"];
            }
            if (region.contains("length") && !region.contains("duration")) {
                region["duration"] = region["length"];
            }
            if (region.contains("essenceFilePath") && !region.contains("audioFilePath")) {
                region["audioFilePath"] = region["essenceFilePath"];
            }

            m_regions.append(region);
            qDebug() << "AAFReader: Added region:" << region["name"].toString()
                     << "on track:" << region["trackName"].toString()
                     << "start:" << region["startTime"].toDouble()
                     << "duration:" << region["duration"].toDouble()
                     << "audioFile:" << region["audioFilePath"].toString();
        }
    }

    // If no regions found or Python bridge not available, create default regions
    if (m_regions.isEmpty()) {
        qWarning() << "Using fallback region creation";

        // Check if mock data is disabled
        QByteArray disableMock = qgetenv("WAAFER_DISABLE_MOCK_DATA");
        if (disableMock == "1" || disableMock.toLower() == "true") {
            qDebug() << "Mock data disabled - not creating fallback regions";
            // Don't create any regions if mock data is disabled
        } else {
            int regionCount = m_fileInfo.value("regionCount", 10).toInt();
            if (regionCount == 0) regionCount = 10; // Default to 10 regions

            QStringList regionTypes = {"Dialog", "Music", "SFX", "Ambience", "Unknown"};

            for (int i = 0; i < regionCount; ++i) {
                QVariantMap region;
                region["id"] = QString("❌ MOCK_region_%1").arg(i + 1);
                region["name"] = QString("❌ FAKE_Region_%1").arg(i + 1);
                region["track"] = m_tracks.isEmpty() ? "❌ MOCK_Track_1" : m_tracks[i % m_tracks.size()];
                region["startTime"] = (i * 8.5); // seconds
                region["duration"] = 5.0 + (i % 3) * 2.0; // 5-9 seconds
                region["type"] = regionTypes[i % regionTypes.size()];
                region["classification"] = "❌ MOCK_DATA";
                region["confidence"] = 0.0;
                region["contentType"] = "❌ FAKE_CONTENT";
                region["audioFilePath"] = QString("❌ fake_audio_%1.wav").arg(i + 1);

                m_regions.append(region);
            }
            qDebug() << "Created" << m_regions.size() << "mock regions";
        }
    }

    qDebug() << "Extracted" << m_regions.size() << "regions";
    return true;
}

void AAFReader::clearData()
{
    m_currentFile.clear();
    m_tracks.clear();
    m_regions.clear();
    m_fileInfo.clear();
    m_duration = 0.0;
    m_frameRate = 25.0;
    m_timecodeFormat = "25 NDF";

    emit currentFileChanged();
    emit tracksChanged();
    emit durationChanged();
    emit frameRateChanged();
    emit timecodeFormatChanged();
}

bool AAFReader::createMockData()
{
    // WARNING: This creates FAKE TEST DATA - not real AAF content!
    qWarning() << "=== CREATING MOCK DATA - NOT REAL AAF CONTENT ===";

    m_currentFile = "❌ MOCK_TEST_FILE.aaf";
    m_duration = 120.0; // 2 minutes
    m_frameRate = 25.0;
    m_timecodeFormat = "25 NDF";

    emit currentFileChanged();
    emit durationChanged();
    emit frameRateChanged();
    emit timecodeFormatChanged();

    if (!extractTracks() || !extractRegions()) {
        return false;
    }

    // Set mock file info with obvious fake markers
    m_fileInfo["fileName"] = "❌ MOCK_TEST_FILE.aaf";
    m_fileInfo["filePath"] = "❌ /fake/path/MOCK_TEST_FILE.aaf";
    m_fileInfo["fileSize"] = 1024000; // 1MB
    m_fileInfo["lastModified"] = QDateTime::currentDateTime();
    m_fileInfo["trackCount"] = m_tracks.size();
    m_fileInfo["regionCount"] = m_regions.size();
    m_fileInfo["duration"] = m_duration;
    m_fileInfo["frameRate"] = m_frameRate;
    m_fileInfo["timecodeFormat"] = m_timecodeFormat;

    m_isLoaded = true;
    emit isLoadedChanged();

    qWarning() << "=== MOCK DATA CREATED - " << m_regions.size() << "fake regions ===";
    return true;
}

QString AAFReader::formatTimecode(double seconds, const QString &format)
{
    if (format == "HH:MM:SS:FF") {
        // Frame-based timecode
        int totalFrames = static_cast<int>(seconds * m_frameRate);
        int frames = totalFrames % static_cast<int>(m_frameRate);
        int totalSeconds = totalFrames / static_cast<int>(m_frameRate);
        int secs = totalSeconds % 60;
        int mins = (totalSeconds / 60) % 60;
        int hours = totalSeconds / 3600;

        return QString("%1:%2:%3:%4")
               .arg(hours, 2, 10, QChar('0'))
               .arg(mins, 2, 10, QChar('0'))
               .arg(secs, 2, 10, QChar('0'))
               .arg(frames, 2, 10, QChar('0'));
    } else {
        // Millisecond-based timecode (HH:MM:SS.mmm)
        int totalMs = static_cast<int>(seconds * 1000);
        int ms = totalMs % 1000;
        int totalSeconds = totalMs / 1000;
        int secs = totalSeconds % 60;
        int mins = (totalSeconds / 60) % 60;
        int hours = totalSeconds / 3600;

        return QString("%1:%2:%3.%4")
               .arg(hours, 2, 10, QChar('0'))
               .arg(mins, 2, 10, QChar('0'))
               .arg(secs, 2, 10, QChar('0'))
               .arg(ms, 3, 10, QChar('0'));
    }
}
