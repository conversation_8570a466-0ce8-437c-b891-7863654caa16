#include "LibAAFWrapper.h"

// LibAAF includes
extern "C" {
#include <libaaf.h>
}

#include <QFileInfo>
#include <QDir>
#include <QStandardPaths>
#include <QDebug>
#include <QJsonDocument>
#include <QJsonObject>
#include <QJsonArray>
#include <QRegularExpression>
#include <cmath>

LibAAFWrapper::LibAAFWrapper(QObject *parent)
    : QObject(parent)
    , m_initialized(false)
    , m_aafi(nullptr)
    , m_proToolsOptimizations(true)
    , m_verbosityLevel(1) // Error level by default
{
}

LibAAFWrapper::~LibAAFWrapper()
{
    releaseAAFFile();
}

bool LibAAFWrapper::initialize()
{
    if (m_initialized) {
        return true;
    }

    qDebug() << "LibAAFWrapper: Initializing LibAAF...";

    // Allocate AAF interface
    m_aafi = aafi_alloc(nullptr);
    if (!m_aafi) {
        m_lastError = "Failed to allocate AAF interface";
        qWarning() << "LibAAFWrapper:" << m_lastError;
        return false;
    }

    // Set debug level and output
    aafi_set_debug(m_aafi, static_cast<verbosityLevel_e>(m_verbosityLevel), 1, stdout, nullptr, nullptr);

    // Set default options
    aafi_set_option_int(m_aafi, "trace", 0);
    aafi_set_option_int(m_aafi, "dump_meta", 0);
    aafi_set_option_int(m_aafi, "dump_tagged_value", 0);

    // Enable ProTools optimizations by default
    if (m_proToolsOptimizations) {
        aafi_set_option_int(m_aafi, "protools", 
            (AAFI_PROTOOLS_OPT_REPLACE_CLIP_FADES | AAFI_PROTOOLS_OPT_REMOVE_SAMPLE_ACCURATE_EDIT));
    }

    // Set media location if specified
    if (!m_mediaLocation.isEmpty()) {
        aafi_set_option_str(m_aafi, "media_location", m_mediaLocation.toUtf8().constData());
    }

    m_initialized = true;
    qDebug() << "LibAAFWrapper: Successfully initialized";
    return true;
}

bool LibAAFWrapper::isInitialized() const
{
    return m_initialized;
}

AAFFileInfo LibAAFWrapper::parseAAFFile(const QString &filePath)
{
    AAFFileInfo info;
    info.isValid = false;

    if (!m_initialized) {
        m_lastError = "LibAAF wrapper not initialized";
        info.errorMessage = m_lastError;
        return info;
    }

    if (!QFileInfo::exists(filePath)) {
        m_lastError = QString("AAF file does not exist: %1").arg(filePath);
        info.errorMessage = m_lastError;
        qWarning() << "LibAAFWrapper:" << m_lastError;
        return info;
    }

    emit parsingProgress(10, "Loading AAF file...");

    // Load AAF file
    if (!loadAAFFile(filePath)) {
        info.errorMessage = m_lastError;
        return info;
    }

    emit parsingProgress(50, "Extracting file information...");

    // Extract basic file information
    QFileInfo fileInfo(filePath);
    info.fileName = fileInfo.fileName();
    info.filePath = filePath;
    info.fileSize = fileInfo.size();

    // Get composition information
    if (m_aafi->compositionLength_editRate) {
        info.duration = convertEditUnitsToSeconds(m_aafi->compositionLength, m_aafi->compositionLength_editRate);
    } else {
        info.duration = 0.0;
    }

    // Get frame rate from timecode or audio
    if (m_aafi->Timecode && m_aafi->Timecode->fps > 0) {
        info.frameRate = static_cast<double>(m_aafi->Timecode->fps);
        info.timecodeFormat = QString("%1 %2").arg(info.frameRate).arg(m_aafi->Timecode->drop ? "DF" : "NDF");
    } else if (m_aafi->Audio && m_aafi->Audio->samplerateRational) {
        // Fallback to audio sample rate for frame rate calculation
        info.frameRate = 25.0; // Default
        info.timecodeFormat = "25 NDF";
    } else {
        info.frameRate = 25.0;
        info.timecodeFormat = "25 NDF";
    }

    // Count tracks and regions
    info.trackCount = 0;
    info.regionCount = 0;

    if (m_aafi->Audio) {
        info.trackCount = m_aafi->Audio->track_count;

        // Count regions across all tracks
        aafiAudioTrack *audioTrack = nullptr;
        AAFI_foreachAudioTrack(m_aafi, audioTrack) {
            info.regionCount += audioTrack->clipCount;
        }
    }

    info.isValid = true;
    emit parsingProgress(100, "AAF file parsed successfully");

    qDebug() << "LibAAFWrapper: Successfully parsed AAF file:"
             << "tracks:" << info.trackCount
             << "regions:" << info.regionCount
             << "duration:" << info.duration << "seconds";

    return info;
}

bool LibAAFWrapper::loadAAFFile(const QString &filePath)
{
    // Release any previously loaded file
    releaseAAFFile();

    // Load the AAF file
    int result = aafi_load_file(m_aafi, filePath.toUtf8().constData());
    if (result != 0) {
        m_lastError = QString("Failed to load AAF file: %1 (error code: %2)").arg(filePath).arg(result);
        qWarning() << "LibAAFWrapper:" << m_lastError;
        return false;
    }

    m_currentFilePath = filePath;
    return true;
}

void LibAAFWrapper::releaseAAFFile()
{
    if (m_aafi) {
        // LibAAF automatically cleans up when loading a new file or releasing
        m_currentFilePath.clear();
    }
}

QVariantList LibAAFWrapper::getAAFTracks(const QString &filePath)
{
    QVariantList tracks;

    if (!m_initialized) {
        m_lastError = "LibAAF wrapper not initialized";
        return tracks;
    }

    // Load file if different from current
    if (m_currentFilePath != filePath && !loadAAFFile(filePath)) {
        return tracks;
    }

    if (!m_aafi->Audio) {
        qDebug() << "LibAAFWrapper: No audio tracks found in AAF file";
        return tracks;
    }

    // Iterate through audio tracks
    aafiAudioTrack *audioTrack = nullptr;
    AAFI_foreachAudioTrack(m_aafi, audioTrack) {
        QVariantMap trackInfo = convertTrackToVariant(audioTrack);
        tracks.append(trackInfo);
    }

    qDebug() << "LibAAFWrapper: Found" << tracks.size() << "audio tracks";
    return tracks;
}

QVariantMap LibAAFWrapper::convertTrackToVariant(const aafiAudioTrack *track)
{
    QVariantMap trackInfo;

    if (!track) {
        return trackInfo;
    }

    trackInfo["number"] = static_cast<int>(track->number);
    trackInfo["name"] = track->name ? QString::fromUtf8(track->name) : QString("Track %1").arg(track->number);
    trackInfo["format"] = static_cast<int>(track->format);
    trackInfo["clipCount"] = track->clipCount;
    trackInfo["solo"] = static_cast<bool>(track->solo);
    trackInfo["mute"] = static_cast<bool>(track->mute);

    // Convert edit rate
    if (track->edit_rate) {
        trackInfo["editRateNum"] = track->edit_rate->numerator;
        trackInfo["editRateDen"] = track->edit_rate->denominator;
        trackInfo["editRate"] = static_cast<double>(track->edit_rate->numerator) / track->edit_rate->denominator;
    }

    // Add gain information if available
    if (track->gain) {
        QVariantMap gainInfo;
        gainInfo["flags"] = static_cast<int>(track->gain->flags);
        gainInfo["pointCount"] = static_cast<int>(track->gain->pts_cnt);
        if (track->gain->pts_cnt > 0 && track->gain->value) {
            gainInfo["value"] = static_cast<double>(track->gain->value[0].numerator) / track->gain->value[0].denominator;
        }
        trackInfo["gain"] = gainInfo;
    }

    return trackInfo;
}

double LibAAFWrapper::convertEditUnitsToSeconds(qint64 editUnits, const void *editRate)
{
    if (!editRate) {
        return 0.0;
    }

    const aafRational_t *rate = static_cast<const aafRational_t *>(editRate);
    if (rate->denominator == 0) {
        return 0.0;
    }

    return static_cast<double>(editUnits) * rate->denominator / rate->numerator;
}

QString LibAAFWrapper::formatTimecode(double seconds, double frameRate, bool dropFrame)
{
    if (seconds < 0 || frameRate <= 0) {
        return "00:00:00:00";
    }

    int totalFrames = static_cast<int>(seconds * frameRate);
    
    int frames = totalFrames % static_cast<int>(frameRate);
    int totalSeconds = totalFrames / static_cast<int>(frameRate);
    int minutes = totalSeconds / 60;
    int hours = minutes / 60;
    
    totalSeconds %= 60;
    minutes %= 60;

    QString separator = dropFrame ? ";" : ":";
    return QString("%1:%2:%3%4%5")
           .arg(hours, 2, 10, QChar('0'))
           .arg(minutes, 2, 10, QChar('0'))
           .arg(totalSeconds, 2, 10, QChar('0'))
           .arg(separator)
           .arg(frames, 2, 10, QChar('0'));
}

bool LibAAFWrapper::validateAAFFile(const QString &filePath)
{
    if (!QFileInfo::exists(filePath)) {
        return false;
    }

    QFileInfo fileInfo(filePath);
    return fileInfo.suffix().toLower() == "aaf";
}

QStringList LibAAFWrapper::getSupportedExtensions()
{
    return QStringList() << "aaf";
}

QString LibAAFWrapper::getLastError() const
{
    return m_lastError;
}

void LibAAFWrapper::setMediaLocation(const QString &mediaPath)
{
    m_mediaLocation = mediaPath;
    if (m_initialized && m_aafi) {
        aafi_set_option_str(m_aafi, "media_location", mediaPath.toUtf8().constData());
    }
}

void LibAAFWrapper::setProToolsOptimizations(bool enabled)
{
    m_proToolsOptimizations = enabled;
    if (m_initialized && m_aafi) {
        int options = enabled ? 
            (AAFI_PROTOOLS_OPT_REPLACE_CLIP_FADES | AAFI_PROTOOLS_OPT_REMOVE_SAMPLE_ACCURATE_EDIT) : 0;
        aafi_set_option_int(m_aafi, "protools", options);
    }
}

void LibAAFWrapper::setVerbosityLevel(int level)
{
    m_verbosityLevel = level;
    if (m_initialized && m_aafi) {
        aafi_set_debug(m_aafi, static_cast<verbosityLevel_e>(level), 1, stdout, nullptr, nullptr);
    }
}

QVariantList LibAAFWrapper::getAAFRegions(const QString &filePath)
{
    QVariantList regions;

    if (!m_initialized) {
        m_lastError = "LibAAF wrapper not initialized";
        return regions;
    }

    // Load file if different from current
    if (m_currentFilePath != filePath && !loadAAFFile(filePath)) {
        return regions;
    }

    if (!m_aafi->Audio) {
        qDebug() << "LibAAFWrapper: No audio data found in AAF file";
        return regions;
    }

    // Iterate through all tracks and their clips
    aafiAudioTrack *audioTrack = nullptr;
    AAFI_foreachAudioTrack(m_aafi, audioTrack) {
        aafiTimelineItem *timelineItem = nullptr;
        AAFI_foreachTrackItem(audioTrack, timelineItem) {
            if (timelineItem->type == AAFI_AUDIO_CLIP) {
                aafiAudioClip *audioClip = aafi_timelineItemToAudioClip(timelineItem);
                if (audioClip) {
                    QVariantMap regionInfo = convertClipToVariant(audioClip, audioTrack->edit_rate);
                    regionInfo["trackNumber"] = static_cast<int>(audioTrack->number);
                    regionInfo["trackName"] = audioTrack->name ? QString::fromUtf8(audioTrack->name) : QString("Track %1").arg(audioTrack->number);
                    regions.append(regionInfo);
                }
            }
        }
    }

    qDebug() << "LibAAFWrapper: Found" << regions.size() << "audio regions";
    return regions;
}

QVariantMap LibAAFWrapper::convertClipToVariant(const aafiAudioClip *clip, const void *trackEditRate)
{
    QVariantMap clipInfo;

    if (!clip) {
        return clipInfo;
    }

    // Basic clip information
    clipInfo["name"] = clip->subClipName ? QString::fromUtf8(clip->subClipName) : QString("Clip");
    clipInfo["channels"] = clip->channels;
    clipInfo["mute"] = static_cast<bool>(clip->mute);

    // Time information
    clipInfo["position"] = convertEditUnitsToSeconds(clip->pos, trackEditRate);
    clipInfo["length"] = convertEditUnitsToSeconds(clip->len, trackEditRate);
    clipInfo["essenceOffset"] = convertEditUnitsToSeconds(clip->essence_offset, trackEditRate);

    // Position and length in edit units
    clipInfo["positionEditUnits"] = static_cast<qint64>(clip->pos);
    clipInfo["lengthEditUnits"] = static_cast<qint64>(clip->len);
    clipInfo["essenceOffsetEditUnits"] = static_cast<qint64>(clip->essence_offset);

    // Essence file information
    if (clip->essencePointerList && clip->essencePointerList->essenceFile) {
        aafiAudioEssenceFile *essence = clip->essencePointerList->essenceFile;
        clipInfo["essenceFileName"] = essence->name ? QString::fromUtf8(essence->name) : QString("Unknown");
        clipInfo["essenceFilePath"] = essence->original_file_path ? QString::fromUtf8(essence->original_file_path) : QString();
        clipInfo["essenceUsableFilePath"] = essence->usable_file_path ? QString::fromUtf8(essence->usable_file_path) : QString();
        clipInfo["essenceIsEmbedded"] = static_cast<bool>(essence->is_embedded);
        clipInfo["essenceChannels"] = static_cast<int>(essence->channels);
        clipInfo["essenceSampleRate"] = static_cast<int>(essence->samplerate);
        clipInfo["essenceSampleSize"] = static_cast<int>(essence->samplesize);
        clipInfo["essenceLength"] = static_cast<qint64>(essence->length);
    }

    // Gain information
    if (clip->gain) {
        QVariantMap gainInfo;
        gainInfo["flags"] = static_cast<int>(clip->gain->flags);
        gainInfo["pointCount"] = static_cast<int>(clip->gain->pts_cnt);
        if (clip->gain->pts_cnt > 0 && clip->gain->value) {
            gainInfo["value"] = static_cast<double>(clip->gain->value[0].numerator) / clip->gain->value[0].denominator;
        }
        clipInfo["gain"] = gainInfo;
    }

    // Metadata
    if (clip->metadata) {
        clipInfo["metadata"] = convertMetadataToVariant(clip->metadata);
    }

    return clipInfo;
}

QVariantList LibAAFWrapper::getAAFEssences(const QString &filePath)
{
    QVariantList essences;

    if (!m_initialized) {
        m_lastError = "LibAAF wrapper not initialized";
        return essences;
    }

    // Load file if different from current
    if (m_currentFilePath != filePath && !loadAAFFile(filePath)) {
        return essences;
    }

    if (!m_aafi->Audio) {
        qDebug() << "LibAAFWrapper: No audio data found in AAF file";
        return essences;
    }

    // Iterate through audio essence files
    aafiAudioEssenceFile *essenceFile = nullptr;
    AAFI_foreachAudioEssenceFile(m_aafi, essenceFile) {
        QVariantMap essenceInfo = convertEssenceToVariant(essenceFile);
        essences.append(essenceInfo);
    }

    qDebug() << "LibAAFWrapper: Found" << essences.size() << "audio essence files";
    return essences;
}

QVariantMap LibAAFWrapper::convertEssenceToVariant(const aafiAudioEssenceFile *essence)
{
    QVariantMap essenceInfo;

    if (!essence) {
        return essenceInfo;
    }

    essenceInfo["name"] = essence->name ? QString::fromUtf8(essence->name) : QString("Unknown");
    essenceInfo["uniqueName"] = essence->unique_name ? QString::fromUtf8(essence->unique_name) : QString();
    essenceInfo["originalFilePath"] = essence->original_file_path ? QString::fromUtf8(essence->original_file_path) : QString();
    essenceInfo["usableFilePath"] = essence->usable_file_path ? QString::fromUtf8(essence->usable_file_path) : QString();
    essenceInfo["isEmbedded"] = static_cast<bool>(essence->is_embedded);
    essenceInfo["length"] = static_cast<qint64>(essence->length);
    essenceInfo["sampleRate"] = static_cast<int>(essence->samplerate);
    essenceInfo["sampleSize"] = static_cast<int>(essence->samplesize);
    essenceInfo["channels"] = static_cast<int>(essence->channels);
    essenceInfo["type"] = static_cast<int>(essence->type);

    // BWF metadata
    if (essence->description[0] != '\0') {
        essenceInfo["description"] = QString::fromUtf8(essence->description);
    }
    if (essence->originator[0] != '\0') {
        essenceInfo["originator"] = QString::fromUtf8(essence->originator);
    }
    if (essence->originatorReference[0] != '\0') {
        essenceInfo["originatorReference"] = QString::fromUtf8(essence->originatorReference);
    }
    if (essence->originationDate[0] != '\0') {
        essenceInfo["originationDate"] = QString::fromUtf8(essence->originationDate);
    }
    if (essence->originationTime[0] != '\0') {
        essenceInfo["originationTime"] = QString::fromUtf8(essence->originationTime);
    }

    essenceInfo["timeReference"] = static_cast<qint64>(essence->timeReference);

    // Metadata
    if (essence->metadata) {
        essenceInfo["metadata"] = convertMetadataToVariant(essence->metadata);
    }

    return essenceInfo;
}

QVariantList LibAAFWrapper::getAAFMarkers(const QString &filePath)
{
    QVariantList markers;

    if (!m_initialized) {
        m_lastError = "LibAAF wrapper not initialized";
        return markers;
    }

    // Load file if different from current
    if (m_currentFilePath != filePath && !loadAAFFile(filePath)) {
        return markers;
    }

    if (!m_aafi->Markers) {
        qDebug() << "LibAAFWrapper: No markers found in AAF file";
        return markers;
    }

    // Iterate through markers
    aafiMarker *marker = nullptr;
    AAFI_foreachMarker(m_aafi, marker) {
        QVariantMap markerInfo = convertMarkerToVariant(marker);
        markers.append(markerInfo);
    }

    qDebug() << "LibAAFWrapper: Found" << markers.size() << "markers";
    return markers;
}

QVariantMap LibAAFWrapper::convertMarkerToVariant(const aafiMarker *marker)
{
    QVariantMap markerInfo;

    if (!marker) {
        return markerInfo;
    }

    markerInfo["start"] = convertEditUnitsToSeconds(marker->start, marker->edit_rate);
    markerInfo["length"] = convertEditUnitsToSeconds(marker->length, marker->edit_rate);
    markerInfo["startEditUnits"] = static_cast<qint64>(marker->start);
    markerInfo["lengthEditUnits"] = static_cast<qint64>(marker->length);
    markerInfo["name"] = marker->name ? QString::fromUtf8(marker->name) : QString("Marker");
    markerInfo["comment"] = marker->comment ? QString::fromUtf8(marker->comment) : QString();

    // Color information (RGBColor is an array, so it's always non-null)
    QVariantList color;
    color << static_cast<int>(marker->RGBColor[0]);
    color << static_cast<int>(marker->RGBColor[1]);
    color << static_cast<int>(marker->RGBColor[2]);
    markerInfo["color"] = color;

    return markerInfo;
}

QVariantMap LibAAFWrapper::getAAFMetadata(const QString &filePath)
{
    QVariantMap metadata;

    if (!m_initialized) {
        m_lastError = "LibAAF wrapper not initialized";
        return metadata;
    }

    // Load file if different from current
    if (m_currentFilePath != filePath && !loadAAFFile(filePath)) {
        return metadata;
    }

    // Get composition metadata
    if (m_aafi->metadata) {
        metadata = convertMetadataToVariant(m_aafi->metadata);
    }

    // Add composition information
    if (m_aafi->compositionName) {
        metadata["compositionName"] = QString::fromUtf8(m_aafi->compositionName);
    }

    metadata["compositionStart"] = static_cast<qint64>(m_aafi->compositionStart);
    metadata["compositionLength"] = static_cast<qint64>(m_aafi->compositionLength);

    // Add timecode information
    if (m_aafi->Timecode) {
        QVariantMap timecodeInfo;
        timecodeInfo["start"] = static_cast<qint64>(m_aafi->Timecode->start);
        timecodeInfo["fps"] = static_cast<int>(m_aafi->Timecode->fps);
        timecodeInfo["drop"] = static_cast<bool>(m_aafi->Timecode->drop);
        metadata["timecode"] = timecodeInfo;
    }

    // Add audio information
    if (m_aafi->Audio) {
        QVariantMap audioInfo;
        audioInfo["start"] = static_cast<qint64>(m_aafi->Audio->start);
        audioInfo["sampleRate"] = static_cast<int>(m_aafi->Audio->samplerate);
        audioInfo["sampleSize"] = static_cast<int>(m_aafi->Audio->samplesize);
        audioInfo["trackCount"] = static_cast<int>(m_aafi->Audio->track_count);
        audioInfo["essenceCount"] = static_cast<int>(m_aafi->Audio->essenceCount);
        metadata["audio"] = audioInfo;
    }

    qDebug() << "LibAAFWrapper: Extracted metadata with" << metadata.size() << "entries";
    return metadata;
}

QVariantMap LibAAFWrapper::convertMetadataToVariant(const aafiMetaData *metadata)
{
    QVariantMap metadataMap;

    if (!metadata) {
        return metadataMap;
    }

    const aafiMetaData *current = metadata;
    AAFI_foreachMetadata(metadata, current) {
        if (current->name && current->text) {
            QString key = QString::fromUtf8(current->name);
            QString value = QString::fromUtf8(current->text);
            metadataMap[key] = value;
        }
    }

    return metadataMap;
}

bool LibAAFWrapper::exportAAFFile(const QString &outputPath,
                                 const QVariantList &regions,
                                 const QVariantList &tracks,
                                 const QVariantMap &metadata)
{
    if (!m_initialized) {
        m_lastError = "LibAAF wrapper not initialized";
        qWarning() << "LibAAFWrapper:" << m_lastError;
        return false;
    }

    qDebug() << "LibAAFWrapper: Starting AAF export to:" << outputPath;
    qDebug() << "LibAAFWrapper: Export data - regions:" << regions.size()
             << "tracks:" << tracks.size();

    // Since LibAAF doesn't support writing, we'll use pyaaf2 but with
    // LibAAF-compatible data structures and error handling
    return createAAFExportWithPyAAF2(outputPath, regions, tracks, metadata);
}

AAF_Iface* LibAAFWrapper::getAAFInterface() const
{
    return m_aafi;
}

bool LibAAFWrapper::createAAFExportWithPyAAF2(const QString &outputPath,
                                              const QVariantList &regions,
                                              const QVariantList &tracks,
                                              const QVariantMap &metadata)
{
    qDebug() << "LibAAFWrapper: Creating AAF export using pyaaf2 with LibAAF-compatible data";

    // Store the export script for execution by AAFExporter
    m_lastExportScript = generateLibAAFCompatibleExportScript(outputPath, regions, tracks, metadata);

    // Return success - the actual execution will be done by AAFExporter
    return true;
}

QString LibAAFWrapper::generateLibAAFCompatibleExportScript(const QString &outputPath,
                                                           const QVariantList &regions,
                                                           const QVariantList &tracks,
                                                           const QVariantMap &metadata)
{
    // Convert QVariant data to Python-compatible JSON (replace null with None)
    QString regionsJson = convertQVariantToPythonJson(regions);
    QString tracksJson = convertQVariantToPythonJson(tracks);
    QString metadataJson = convertQVariantToPythonJson(metadata);

    QString script = QString(
        "try:\n"
        "    import aaf2\n"
        "    import json\n"
        "    from pathlib import Path\n"
        "except ImportError as e:\n"
        "    print(f'EXPORT_ERROR: Missing required library: {str(e)}')\n"
        "    print('EXPORT_ERROR: Please install: pip install pyaaf2')\n"
        "    exit(1)\n"
        "\n"
        "def export_aaf_file_libaaf_compatible(output_file, regions_data, tracks_data, metadata):\n"
        "    try:\n"
        "        import uuid\n"
        "        output_path = Path(output_file)\n"
        "        output_path.parent.mkdir(parents=True, exist_ok=True)\n"
        "        comp_name = metadata.get('compositionName', 'WAAFer Export')\n"
        "        frame_rate = float(metadata.get('frameRate', 25.0))\n"
        "        sample_rate = int(metadata.get('sampleRate', 48000))\n"
        "        print(f'Creating AAF file: {output_file}')\n"
        "        print(f'Composition: {comp_name}, Frame rate: {frame_rate}, Sample rate: {sample_rate}')\n"
        "        print(f'Regions: {len(regions_data)}')\n"
        "        \n"
        "        with aaf2.open(output_file, 'w') as f:\n"
        "            # Create composition mob\n"
        "            comp_mob = f.create.CompositionMob(comp_name)\n"
        "            f.content.mobs.append(comp_mob)\n"
        "            \n"
        "            # Group regions by track for proper track organization\n"
        "            tracks_dict = {}\n"
        "            for region in regions_data:\n"
        "                track_name = region.get('track_name', 'Main Audio Track')\n"
        "                if track_name not in tracks_dict:\n"
        "                    tracks_dict[track_name] = []\n"
        "                tracks_dict[track_name].append(region)\n"
        "            \n"
        "            # Create source mobs and essence descriptors for each unique audio file\n"
        "            source_mobs = {}\n"
        "            audio_files = set()\n"
        "            \n"
        "            for region in regions_data:\n"
        "                audio_file = region.get('audio_file_path', '')\n"
        "                if audio_file and audio_file not in audio_files:\n"
        "                    audio_files.add(audio_file)\n"
        "                    \n"
        "                    # Create source mob for this audio file\n"
        "                    source_mob = f.create.SourceMob()\n"
        "                    source_mob.name = Path(audio_file).stem\n"
        "                    f.content.mobs.append(source_mob)\n"
        "                    \n"
        "                    # Create timeline slot for source mob\n"
        "                    source_slot = f.create.TimelineMobSlot(edit_rate=sample_rate)\n"
        "                    source_slot.slot_id = 1\n"
        "                    source_slot.name = 'Audio'\n"
        "                    source_mob.slots.append(source_slot)\n"
        "                    \n"
        "                    # Create source clip for the entire audio file\n"
        "                    source_clip = f.create.SourceClip(media_kind='sound')\n"
        "                    # Use a large length to represent the full audio file\n"
        "                    source_clip.length = sample_rate * 3600  # 1 hour max\n"
        "                    source_slot.segment = source_clip\n"
        "                    \n"
        "                    # Create essence descriptor\n"
        "                    essence_desc = f.create.PCMDescriptor()\n"
        "                    essence_desc.sample_rate = sample_rate\n"
        "                    essence_desc.channels = 2  # Stereo by default\n"
        "                    essence_desc.bits_per_sample = 16\n"
        "                    essence_desc.block_align = 4  # 2 channels * 2 bytes\n"
        "                    essence_desc.avg_bps = sample_rate * 4  # sample_rate * block_align\n"
        "                    \n"
        "                    # Create file descriptor and link to audio file\n"
        "                    file_desc = f.create.NetworkLocator()\n"
        "                    file_desc.url_string = f'file://{audio_file}'\n"
        "                    essence_desc.locators.append(file_desc)\n"
        "                    \n"
        "                    source_mob.essence_descriptor = essence_desc\n"
        "                    source_mobs[audio_file] = source_mob\n"
        "                    \n"
        "                    print(f'Created source mob for: {audio_file}')\n"
        "            \n"
        "            # Create timeline slots for each track\n"
        "            slot_id = 1\n"
        "            for track_name, track_regions in tracks_dict.items():\n"
        "                print(f'Creating track: {track_name} with {len(track_regions)} regions')\n"
        "                \n"
        "                timeline_slot = f.create.TimelineMobSlot(edit_rate=sample_rate)\n"
        "                timeline_slot.slot_id = slot_id\n"
        "                timeline_slot.name = track_name\n"
        "                comp_mob.slots.append(timeline_slot)\n"
        "                \n"
        "                sequence = f.create.Sequence(media_kind='sound')\n"
        "                timeline_slot.segment = sequence\n"
        "                \n"
        "                # Sort regions by start time\n"
        "                sorted_regions = sorted(track_regions, key=lambda r: float(r.get('start_time', 0)))\n"
        "                \n"
        "                current_position = 0\n"
        "                for region in sorted_regions:\n"
        "                    region_name = region.get('name', f'Region_{len(sequence.components)+1}')\n"
        "                    classification = region.get('content_type', 'Unknown')\n"
        "                    start_time = float(region.get('start_time', 0))\n"
        "                    duration = float(region.get('duration', 1.0))\n"
        "                    audio_file = region.get('audio_file_path', '')\n"
        "                    \n"
        "                    # Convert time to edit units (samples)\n"
        "                    start_samples = int(start_time * sample_rate)\n"
        "                    length_samples = int(duration * sample_rate)\n"
        "                    \n"
        "                    # Add filler if there's a gap\n"
        "                    if start_samples > current_position:\n"
        "                        gap_length = start_samples - current_position\n"
        "                        filler = f.create.Filler(media_kind='sound')\n"
        "                        filler.length = gap_length\n"
        "                        sequence.components.append(filler)\n"
        "                        current_position = start_samples\n"
        "                    \n"
        "                    # Create source clip with proper source mob reference\n"
        "                    source_clip = f.create.SourceClip(media_kind='sound')\n"
        "                    source_clip.length = length_samples\n"
        "                    \n"
        "                    # Link to source mob if audio file exists\n"
        "                    if audio_file in source_mobs:\n"
        "                        source_clip.source_mob = source_mobs[audio_file]\n"
        "                        source_clip.source_slot_id = 1\n"
        "                        # Set start time within the source file\n"
        "                        source_start = region.get('source_start_time', 0)\n"
        "                        source_clip.start_time = int(float(source_start) * sample_rate)\n"
        "                    \n"
        "                    sequence.components.append(source_clip)\n"
        "                    current_position += length_samples\n"
        "                    \n"
        "                    print(f'Added region: {region_name} ({classification}) - {duration:.2f}s')\n"
        "                \n"
        "                # Set sequence length\n"
        "                sequence.length = current_position\n"
        "                slot_id += 1\n"
        "            \n"
        "            print(f'Created AAF with {len(source_mobs)} source mobs and {len(tracks_dict)} tracks')\n"
        "        \n"
        "        print(f'AAF export completed successfully: {output_file}')\n"
        "        return True, f'AAF export completed: {output_file}'\n"
        "    except Exception as e:\n"
        "        import traceback\n"
        "        error_msg = f'AAF export failed: {str(e)}\\n{traceback.format_exc()}'\n"
        "        print(f'ERROR: {error_msg}')\n"
        "        return False, error_msg\n"
        "\n"
        "output_file = r'%1'\n"
        "regions_data = %2\n"
        "tracks_data = %3\n"
        "metadata = %4\n"
        "success, message = export_aaf_file_libaaf_compatible(output_file, regions_data, tracks_data, metadata)\n"
        "print('EXPORT_RESULT:', success, '|', message)\n"
    ).arg(outputPath)
     .arg(regionsJson)
     .arg(tracksJson)
     .arg(metadataJson);

    return script;
}

QString LibAAFWrapper::convertQVariantToPythonJson(const QVariant &variant)
{
    // Convert QVariant to JSON, then replace null with None for Python compatibility
    QString jsonString = QString::fromUtf8(QJsonDocument::fromVariant(variant).toJson(QJsonDocument::Compact));

    // Replace JSON null with Python None
    jsonString.replace(QRegularExpression("\\bnull\\b"), "None");

    // Replace JSON true/false with Python True/False
    jsonString.replace(QRegularExpression("\\btrue\\b"), "True");
    jsonString.replace(QRegularExpression("\\bfalse\\b"), "False");

    return jsonString;
}

QString LibAAFWrapper::getLastExportScript() const
{
    return m_lastExportScript;
}
