#ifndef AAFREADER_H
#define AAFREADER_H

#include <QObject>
#include <QString>
#include <QStringList>
#include <QVariantMap>
#include <QUrl>
#include <QDebug>

// Forward declarations
class PythonBridge;
class LibAAFWrapper;
class MemoryManager;

/**
 * @brief AAF file reader and parser
 * 
 * This class provides functionality to read and parse AAF (Advanced Authoring Format)
 * files, extracting track and region information for audio organization.
 */
class AAFReader : public QObject
{
    Q_OBJECT
    Q_PROPERTY(QString currentFile READ currentFile NOTIFY currentFileChanged)
    Q_PROPERTY(bool isLoaded READ isLoaded NOTIFY isLoadedChanged)
    Q_PROPERTY(QStringList tracks READ tracks NOTIFY tracksChanged)
    Q_PROPERTY(double duration READ duration NOTIFY durationChanged)
    Q_PROPERTY(double frameRate READ frameRate NOTIFY frameRateChanged)
    Q_PROPERTY(QString timecodeFormat READ timecodeFormat NOTIFY timecodeFormatChanged)
    
public:
    explicit AAFReader(QObject *parent = nullptr);
    ~AAFReader();

    /**
     * @brief Set Python bridge for AAF parsing (deprecated - kept for compatibility)
     * @param pythonBridge Python bridge instance
     */
    void setPythonBridge(PythonBridge *pythonBridge);

    /**
     * @brief Set LibAAF wrapper for AAF parsing
     * @param libAAFWrapper Pointer to LibAAF wrapper instance
     */
    void setLibAAFWrapper(LibAAFWrapper *libAAFWrapper);

    /**
     * @brief Set memory manager for tracking memory usage
     * @param manager Memory manager instance
     */
    void setMemoryManager(MemoryManager *manager);
    
    /**
     * @brief Get currently loaded file path
     * @return Current file path
     */
    QString currentFile() const { return m_currentFile; }
    
    /**
     * @brief Check if AAF file is loaded
     * @return true if file is loaded
     */
    bool isLoaded() const { return m_isLoaded; }
    
    /**
     * @brief Get list of track names
     * @return List of track names
     */
    QStringList tracks() const { return m_tracks; }

    /**
     * @brief Get sequence duration in seconds
     * @return Duration in seconds
     */
    double duration() const { return m_duration; }

    /**
     * @brief Get frame rate
     * @return Frame rate (fps)
     */
    double frameRate() const { return m_frameRate; }

    /**
     * @brief Get timecode format string
     * @return Timecode format (e.g., "29.97 DF", "25 NDF")
     */
    QString timecodeFormat() const { return m_timecodeFormat; }
    
    /**
     * @brief Get file information
     * @return Map containing file metadata
     */
    QVariantMap fileInfo() const { return m_fileInfo; }
    
public slots:
    /**
     * @brief Load AAF file
     * @param filePath Path to AAF file
     * @return true if successful
     */
    bool loadFile(const QString &filePath);
    
    /**
     * @brief Load AAF file from URL
     * @param fileUrl URL to AAF file
     * @return true if successful
     */
    bool loadFileFromUrl(const QUrl &fileUrl);
    
    /**
     * @brief Close current file
     */
    void closeFile();
    
    /**
     * @brief Get track information
     * @param trackName Name of track
     * @return Track information map
     */
    QVariantMap getTrackInfo(const QString &trackName);
    
    /**
     * @brief Get regions for a track
     * @param trackName Name of track
     * @return List of region information maps
     */
    QVariantList getTrackRegions(const QString &trackName);
    
    /**
     * @brief Get all regions from file
     * @return List of all region information maps
     */
    QVariantList getAllRegions();

    /**
     * @brief Get audio file path for a specific region
     * @param regionId ID of the region
     * @return Audio file path or empty string if not found
     */
    QString getAudioFileForRegion(const QString &regionId);

    /**
     * @brief Validate AAF file format
     * @param filePath Path to file
     * @return true if valid AAF file
     */
    bool validateAAFFile(const QString &filePath);
    
    /**
     * @brief Get supported file extensions
     * @return List of supported extensions
     */
    QStringList getSupportedExtensions();

    /**
     * @brief Format duration as timecode string
     * @param seconds Duration in seconds
     * @param format Timecode format ("HH:MM:SS:FF" or "HH:MM:SS.mmm")
     * @return Formatted timecode string
     */
    QString formatTimecode(double seconds, const QString &format = "HH:MM:SS.mmm");
    
signals:
    /**
     * @brief Emitted when current file changes
     */
    void currentFileChanged();
    
    /**
     * @brief Emitted when loaded state changes
     */
    void isLoadedChanged();
    
    /**
     * @brief Emitted when tracks list changes
     */
    void tracksChanged();

    /**
     * @brief Emitted when duration changes
     */
    void durationChanged();

    /**
     * @brief Emitted when frame rate changes
     */
    void frameRateChanged();

    /**
     * @brief Emitted when timecode format changes
     */
    void timecodeFormatChanged();
    
    /**
     * @brief Emitted when file loading completes
     * @param success Whether loading was successful
     * @param message Success or error message
     */
    void fileLoadCompleted(bool success, const QString &message);
    
    /**
     * @brief Emitted when file parsing progress updates
     * @param progress Progress percentage (0-100)
     * @param status Current status message
     */
    void parsingProgress(int progress, const QString &status);
    
    /**
     * @brief Emitted when error occurs
     * @param error Error message
     */
    void error(const QString &error);
    
private:
    QString m_currentFile;
    bool m_isLoaded;
    QStringList m_tracks;
    QVariantMap m_fileInfo;
    QVariantList m_regions;
    double m_duration;
    double m_frameRate;
    QString m_timecodeFormat;
    PythonBridge *m_pythonBridge;
    LibAAFWrapper *m_libAAFWrapper;
    MemoryManager *m_memoryManager;
    
    /**
     * @brief Parse AAF file structure
     * @param filePath Path to AAF file
     * @return true if successful
     */
    bool parseAAFFile(const QString &filePath);
    
    /**
     * @brief Extract track information
     * @return true if successful
     */
    bool extractTracks();
    
    /**
     * @brief Extract region information
     * @return true if successful
     */
    bool extractRegions();
    
    /**
     * @brief Clear all loaded data
     */
    void clearData();
    
    /**
     * @brief Create mock data for testing
     * @return true if successful
     */
    bool createMockData();
};

#endif // AAFREADER_H
