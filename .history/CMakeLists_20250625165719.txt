cmake_minimum_required(VERSION 3.20)
project(WAAFer VERSION 1.0.0 LANGUAGES CXX C)

# Set C++ standard
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# Set C standard for LibAAF
set(CMAKE_C_STANDARD 99)
set(CMAKE_C_STANDARD_REQUIRED ON)

# Force arm64 architecture on Apple Silicon
if(APPLE)
    set(CMAKE_OSX_ARCHITECTURES "arm64")
    set(CMAKE_OSX_DEPLOYMENT_TARGET "11.0")
    set(CMAKE_MACOSX_RPATH ON)
    set(CMAKE_INSTALL_RPATH_USE_LINK_PATH ON)

    # Use arm64 Homebrew paths
    set(CMAKE_PREFIX_PATH "/opt/homebrew" ${CMAKE_PREFIX_PATH})
    set(Qt6_DIR "/opt/homebrew/lib/cmake/Qt6")
endif()

# Find required packages
find_package(Qt6 REQUIRED COMPONENTS Core Widgets Qml Quick Network Multimedia)

# Use arm64 Python from Homebrew
if(APPLE)
    set(Python3_EXECUTABLE "/opt/homebrew/bin/python3")
    set(Python3_INCLUDE_DIR "/opt/homebrew/Frameworks/Python.framework/Versions/3.13/include/python3.13")
    set(Python3_LIBRARY "/opt/homebrew/Frameworks/Python.framework/Versions/3.13/lib/libpython3.13.dylib")
endif()
find_package(Python3 REQUIRED COMPONENTS Interpreter Development)

# Enable Qt MOC, UIC, and RCC
set(CMAKE_AUTOMOC ON)
set(CMAKE_AUTOUIC ON)
set(CMAKE_AUTORCC ON)

# Add LibAAF subdirectory
set(BUILD_STATIC_LIB ON CACHE BOOL "Build LibAAF static library" FORCE)
set(BUILD_SHARED_LIB OFF CACHE BOOL "Don't build LibAAF shared library" FORCE)
set(BUILD_TOOLS OFF CACHE BOOL "Don't build LibAAF tools" FORCE)
set(BUILD_DOC OFF CACHE BOOL "Don't build LibAAF documentation" FORCE)
set(BUILD_UNIT_TEST OFF CACHE BOOL "Don't build LibAAF unit tests" FORCE)
add_subdirectory(third-party/LibAAF)

# Include directories
include_directories(${CMAKE_SOURCE_DIR}/src)
include_directories(${CMAKE_SOURCE_DIR}/third-party/LibAAF/include)
include_directories(${Python3_INCLUDE_DIRS})

# Source files
set(SOURCES
    src/main.cpp
    src/core/AAFReader.cpp
    src/core/LibAAFWrapper.cpp
    src/core/MemoryManager.cpp
    src/core/ChunkedFileReader.cpp
    src/core/ProgressTracker.cpp
    src/core/AnalysisSettings.cpp
    src/core/TrackOrganizer.cpp
    src/python/PythonBridge.cpp
    src/python/PythonInterpreter.cpp
    src/audio/AudioAnalyzer.cpp
    src/audio/AudioFileManager.cpp
    src/audio/ClassificationEngine.cpp
    src/audio/AudioPlaybackManager.cpp
    src/audio/WebRTCVAD.cpp
    src/ai/LMStudioClient.cpp
    src/export/AAFExporter.cpp
    src/ui/MainWindow.cpp
    src/ui/TimelineWidget.cpp
    src/ui/ClassificationReviewDialog.cpp
    src/ui/AnalysisSettingsDialog.cpp
    src/utils/TimecodeUtils.cpp
    src/ui/PresetManagementDialog.cpp
)

set(HEADERS
    src/core/AAFReader.h
    src/core/AAFTypes.h
    src/core/LibAAFWrapper.h
    src/core/MemoryManager.h
    src/core/ChunkedFileReader.h
    src/core/ProgressTracker.h
    src/core/AnalysisSettings.h
    src/core/TrackOrganizer.h
    src/python/PythonBridge.h
    src/python/PythonInterpreter.h
    src/audio/AudioAnalyzer.h
    src/audio/AudioFileManager.h
    src/audio/ClassificationEngine.h
    src/audio/AudioPlaybackManager.h
    src/audio/WebRTCVAD.h
    src/ai/LMStudioClient.h
    src/export/AAFExporter.h
    src/ui/MainWindow.h
    src/ui/TimelineWidget.h
    src/ui/ClassificationReviewDialog.h
    src/ui/AnalysisSettingsDialog.h
    src/utils/TimecodeUtils.h
    src/ui/PresetManagementDialog.h
)

# QML resources
qt6_add_resources(QML_RESOURCES qml/qml.qrc)

# Create executable
qt6_add_executable(WAAFer ${SOURCES} ${HEADERS} ${QML_RESOURCES})

# Link libraries
target_link_libraries(WAAFer PRIVATE
    Qt6::Core
    Qt6::Widgets
    Qt6::Qml
    Qt6::Quick
    Qt6::Network
    Qt6::Multimedia
    ${Python3_LIBRARIES}
    aaf-static
)

# macOS specific linking
if(APPLE)
    target_link_libraries(WAAFer PRIVATE
        "-framework Foundation"
        "-framework CoreFoundation"
    )
endif()

# Set target properties
set_target_properties(WAAFer PROPERTIES
    MACOSX_BUNDLE TRUE
    MACOSX_BUNDLE_INFO_PLIST ${CMAKE_SOURCE_DIR}/Info.plist.in
)

# Install target
install(TARGETS WAAFer
    BUNDLE DESTINATION .
    RUNTIME DESTINATION bin
)
