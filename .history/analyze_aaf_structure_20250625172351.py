#!/usr/bin/env python3
"""
AAF Structure Analysis Tool
Analyzes AAF files to understand their internal structure and identify issues
"""

import sys
import os
from pathlib import Path

try:
    import aaf2
except ImportError:
    print("Error: pyaaf2 not installed. Please install with: pip install pyaaf2")
    sys.exit(1)

def analyze_aaf_structure(aaf_path, output_file=None):
    """Analyze AAF file structure and print detailed information"""
    
    print(f"\n=== Analyzing AAF File: {aaf_path} ===")
    
    if not os.path.exists(aaf_path):
        print(f"Error: File not found: {aaf_path}")
        return False
    
    try:
        with aaf2.open(aaf_path, 'r') as f:
            analysis = []
            
            # Basic file info
            analysis.append("=== BASIC FILE INFO ===")
            analysis.append(f"File: {aaf_path}")
            analysis.append(f"File size: {os.path.getsize(aaf_path):,} bytes")
            
            # Header information
            analysis.append("\n=== HEADER INFO ===")
            try:
                header = f.header
                analysis.append(f"Header: {header}")
                analysis.append(f"Version: {getattr(header, 'version', 'Unknown')}")
                analysis.append(f"Object Model Version: {getattr(header, 'object_model_version', 'Unknown')}")
                analysis.append(f"Operational Pattern: {getattr(header, 'operational_pattern', 'Unknown')}")
            except Exception as e:
                analysis.append(f"Header analysis error: {e}")
            
            # Content information
            analysis.append("\n=== CONTENT INFO ===")
            content = f.content
            analysis.append(f"Content: {content}")
            
            # Mobs analysis
            analysis.append(f"\n=== MOBS ({len(content.mobs)} total) ===")
            for i, mob in enumerate(content.mobs):
                analysis.append(f"\nMob {i+1}:")
                analysis.append(f"  Type: {type(mob).__name__}")
                analysis.append(f"  Name: {getattr(mob, 'name', 'Unnamed')}")
                analysis.append(f"  Mob ID: {getattr(mob, 'mob_id', 'No ID')}")
                analysis.append(f"  Usage Code: {getattr(mob, 'usage_code', 'No usage code')}")
                
                # Slots analysis
                if hasattr(mob, 'slots'):
                    analysis.append(f"  Slots: {len(mob.slots)}")
                    for j, slot in enumerate(mob.slots):
                        analysis.append(f"    Slot {j+1}:")
                        analysis.append(f"      Type: {type(slot).__name__}")
                        analysis.append(f"      Slot ID: {getattr(slot, 'slot_id', 'No ID')}")
                        analysis.append(f"      Name: {getattr(slot, 'name', 'Unnamed')}")
                        analysis.append(f"      Physical Track Number: {getattr(slot, 'physical_track_number', 'None')}")
                        
                        # Segment analysis
                        if hasattr(slot, 'segment'):
                            segment = slot.segment
                            analysis.append(f"      Segment: {type(segment).__name__}")
                            analysis.append(f"      Segment Length: {getattr(segment, 'length', 'Unknown')}")
                            
                            # Components analysis for sequences
                            if hasattr(segment, 'components'):
                                try:
                                    components_count = len(segment.components)
                                    analysis.append(f"      Components: {components_count}")

                                    # Iterate through components safely
                                    for k in range(min(5, components_count)):  # Limit to first 5
                                        try:
                                            comp = segment.components[k]
                                            analysis.append(f"        Component {k+1}: {type(comp).__name__}")
                                            analysis.append(f"          Length: {getattr(comp, 'length', 'Unknown')}")
                                            analysis.append(f"          Start Time: {getattr(comp, 'start_time', 'Unknown')}")
                                            if hasattr(comp, 'source_mob'):
                                                analysis.append(f"          Source Mob: {getattr(comp.source_mob, 'name', 'Unnamed') if comp.source_mob else 'None'}")
                                        except Exception as comp_e:
                                            analysis.append(f"        Component {k+1}: Error accessing - {comp_e}")

                                    if components_count > 5:
                                        analysis.append(f"        ... and {components_count - 5} more components")
                                except Exception as comp_err:
                                    analysis.append(f"      Components: Error accessing - {comp_err}")
            
            # Dictionary analysis
            analysis.append(f"\n=== DICTIONARY ===")
            try:
                dictionary = f.dictionary
                analysis.append(f"Dictionary: {dictionary}")
                analysis.append(f"Class definitions: {len(dictionary.class_defs) if hasattr(dictionary, 'class_defs') else 'Unknown'}")
                analysis.append(f"Type definitions: {len(dictionary.type_defs) if hasattr(dictionary, 'type_defs') else 'Unknown'}")
            except Exception as e:
                analysis.append(f"Dictionary analysis error: {e}")
            
            # Essence data
            analysis.append(f"\n=== ESSENCE DATA ===")
            try:
                if hasattr(content, 'essence_data'):
                    analysis.append(f"Essence data objects: {len(content.essence_data)}")
                    for i, essence in enumerate(content.essence_data[:3]):  # Limit to first 3
                        analysis.append(f"  Essence {i+1}: {essence}")
                        analysis.append(f"    Mob ID: {getattr(essence, 'mob_id', 'No ID')}")
                else:
                    analysis.append("No essence data found")
            except Exception as e:
                analysis.append(f"Essence data analysis error: {e}")
            
            # Print analysis
            for line in analysis:
                print(line)
            
            # Save to file if requested
            if output_file:
                with open(output_file, 'w') as out:
                    out.write('\n'.join(analysis))
                print(f"\nAnalysis saved to: {output_file}")
            
            return True
            
    except Exception as e:
        print(f"Error analyzing AAF file: {e}")
        import traceback
        traceback.print_exc()
        return False

def compare_aaf_files(good_aaf, problematic_aaf):
    """Compare two AAF files to identify structural differences"""
    
    print(f"\n=== COMPARING AAF FILES ===")
    print(f"Good AAF: {good_aaf}")
    print(f"Problematic AAF: {problematic_aaf}")
    
    # Analyze both files
    print("\n--- ANALYZING GOOD AAF ---")
    analyze_aaf_structure(good_aaf, "good_aaf_analysis.txt")
    
    print("\n--- ANALYZING PROBLEMATIC AAF ---")
    analyze_aaf_structure(problematic_aaf, "problematic_aaf_analysis.txt")
    
    print("\n=== COMPARISON COMPLETE ===")
    print("Check good_aaf_analysis.txt and problematic_aaf_analysis.txt for detailed comparison")

def main():
    if len(sys.argv) < 2:
        print("Usage:")
        print("  python analyze_aaf_structure.py <aaf_file>")
        print("  python analyze_aaf_structure.py <good_aaf> <problematic_aaf>  # Compare two files")
        sys.exit(1)
    
    if len(sys.argv) == 2:
        # Single file analysis
        aaf_file = sys.argv[1]
        analyze_aaf_structure(aaf_file)
    else:
        # Compare two files
        good_aaf = sys.argv[1]
        problematic_aaf = sys.argv[2]
        compare_aaf_files(good_aaf, problematic_aaf)

if __name__ == "__main__":
    main()
