#!/usr/bin/env python3
"""
Test script to verify the Analysis Settings Dialog functionality
"""

import sys
import os
import time
import subprocess

def test_analysis_settings():
    """Test the Analysis Settings Dialog"""
    print("Testing WAAFer Analysis Settings Dialog...")
    
    # Check if WAAFer is running
    try:
        # Use AppleScript to interact with the application
        script = '''
        tell application "System Events"
            tell process "WAAFer"
                if exists then
                    set frontmost to true
                    return "WAAFer is running"
                else
                    return "WAAFer not found"
                end if
            end tell
        end tell
        '''
        
        result = subprocess.run(['osascript', '-e', script], 
                              capture_output=True, text=True)
        
        if "WAAFer is running" in result.stdout:
            print("✓ WAAFer application is running")
            
            # Try to click on Classification tab and then Analysis Settings
            click_script = '''
            tell application "System Events"
                tell process "WAAFer"
                    try
                        -- Click on Classification tab
                        click button "Classification" of tab group 1 of window 1
                        delay 1
                        
                        -- Look for Analysis Settings button
                        if exists button "Analysis Settings" of window 1 then
                            click button "Analysis Settings" of window 1
                            delay 2
                            return "Analysis Settings dialog opened"
                        else
                            return "Analysis Settings button not found"
                        end if
                    on error errMsg
                        return "Error: " & errMsg
                    end try
                end tell
            end tell
            '''
            
            result = subprocess.run(['osascript', '-e', click_script], 
                                  capture_output=True, text=True)
            print(f"Dialog test result: {result.stdout.strip()}")
            
        else:
            print("✗ WAAFer application not found")
            
    except Exception as e:
        print(f"✗ Error testing application: {e}")

def main():
    """Main test function"""
    print("=== WAAFer Analysis Settings Test ===")
    test_analysis_settings()
    print("=== Test Complete ===")

if __name__ == "__main__":
    main()
