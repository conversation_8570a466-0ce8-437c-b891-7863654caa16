[{"directory": "/Volumes/Projects/_Code/_Waafer/build", "command": "/usr/bin/c++ -DQT_CORE_LIB -DQT_GUI_LIB -DQT_MULTIMEDIA_LIB -DQT_NETWORK_LIB -DQT_OPENGL_LIB -DQT_QMLINTEGRATION_LIB -DQT_QMLMETA_LIB -DQT_QMLMODELS_LIB -DQT_QMLWORKERSCRIPT_LIB -DQT_QML_LIB -DQT_QUICK_LIB -DQT_WIDGETS_LIB -I/Volumes/Projects/_Code/_Waafer/build/WAAFer_autogen/include -I/Volumes/Projects/_Code/_Waafer/src -I/Volumes/Projects/_Code/_Waafer/third-party/LibAAF/include -I/opt/homebrew/Frameworks/Python.framework/Versions/3.13/include/python3.13 -I/Volumes/Projects/_Code/_Waafer/build/include -isystem /opt/homebrew/lib/QtCore.framework/Headers -iframework /opt/homebrew/lib -isystem /opt/homebrew/share/qt/mkspecs/macx-clang -isystem /opt/homebrew/include -isystem /opt/homebrew/lib/QtWidgets.framework/Headers -isystem /opt/homebrew/lib/QtGui.framework/Headers -isystem /opt/homebrew/lib/QtQml.framework/Headers -isystem /opt/homebrew/include/QtQmlIntegration -isystem /opt/homebrew/lib/QtNetwork.framework/Headers -isystem /opt/homebrew/lib/QtQuick.framework/Headers -isystem /opt/homebrew/lib/QtQmlMeta.framework/Headers -isystem /opt/homebrew/lib/QtQmlModels.framework/Headers -isystem /opt/homebrew/lib/QtQmlWorkerScript.framework/Headers -isystem /opt/homebrew/lib/QtOpenGL.framework/Headers -isystem /opt/homebrew/lib/QtMultimedia.framework/Headers -g -std=gnu++17 -arch arm64 -mmacosx-version-min=11.0 -o CMakeFiles/WAAFer.dir/WAAFer_autogen/mocs_compilation.cpp.o -c /Volumes/Projects/_Code/_Waafer/build/WAAFer_autogen/mocs_compilation.cpp", "file": "/Volumes/Projects/_Code/_Waafer/build/WAAFer_autogen/mocs_compilation.cpp", "output": "CMakeFiles/WAAFer.dir/WAAFer_autogen/mocs_compilation.cpp.o"}, {"directory": "/Volumes/Projects/_Code/_Waafer/build", "command": "/usr/bin/c++ -DQT_CORE_LIB -DQT_GUI_LIB -DQT_MULTIMEDIA_LIB -DQT_NETWORK_LIB -DQT_OPENGL_LIB -DQT_QMLINTEGRATION_LIB -DQT_QMLMETA_LIB -DQT_QMLMODELS_LIB -DQT_QMLWORKERSCRIPT_LIB -DQT_QML_LIB -DQT_QUICK_LIB -DQT_WIDGETS_LIB -I/Volumes/Projects/_Code/_Waafer/build/WAAFer_autogen/include -I/Volumes/Projects/_Code/_Waafer/src -I/Volumes/Projects/_Code/_Waafer/third-party/LibAAF/include -I/opt/homebrew/Frameworks/Python.framework/Versions/3.13/include/python3.13 -I/Volumes/Projects/_Code/_Waafer/build/include -isystem /opt/homebrew/lib/QtCore.framework/Headers -iframework /opt/homebrew/lib -isystem /opt/homebrew/share/qt/mkspecs/macx-clang -isystem /opt/homebrew/include -isystem /opt/homebrew/lib/QtWidgets.framework/Headers -isystem /opt/homebrew/lib/QtGui.framework/Headers -isystem /opt/homebrew/lib/QtQml.framework/Headers -isystem /opt/homebrew/include/QtQmlIntegration -isystem /opt/homebrew/lib/QtNetwork.framework/Headers -isystem /opt/homebrew/lib/QtQuick.framework/Headers -isystem /opt/homebrew/lib/QtQmlMeta.framework/Headers -isystem /opt/homebrew/lib/QtQmlModels.framework/Headers -isystem /opt/homebrew/lib/QtQmlWorkerScript.framework/Headers -isystem /opt/homebrew/lib/QtOpenGL.framework/Headers -isystem /opt/homebrew/lib/QtMultimedia.framework/Headers -g -std=gnu++17 -arch arm64 -mmacosx-version-min=11.0 -o CMakeFiles/WAAFer.dir/src/main.cpp.o -c /Volumes/Projects/_Code/_Waafer/src/main.cpp", "file": "/Volumes/Projects/_Code/_Waafer/src/main.cpp", "output": "CMakeFiles/WAAFer.dir/src/main.cpp.o"}, {"directory": "/Volumes/Projects/_Code/_Waafer/build", "command": "/usr/bin/c++ -DQT_CORE_LIB -DQT_GUI_LIB -DQT_MULTIMEDIA_LIB -DQT_NETWORK_LIB -DQT_OPENGL_LIB -DQT_QMLINTEGRATION_LIB -DQT_QMLMETA_LIB -DQT_QMLMODELS_LIB -DQT_QMLWORKERSCRIPT_LIB -DQT_QML_LIB -DQT_QUICK_LIB -DQT_WIDGETS_LIB -I/Volumes/Projects/_Code/_Waafer/build/WAAFer_autogen/include -I/Volumes/Projects/_Code/_Waafer/src -I/Volumes/Projects/_Code/_Waafer/third-party/LibAAF/include -I/opt/homebrew/Frameworks/Python.framework/Versions/3.13/include/python3.13 -I/Volumes/Projects/_Code/_Waafer/build/include -isystem /opt/homebrew/lib/QtCore.framework/Headers -iframework /opt/homebrew/lib -isystem /opt/homebrew/share/qt/mkspecs/macx-clang -isystem /opt/homebrew/include -isystem /opt/homebrew/lib/QtWidgets.framework/Headers -isystem /opt/homebrew/lib/QtGui.framework/Headers -isystem /opt/homebrew/lib/QtQml.framework/Headers -isystem /opt/homebrew/include/QtQmlIntegration -isystem /opt/homebrew/lib/QtNetwork.framework/Headers -isystem /opt/homebrew/lib/QtQuick.framework/Headers -isystem /opt/homebrew/lib/QtQmlMeta.framework/Headers -isystem /opt/homebrew/lib/QtQmlModels.framework/Headers -isystem /opt/homebrew/lib/QtQmlWorkerScript.framework/Headers -isystem /opt/homebrew/lib/QtOpenGL.framework/Headers -isystem /opt/homebrew/lib/QtMultimedia.framework/Headers -g -std=gnu++17 -arch arm64 -mmacosx-version-min=11.0 -o CMakeFiles/WAAFer.dir/src/core/AAFReader.cpp.o -c /Volumes/Projects/_Code/_Waafer/src/core/AAFReader.cpp", "file": "/Volumes/Projects/_Code/_Waafer/src/core/AAFReader.cpp", "output": "CMakeFiles/WAAFer.dir/src/core/AAFReader.cpp.o"}, {"directory": "/Volumes/Projects/_Code/_Waafer/build", "command": "/usr/bin/c++ -DQT_CORE_LIB -DQT_GUI_LIB -DQT_MULTIMEDIA_LIB -DQT_NETWORK_LIB -DQT_OPENGL_LIB -DQT_QMLINTEGRATION_LIB -DQT_QMLMETA_LIB -DQT_QMLMODELS_LIB -DQT_QMLWORKERSCRIPT_LIB -DQT_QML_LIB -DQT_QUICK_LIB -DQT_WIDGETS_LIB -I/Volumes/Projects/_Code/_Waafer/build/WAAFer_autogen/include -I/Volumes/Projects/_Code/_Waafer/src -I/Volumes/Projects/_Code/_Waafer/third-party/LibAAF/include -I/opt/homebrew/Frameworks/Python.framework/Versions/3.13/include/python3.13 -I/Volumes/Projects/_Code/_Waafer/build/include -isystem /opt/homebrew/lib/QtCore.framework/Headers -iframework /opt/homebrew/lib -isystem /opt/homebrew/share/qt/mkspecs/macx-clang -isystem /opt/homebrew/include -isystem /opt/homebrew/lib/QtWidgets.framework/Headers -isystem /opt/homebrew/lib/QtGui.framework/Headers -isystem /opt/homebrew/lib/QtQml.framework/Headers -isystem /opt/homebrew/include/QtQmlIntegration -isystem /opt/homebrew/lib/QtNetwork.framework/Headers -isystem /opt/homebrew/lib/QtQuick.framework/Headers -isystem /opt/homebrew/lib/QtQmlMeta.framework/Headers -isystem /opt/homebrew/lib/QtQmlModels.framework/Headers -isystem /opt/homebrew/lib/QtQmlWorkerScript.framework/Headers -isystem /opt/homebrew/lib/QtOpenGL.framework/Headers -isystem /opt/homebrew/lib/QtMultimedia.framework/Headers -g -std=gnu++17 -arch arm64 -mmacosx-version-min=11.0 -o CMakeFiles/WAAFer.dir/src/core/LibAAFWrapper.cpp.o -c /Volumes/Projects/_Code/_Waafer/src/core/LibAAFWrapper.cpp", "file": "/Volumes/Projects/_Code/_Waafer/src/core/LibAAFWrapper.cpp", "output": "CMakeFiles/WAAFer.dir/src/core/LibAAFWrapper.cpp.o"}, {"directory": "/Volumes/Projects/_Code/_Waafer/build", "command": "/usr/bin/c++ -DQT_CORE_LIB -DQT_GUI_LIB -DQT_MULTIMEDIA_LIB -DQT_NETWORK_LIB -DQT_OPENGL_LIB -DQT_QMLINTEGRATION_LIB -DQT_QMLMETA_LIB -DQT_QMLMODELS_LIB -DQT_QMLWORKERSCRIPT_LIB -DQT_QML_LIB -DQT_QUICK_LIB -DQT_WIDGETS_LIB -I/Volumes/Projects/_Code/_Waafer/build/WAAFer_autogen/include -I/Volumes/Projects/_Code/_Waafer/src -I/Volumes/Projects/_Code/_Waafer/third-party/LibAAF/include -I/opt/homebrew/Frameworks/Python.framework/Versions/3.13/include/python3.13 -I/Volumes/Projects/_Code/_Waafer/build/include -isystem /opt/homebrew/lib/QtCore.framework/Headers -iframework /opt/homebrew/lib -isystem /opt/homebrew/share/qt/mkspecs/macx-clang -isystem /opt/homebrew/include -isystem /opt/homebrew/lib/QtWidgets.framework/Headers -isystem /opt/homebrew/lib/QtGui.framework/Headers -isystem /opt/homebrew/lib/QtQml.framework/Headers -isystem /opt/homebrew/include/QtQmlIntegration -isystem /opt/homebrew/lib/QtNetwork.framework/Headers -isystem /opt/homebrew/lib/QtQuick.framework/Headers -isystem /opt/homebrew/lib/QtQmlMeta.framework/Headers -isystem /opt/homebrew/lib/QtQmlModels.framework/Headers -isystem /opt/homebrew/lib/QtQmlWorkerScript.framework/Headers -isystem /opt/homebrew/lib/QtOpenGL.framework/Headers -isystem /opt/homebrew/lib/QtMultimedia.framework/Headers -g -std=gnu++17 -arch arm64 -mmacosx-version-min=11.0 -o CMakeFiles/WAAFer.dir/src/core/MemoryManager.cpp.o -c /Volumes/Projects/_Code/_Waafer/src/core/MemoryManager.cpp", "file": "/Volumes/Projects/_Code/_Waafer/src/core/MemoryManager.cpp", "output": "CMakeFiles/WAAFer.dir/src/core/MemoryManager.cpp.o"}, {"directory": "/Volumes/Projects/_Code/_Waafer/build", "command": "/usr/bin/c++ -DQT_CORE_LIB -DQT_GUI_LIB -DQT_MULTIMEDIA_LIB -DQT_NETWORK_LIB -DQT_OPENGL_LIB -DQT_QMLINTEGRATION_LIB -DQT_QMLMETA_LIB -DQT_QMLMODELS_LIB -DQT_QMLWORKERSCRIPT_LIB -DQT_QML_LIB -DQT_QUICK_LIB -DQT_WIDGETS_LIB -I/Volumes/Projects/_Code/_Waafer/build/WAAFer_autogen/include -I/Volumes/Projects/_Code/_Waafer/src -I/Volumes/Projects/_Code/_Waafer/third-party/LibAAF/include -I/opt/homebrew/Frameworks/Python.framework/Versions/3.13/include/python3.13 -I/Volumes/Projects/_Code/_Waafer/build/include -isystem /opt/homebrew/lib/QtCore.framework/Headers -iframework /opt/homebrew/lib -isystem /opt/homebrew/share/qt/mkspecs/macx-clang -isystem /opt/homebrew/include -isystem /opt/homebrew/lib/QtWidgets.framework/Headers -isystem /opt/homebrew/lib/QtGui.framework/Headers -isystem /opt/homebrew/lib/QtQml.framework/Headers -isystem /opt/homebrew/include/QtQmlIntegration -isystem /opt/homebrew/lib/QtNetwork.framework/Headers -isystem /opt/homebrew/lib/QtQuick.framework/Headers -isystem /opt/homebrew/lib/QtQmlMeta.framework/Headers -isystem /opt/homebrew/lib/QtQmlModels.framework/Headers -isystem /opt/homebrew/lib/QtQmlWorkerScript.framework/Headers -isystem /opt/homebrew/lib/QtOpenGL.framework/Headers -isystem /opt/homebrew/lib/QtMultimedia.framework/Headers -g -std=gnu++17 -arch arm64 -mmacosx-version-min=11.0 -o CMakeFiles/WAAFer.dir/src/core/ChunkedFileReader.cpp.o -c /Volumes/Projects/_Code/_Waafer/src/core/ChunkedFileReader.cpp", "file": "/Volumes/Projects/_Code/_Waafer/src/core/ChunkedFileReader.cpp", "output": "CMakeFiles/WAAFer.dir/src/core/ChunkedFileReader.cpp.o"}, {"directory": "/Volumes/Projects/_Code/_Waafer/build", "command": "/usr/bin/c++ -DQT_CORE_LIB -DQT_GUI_LIB -DQT_MULTIMEDIA_LIB -DQT_NETWORK_LIB -DQT_OPENGL_LIB -DQT_QMLINTEGRATION_LIB -DQT_QMLMETA_LIB -DQT_QMLMODELS_LIB -DQT_QMLWORKERSCRIPT_LIB -DQT_QML_LIB -DQT_QUICK_LIB -DQT_WIDGETS_LIB -I/Volumes/Projects/_Code/_Waafer/build/WAAFer_autogen/include -I/Volumes/Projects/_Code/_Waafer/src -I/Volumes/Projects/_Code/_Waafer/third-party/LibAAF/include -I/opt/homebrew/Frameworks/Python.framework/Versions/3.13/include/python3.13 -I/Volumes/Projects/_Code/_Waafer/build/include -isystem /opt/homebrew/lib/QtCore.framework/Headers -iframework /opt/homebrew/lib -isystem /opt/homebrew/share/qt/mkspecs/macx-clang -isystem /opt/homebrew/include -isystem /opt/homebrew/lib/QtWidgets.framework/Headers -isystem /opt/homebrew/lib/QtGui.framework/Headers -isystem /opt/homebrew/lib/QtQml.framework/Headers -isystem /opt/homebrew/include/QtQmlIntegration -isystem /opt/homebrew/lib/QtNetwork.framework/Headers -isystem /opt/homebrew/lib/QtQuick.framework/Headers -isystem /opt/homebrew/lib/QtQmlMeta.framework/Headers -isystem /opt/homebrew/lib/QtQmlModels.framework/Headers -isystem /opt/homebrew/lib/QtQmlWorkerScript.framework/Headers -isystem /opt/homebrew/lib/QtOpenGL.framework/Headers -isystem /opt/homebrew/lib/QtMultimedia.framework/Headers -g -std=gnu++17 -arch arm64 -mmacosx-version-min=11.0 -o CMakeFiles/WAAFer.dir/src/core/ProgressTracker.cpp.o -c /Volumes/Projects/_Code/_Waafer/src/core/ProgressTracker.cpp", "file": "/Volumes/Projects/_Code/_Waafer/src/core/ProgressTracker.cpp", "output": "CMakeFiles/WAAFer.dir/src/core/ProgressTracker.cpp.o"}, {"directory": "/Volumes/Projects/_Code/_Waafer/build", "command": "/usr/bin/c++ -DQT_CORE_LIB -DQT_GUI_LIB -DQT_MULTIMEDIA_LIB -DQT_NETWORK_LIB -DQT_OPENGL_LIB -DQT_QMLINTEGRATION_LIB -DQT_QMLMETA_LIB -DQT_QMLMODELS_LIB -DQT_QMLWORKERSCRIPT_LIB -DQT_QML_LIB -DQT_QUICK_LIB -DQT_WIDGETS_LIB -I/Volumes/Projects/_Code/_Waafer/build/WAAFer_autogen/include -I/Volumes/Projects/_Code/_Waafer/src -I/Volumes/Projects/_Code/_Waafer/third-party/LibAAF/include -I/opt/homebrew/Frameworks/Python.framework/Versions/3.13/include/python3.13 -I/Volumes/Projects/_Code/_Waafer/build/include -isystem /opt/homebrew/lib/QtCore.framework/Headers -iframework /opt/homebrew/lib -isystem /opt/homebrew/share/qt/mkspecs/macx-clang -isystem /opt/homebrew/include -isystem /opt/homebrew/lib/QtWidgets.framework/Headers -isystem /opt/homebrew/lib/QtGui.framework/Headers -isystem /opt/homebrew/lib/QtQml.framework/Headers -isystem /opt/homebrew/include/QtQmlIntegration -isystem /opt/homebrew/lib/QtNetwork.framework/Headers -isystem /opt/homebrew/lib/QtQuick.framework/Headers -isystem /opt/homebrew/lib/QtQmlMeta.framework/Headers -isystem /opt/homebrew/lib/QtQmlModels.framework/Headers -isystem /opt/homebrew/lib/QtQmlWorkerScript.framework/Headers -isystem /opt/homebrew/lib/QtOpenGL.framework/Headers -isystem /opt/homebrew/lib/QtMultimedia.framework/Headers -g -std=gnu++17 -arch arm64 -mmacosx-version-min=11.0 -o CMakeFiles/WAAFer.dir/src/core/AnalysisSettings.cpp.o -c /Volumes/Projects/_Code/_Waafer/src/core/AnalysisSettings.cpp", "file": "/Volumes/Projects/_Code/_Waafer/src/core/AnalysisSettings.cpp", "output": "CMakeFiles/WAAFer.dir/src/core/AnalysisSettings.cpp.o"}, {"directory": "/Volumes/Projects/_Code/_Waafer/build", "command": "/usr/bin/c++ -DQT_CORE_LIB -DQT_GUI_LIB -DQT_MULTIMEDIA_LIB -DQT_NETWORK_LIB -DQT_OPENGL_LIB -DQT_QMLINTEGRATION_LIB -DQT_QMLMETA_LIB -DQT_QMLMODELS_LIB -DQT_QMLWORKERSCRIPT_LIB -DQT_QML_LIB -DQT_QUICK_LIB -DQT_WIDGETS_LIB -I/Volumes/Projects/_Code/_Waafer/build/WAAFer_autogen/include -I/Volumes/Projects/_Code/_Waafer/src -I/Volumes/Projects/_Code/_Waafer/third-party/LibAAF/include -I/opt/homebrew/Frameworks/Python.framework/Versions/3.13/include/python3.13 -I/Volumes/Projects/_Code/_Waafer/build/include -isystem /opt/homebrew/lib/QtCore.framework/Headers -iframework /opt/homebrew/lib -isystem /opt/homebrew/share/qt/mkspecs/macx-clang -isystem /opt/homebrew/include -isystem /opt/homebrew/lib/QtWidgets.framework/Headers -isystem /opt/homebrew/lib/QtGui.framework/Headers -isystem /opt/homebrew/lib/QtQml.framework/Headers -isystem /opt/homebrew/include/QtQmlIntegration -isystem /opt/homebrew/lib/QtNetwork.framework/Headers -isystem /opt/homebrew/lib/QtQuick.framework/Headers -isystem /opt/homebrew/lib/QtQmlMeta.framework/Headers -isystem /opt/homebrew/lib/QtQmlModels.framework/Headers -isystem /opt/homebrew/lib/QtQmlWorkerScript.framework/Headers -isystem /opt/homebrew/lib/QtOpenGL.framework/Headers -isystem /opt/homebrew/lib/QtMultimedia.framework/Headers -g -std=gnu++17 -arch arm64 -mmacosx-version-min=11.0 -o CMakeFiles/WAAFer.dir/src/core/TrackOrganizer.cpp.o -c /Volumes/Projects/_Code/_Waafer/src/core/TrackOrganizer.cpp", "file": "/Volumes/Projects/_Code/_Waafer/src/core/TrackOrganizer.cpp", "output": "CMakeFiles/WAAFer.dir/src/core/TrackOrganizer.cpp.o"}, {"directory": "/Volumes/Projects/_Code/_Waafer/build", "command": "/usr/bin/c++ -DQT_CORE_LIB -DQT_GUI_LIB -DQT_MULTIMEDIA_LIB -DQT_NETWORK_LIB -DQT_OPENGL_LIB -DQT_QMLINTEGRATION_LIB -DQT_QMLMETA_LIB -DQT_QMLMODELS_LIB -DQT_QMLWORKERSCRIPT_LIB -DQT_QML_LIB -DQT_QUICK_LIB -DQT_WIDGETS_LIB -I/Volumes/Projects/_Code/_Waafer/build/WAAFer_autogen/include -I/Volumes/Projects/_Code/_Waafer/src -I/Volumes/Projects/_Code/_Waafer/third-party/LibAAF/include -I/opt/homebrew/Frameworks/Python.framework/Versions/3.13/include/python3.13 -I/Volumes/Projects/_Code/_Waafer/build/include -isystem /opt/homebrew/lib/QtCore.framework/Headers -iframework /opt/homebrew/lib -isystem /opt/homebrew/share/qt/mkspecs/macx-clang -isystem /opt/homebrew/include -isystem /opt/homebrew/lib/QtWidgets.framework/Headers -isystem /opt/homebrew/lib/QtGui.framework/Headers -isystem /opt/homebrew/lib/QtQml.framework/Headers -isystem /opt/homebrew/include/QtQmlIntegration -isystem /opt/homebrew/lib/QtNetwork.framework/Headers -isystem /opt/homebrew/lib/QtQuick.framework/Headers -isystem /opt/homebrew/lib/QtQmlMeta.framework/Headers -isystem /opt/homebrew/lib/QtQmlModels.framework/Headers -isystem /opt/homebrew/lib/QtQmlWorkerScript.framework/Headers -isystem /opt/homebrew/lib/QtOpenGL.framework/Headers -isystem /opt/homebrew/lib/QtMultimedia.framework/Headers -g -std=gnu++17 -arch arm64 -mmacosx-version-min=11.0 -o CMakeFiles/WAAFer.dir/src/python/PythonBridge.cpp.o -c /Volumes/Projects/_Code/_Waafer/src/python/PythonBridge.cpp", "file": "/Volumes/Projects/_Code/_Waafer/src/python/PythonBridge.cpp", "output": "CMakeFiles/WAAFer.dir/src/python/PythonBridge.cpp.o"}, {"directory": "/Volumes/Projects/_Code/_Waafer/build", "command": "/usr/bin/c++ -DQT_CORE_LIB -DQT_GUI_LIB -DQT_MULTIMEDIA_LIB -DQT_NETWORK_LIB -DQT_OPENGL_LIB -DQT_QMLINTEGRATION_LIB -DQT_QMLMETA_LIB -DQT_QMLMODELS_LIB -DQT_QMLWORKERSCRIPT_LIB -DQT_QML_LIB -DQT_QUICK_LIB -DQT_WIDGETS_LIB -I/Volumes/Projects/_Code/_Waafer/build/WAAFer_autogen/include -I/Volumes/Projects/_Code/_Waafer/src -I/Volumes/Projects/_Code/_Waafer/third-party/LibAAF/include -I/opt/homebrew/Frameworks/Python.framework/Versions/3.13/include/python3.13 -I/Volumes/Projects/_Code/_Waafer/build/include -isystem /opt/homebrew/lib/QtCore.framework/Headers -iframework /opt/homebrew/lib -isystem /opt/homebrew/share/qt/mkspecs/macx-clang -isystem /opt/homebrew/include -isystem /opt/homebrew/lib/QtWidgets.framework/Headers -isystem /opt/homebrew/lib/QtGui.framework/Headers -isystem /opt/homebrew/lib/QtQml.framework/Headers -isystem /opt/homebrew/include/QtQmlIntegration -isystem /opt/homebrew/lib/QtNetwork.framework/Headers -isystem /opt/homebrew/lib/QtQuick.framework/Headers -isystem /opt/homebrew/lib/QtQmlMeta.framework/Headers -isystem /opt/homebrew/lib/QtQmlModels.framework/Headers -isystem /opt/homebrew/lib/QtQmlWorkerScript.framework/Headers -isystem /opt/homebrew/lib/QtOpenGL.framework/Headers -isystem /opt/homebrew/lib/QtMultimedia.framework/Headers -g -std=gnu++17 -arch arm64 -mmacosx-version-min=11.0 -o CMakeFiles/WAAFer.dir/src/python/PythonInterpreter.cpp.o -c /Volumes/Projects/_Code/_Waafer/src/python/PythonInterpreter.cpp", "file": "/Volumes/Projects/_Code/_Waafer/src/python/PythonInterpreter.cpp", "output": "CMakeFiles/WAAFer.dir/src/python/PythonInterpreter.cpp.o"}, {"directory": "/Volumes/Projects/_Code/_Waafer/build", "command": "/usr/bin/c++ -DQT_CORE_LIB -DQT_GUI_LIB -DQT_MULTIMEDIA_LIB -DQT_NETWORK_LIB -DQT_OPENGL_LIB -DQT_QMLINTEGRATION_LIB -DQT_QMLMETA_LIB -DQT_QMLMODELS_LIB -DQT_QMLWORKERSCRIPT_LIB -DQT_QML_LIB -DQT_QUICK_LIB -DQT_WIDGETS_LIB -I/Volumes/Projects/_Code/_Waafer/build/WAAFer_autogen/include -I/Volumes/Projects/_Code/_Waafer/src -I/Volumes/Projects/_Code/_Waafer/third-party/LibAAF/include -I/opt/homebrew/Frameworks/Python.framework/Versions/3.13/include/python3.13 -I/Volumes/Projects/_Code/_Waafer/build/include -isystem /opt/homebrew/lib/QtCore.framework/Headers -iframework /opt/homebrew/lib -isystem /opt/homebrew/share/qt/mkspecs/macx-clang -isystem /opt/homebrew/include -isystem /opt/homebrew/lib/QtWidgets.framework/Headers -isystem /opt/homebrew/lib/QtGui.framework/Headers -isystem /opt/homebrew/lib/QtQml.framework/Headers -isystem /opt/homebrew/include/QtQmlIntegration -isystem /opt/homebrew/lib/QtNetwork.framework/Headers -isystem /opt/homebrew/lib/QtQuick.framework/Headers -isystem /opt/homebrew/lib/QtQmlMeta.framework/Headers -isystem /opt/homebrew/lib/QtQmlModels.framework/Headers -isystem /opt/homebrew/lib/QtQmlWorkerScript.framework/Headers -isystem /opt/homebrew/lib/QtOpenGL.framework/Headers -isystem /opt/homebrew/lib/QtMultimedia.framework/Headers -g -std=gnu++17 -arch arm64 -mmacosx-version-min=11.0 -o CMakeFiles/WAAFer.dir/src/audio/AudioAnalyzer.cpp.o -c /Volumes/Projects/_Code/_Waafer/src/audio/AudioAnalyzer.cpp", "file": "/Volumes/Projects/_Code/_Waafer/src/audio/AudioAnalyzer.cpp", "output": "CMakeFiles/WAAFer.dir/src/audio/AudioAnalyzer.cpp.o"}, {"directory": "/Volumes/Projects/_Code/_Waafer/build", "command": "/usr/bin/c++ -DQT_CORE_LIB -DQT_GUI_LIB -DQT_MULTIMEDIA_LIB -DQT_NETWORK_LIB -DQT_OPENGL_LIB -DQT_QMLINTEGRATION_LIB -DQT_QMLMETA_LIB -DQT_QMLMODELS_LIB -DQT_QMLWORKERSCRIPT_LIB -DQT_QML_LIB -DQT_QUICK_LIB -DQT_WIDGETS_LIB -I/Volumes/Projects/_Code/_Waafer/build/WAAFer_autogen/include -I/Volumes/Projects/_Code/_Waafer/src -I/Volumes/Projects/_Code/_Waafer/third-party/LibAAF/include -I/opt/homebrew/Frameworks/Python.framework/Versions/3.13/include/python3.13 -I/Volumes/Projects/_Code/_Waafer/build/include -isystem /opt/homebrew/lib/QtCore.framework/Headers -iframework /opt/homebrew/lib -isystem /opt/homebrew/share/qt/mkspecs/macx-clang -isystem /opt/homebrew/include -isystem /opt/homebrew/lib/QtWidgets.framework/Headers -isystem /opt/homebrew/lib/QtGui.framework/Headers -isystem /opt/homebrew/lib/QtQml.framework/Headers -isystem /opt/homebrew/include/QtQmlIntegration -isystem /opt/homebrew/lib/QtNetwork.framework/Headers -isystem /opt/homebrew/lib/QtQuick.framework/Headers -isystem /opt/homebrew/lib/QtQmlMeta.framework/Headers -isystem /opt/homebrew/lib/QtQmlModels.framework/Headers -isystem /opt/homebrew/lib/QtQmlWorkerScript.framework/Headers -isystem /opt/homebrew/lib/QtOpenGL.framework/Headers -isystem /opt/homebrew/lib/QtMultimedia.framework/Headers -g -std=gnu++17 -arch arm64 -mmacosx-version-min=11.0 -o CMakeFiles/WAAFer.dir/src/audio/AudioFileManager.cpp.o -c /Volumes/Projects/_Code/_Waafer/src/audio/AudioFileManager.cpp", "file": "/Volumes/Projects/_Code/_Waafer/src/audio/AudioFileManager.cpp", "output": "CMakeFiles/WAAFer.dir/src/audio/AudioFileManager.cpp.o"}, {"directory": "/Volumes/Projects/_Code/_Waafer/build", "command": "/usr/bin/c++ -DQT_CORE_LIB -DQT_GUI_LIB -DQT_MULTIMEDIA_LIB -DQT_NETWORK_LIB -DQT_OPENGL_LIB -DQT_QMLINTEGRATION_LIB -DQT_QMLMETA_LIB -DQT_QMLMODELS_LIB -DQT_QMLWORKERSCRIPT_LIB -DQT_QML_LIB -DQT_QUICK_LIB -DQT_WIDGETS_LIB -I/Volumes/Projects/_Code/_Waafer/build/WAAFer_autogen/include -I/Volumes/Projects/_Code/_Waafer/src -I/Volumes/Projects/_Code/_Waafer/third-party/LibAAF/include -I/opt/homebrew/Frameworks/Python.framework/Versions/3.13/include/python3.13 -I/Volumes/Projects/_Code/_Waafer/build/include -isystem /opt/homebrew/lib/QtCore.framework/Headers -iframework /opt/homebrew/lib -isystem /opt/homebrew/share/qt/mkspecs/macx-clang -isystem /opt/homebrew/include -isystem /opt/homebrew/lib/QtWidgets.framework/Headers -isystem /opt/homebrew/lib/QtGui.framework/Headers -isystem /opt/homebrew/lib/QtQml.framework/Headers -isystem /opt/homebrew/include/QtQmlIntegration -isystem /opt/homebrew/lib/QtNetwork.framework/Headers -isystem /opt/homebrew/lib/QtQuick.framework/Headers -isystem /opt/homebrew/lib/QtQmlMeta.framework/Headers -isystem /opt/homebrew/lib/QtQmlModels.framework/Headers -isystem /opt/homebrew/lib/QtQmlWorkerScript.framework/Headers -isystem /opt/homebrew/lib/QtOpenGL.framework/Headers -isystem /opt/homebrew/lib/QtMultimedia.framework/Headers -g -std=gnu++17 -arch arm64 -mmacosx-version-min=11.0 -o CMakeFiles/WAAFer.dir/src/audio/ClassificationEngine.cpp.o -c /Volumes/Projects/_Code/_Waafer/src/audio/ClassificationEngine.cpp", "file": "/Volumes/Projects/_Code/_Waafer/src/audio/ClassificationEngine.cpp", "output": "CMakeFiles/WAAFer.dir/src/audio/ClassificationEngine.cpp.o"}, {"directory": "/Volumes/Projects/_Code/_Waafer/build", "command": "/usr/bin/c++ -DQT_CORE_LIB -DQT_GUI_LIB -DQT_MULTIMEDIA_LIB -DQT_NETWORK_LIB -DQT_OPENGL_LIB -DQT_QMLINTEGRATION_LIB -DQT_QMLMETA_LIB -DQT_QMLMODELS_LIB -DQT_QMLWORKERSCRIPT_LIB -DQT_QML_LIB -DQT_QUICK_LIB -DQT_WIDGETS_LIB -I/Volumes/Projects/_Code/_Waafer/build/WAAFer_autogen/include -I/Volumes/Projects/_Code/_Waafer/src -I/Volumes/Projects/_Code/_Waafer/third-party/LibAAF/include -I/opt/homebrew/Frameworks/Python.framework/Versions/3.13/include/python3.13 -I/Volumes/Projects/_Code/_Waafer/build/include -isystem /opt/homebrew/lib/QtCore.framework/Headers -iframework /opt/homebrew/lib -isystem /opt/homebrew/share/qt/mkspecs/macx-clang -isystem /opt/homebrew/include -isystem /opt/homebrew/lib/QtWidgets.framework/Headers -isystem /opt/homebrew/lib/QtGui.framework/Headers -isystem /opt/homebrew/lib/QtQml.framework/Headers -isystem /opt/homebrew/include/QtQmlIntegration -isystem /opt/homebrew/lib/QtNetwork.framework/Headers -isystem /opt/homebrew/lib/QtQuick.framework/Headers -isystem /opt/homebrew/lib/QtQmlMeta.framework/Headers -isystem /opt/homebrew/lib/QtQmlModels.framework/Headers -isystem /opt/homebrew/lib/QtQmlWorkerScript.framework/Headers -isystem /opt/homebrew/lib/QtOpenGL.framework/Headers -isystem /opt/homebrew/lib/QtMultimedia.framework/Headers -g -std=gnu++17 -arch arm64 -mmacosx-version-min=11.0 -o CMakeFiles/WAAFer.dir/src/audio/AudioPlaybackManager.cpp.o -c /Volumes/Projects/_Code/_Waafer/src/audio/AudioPlaybackManager.cpp", "file": "/Volumes/Projects/_Code/_Waafer/src/audio/AudioPlaybackManager.cpp", "output": "CMakeFiles/WAAFer.dir/src/audio/AudioPlaybackManager.cpp.o"}, {"directory": "/Volumes/Projects/_Code/_Waafer/build", "command": "/usr/bin/c++ -DQT_CORE_LIB -DQT_GUI_LIB -DQT_MULTIMEDIA_LIB -DQT_NETWORK_LIB -DQT_OPENGL_LIB -DQT_QMLINTEGRATION_LIB -DQT_QMLMETA_LIB -DQT_QMLMODELS_LIB -DQT_QMLWORKERSCRIPT_LIB -DQT_QML_LIB -DQT_QUICK_LIB -DQT_WIDGETS_LIB -I/Volumes/Projects/_Code/_Waafer/build/WAAFer_autogen/include -I/Volumes/Projects/_Code/_Waafer/src -I/Volumes/Projects/_Code/_Waafer/third-party/LibAAF/include -I/opt/homebrew/Frameworks/Python.framework/Versions/3.13/include/python3.13 -I/Volumes/Projects/_Code/_Waafer/build/include -isystem /opt/homebrew/lib/QtCore.framework/Headers -iframework /opt/homebrew/lib -isystem /opt/homebrew/share/qt/mkspecs/macx-clang -isystem /opt/homebrew/include -isystem /opt/homebrew/lib/QtWidgets.framework/Headers -isystem /opt/homebrew/lib/QtGui.framework/Headers -isystem /opt/homebrew/lib/QtQml.framework/Headers -isystem /opt/homebrew/include/QtQmlIntegration -isystem /opt/homebrew/lib/QtNetwork.framework/Headers -isystem /opt/homebrew/lib/QtQuick.framework/Headers -isystem /opt/homebrew/lib/QtQmlMeta.framework/Headers -isystem /opt/homebrew/lib/QtQmlModels.framework/Headers -isystem /opt/homebrew/lib/QtQmlWorkerScript.framework/Headers -isystem /opt/homebrew/lib/QtOpenGL.framework/Headers -isystem /opt/homebrew/lib/QtMultimedia.framework/Headers -g -std=gnu++17 -arch arm64 -mmacosx-version-min=11.0 -o CMakeFiles/WAAFer.dir/src/audio/WebRTCVAD.cpp.o -c /Volumes/Projects/_Code/_Waafer/src/audio/WebRTCVAD.cpp", "file": "/Volumes/Projects/_Code/_Waafer/src/audio/WebRTCVAD.cpp", "output": "CMakeFiles/WAAFer.dir/src/audio/WebRTCVAD.cpp.o"}, {"directory": "/Volumes/Projects/_Code/_Waafer/build", "command": "/usr/bin/c++ -DQT_CORE_LIB -DQT_GUI_LIB -DQT_MULTIMEDIA_LIB -DQT_NETWORK_LIB -DQT_OPENGL_LIB -DQT_QMLINTEGRATION_LIB -DQT_QMLMETA_LIB -DQT_QMLMODELS_LIB -DQT_QMLWORKERSCRIPT_LIB -DQT_QML_LIB -DQT_QUICK_LIB -DQT_WIDGETS_LIB -I/Volumes/Projects/_Code/_Waafer/build/WAAFer_autogen/include -I/Volumes/Projects/_Code/_Waafer/src -I/Volumes/Projects/_Code/_Waafer/third-party/LibAAF/include -I/opt/homebrew/Frameworks/Python.framework/Versions/3.13/include/python3.13 -I/Volumes/Projects/_Code/_Waafer/build/include -isystem /opt/homebrew/lib/QtCore.framework/Headers -iframework /opt/homebrew/lib -isystem /opt/homebrew/share/qt/mkspecs/macx-clang -isystem /opt/homebrew/include -isystem /opt/homebrew/lib/QtWidgets.framework/Headers -isystem /opt/homebrew/lib/QtGui.framework/Headers -isystem /opt/homebrew/lib/QtQml.framework/Headers -isystem /opt/homebrew/include/QtQmlIntegration -isystem /opt/homebrew/lib/QtNetwork.framework/Headers -isystem /opt/homebrew/lib/QtQuick.framework/Headers -isystem /opt/homebrew/lib/QtQmlMeta.framework/Headers -isystem /opt/homebrew/lib/QtQmlModels.framework/Headers -isystem /opt/homebrew/lib/QtQmlWorkerScript.framework/Headers -isystem /opt/homebrew/lib/QtOpenGL.framework/Headers -isystem /opt/homebrew/lib/QtMultimedia.framework/Headers -g -std=gnu++17 -arch arm64 -mmacosx-version-min=11.0 -o CMakeFiles/WAAFer.dir/src/ai/LMStudioClient.cpp.o -c /Volumes/Projects/_Code/_Waafer/src/ai/LMStudioClient.cpp", "file": "/Volumes/Projects/_Code/_Waafer/src/ai/LMStudioClient.cpp", "output": "CMakeFiles/WAAFer.dir/src/ai/LMStudioClient.cpp.o"}, {"directory": "/Volumes/Projects/_Code/_Waafer/build", "command": "/usr/bin/c++ -DQT_CORE_LIB -DQT_GUI_LIB -DQT_MULTIMEDIA_LIB -DQT_NETWORK_LIB -DQT_OPENGL_LIB -DQT_QMLINTEGRATION_LIB -DQT_QMLMETA_LIB -DQT_QMLMODELS_LIB -DQT_QMLWORKERSCRIPT_LIB -DQT_QML_LIB -DQT_QUICK_LIB -DQT_WIDGETS_LIB -I/Volumes/Projects/_Code/_Waafer/build/WAAFer_autogen/include -I/Volumes/Projects/_Code/_Waafer/src -I/Volumes/Projects/_Code/_Waafer/third-party/LibAAF/include -I/opt/homebrew/Frameworks/Python.framework/Versions/3.13/include/python3.13 -I/Volumes/Projects/_Code/_Waafer/build/include -isystem /opt/homebrew/lib/QtCore.framework/Headers -iframework /opt/homebrew/lib -isystem /opt/homebrew/share/qt/mkspecs/macx-clang -isystem /opt/homebrew/include -isystem /opt/homebrew/lib/QtWidgets.framework/Headers -isystem /opt/homebrew/lib/QtGui.framework/Headers -isystem /opt/homebrew/lib/QtQml.framework/Headers -isystem /opt/homebrew/include/QtQmlIntegration -isystem /opt/homebrew/lib/QtNetwork.framework/Headers -isystem /opt/homebrew/lib/QtQuick.framework/Headers -isystem /opt/homebrew/lib/QtQmlMeta.framework/Headers -isystem /opt/homebrew/lib/QtQmlModels.framework/Headers -isystem /opt/homebrew/lib/QtQmlWorkerScript.framework/Headers -isystem /opt/homebrew/lib/QtOpenGL.framework/Headers -isystem /opt/homebrew/lib/QtMultimedia.framework/Headers -g -std=gnu++17 -arch arm64 -mmacosx-version-min=11.0 -o CMakeFiles/WAAFer.dir/src/export/AAFExporter.cpp.o -c /Volumes/Projects/_Code/_Waafer/src/export/AAFExporter.cpp", "file": "/Volumes/Projects/_Code/_Waafer/src/export/AAFExporter.cpp", "output": "CMakeFiles/WAAFer.dir/src/export/AAFExporter.cpp.o"}, {"directory": "/Volumes/Projects/_Code/_Waafer/build", "command": "/usr/bin/c++ -DQT_CORE_LIB -DQT_GUI_LIB -DQT_MULTIMEDIA_LIB -DQT_NETWORK_LIB -DQT_OPENGL_LIB -DQT_QMLINTEGRATION_LIB -DQT_QMLMETA_LIB -DQT_QMLMODELS_LIB -DQT_QMLWORKERSCRIPT_LIB -DQT_QML_LIB -DQT_QUICK_LIB -DQT_WIDGETS_LIB -I/Volumes/Projects/_Code/_Waafer/build/WAAFer_autogen/include -I/Volumes/Projects/_Code/_Waafer/src -I/Volumes/Projects/_Code/_Waafer/third-party/LibAAF/include -I/opt/homebrew/Frameworks/Python.framework/Versions/3.13/include/python3.13 -I/Volumes/Projects/_Code/_Waafer/build/include -isystem /opt/homebrew/lib/QtCore.framework/Headers -iframework /opt/homebrew/lib -isystem /opt/homebrew/share/qt/mkspecs/macx-clang -isystem /opt/homebrew/include -isystem /opt/homebrew/lib/QtWidgets.framework/Headers -isystem /opt/homebrew/lib/QtGui.framework/Headers -isystem /opt/homebrew/lib/QtQml.framework/Headers -isystem /opt/homebrew/include/QtQmlIntegration -isystem /opt/homebrew/lib/QtNetwork.framework/Headers -isystem /opt/homebrew/lib/QtQuick.framework/Headers -isystem /opt/homebrew/lib/QtQmlMeta.framework/Headers -isystem /opt/homebrew/lib/QtQmlModels.framework/Headers -isystem /opt/homebrew/lib/QtQmlWorkerScript.framework/Headers -isystem /opt/homebrew/lib/QtOpenGL.framework/Headers -isystem /opt/homebrew/lib/QtMultimedia.framework/Headers -g -std=gnu++17 -arch arm64 -mmacosx-version-min=11.0 -o CMakeFiles/WAAFer.dir/src/ui/MainWindow.cpp.o -c /Volumes/Projects/_Code/_Waafer/src/ui/MainWindow.cpp", "file": "/Volumes/Projects/_Code/_Waafer/src/ui/MainWindow.cpp", "output": "CMakeFiles/WAAFer.dir/src/ui/MainWindow.cpp.o"}, {"directory": "/Volumes/Projects/_Code/_Waafer/build", "command": "/usr/bin/c++ -DQT_CORE_LIB -DQT_GUI_LIB -DQT_MULTIMEDIA_LIB -DQT_NETWORK_LIB -DQT_OPENGL_LIB -DQT_QMLINTEGRATION_LIB -DQT_QMLMETA_LIB -DQT_QMLMODELS_LIB -DQT_QMLWORKERSCRIPT_LIB -DQT_QML_LIB -DQT_QUICK_LIB -DQT_WIDGETS_LIB -I/Volumes/Projects/_Code/_Waafer/build/WAAFer_autogen/include -I/Volumes/Projects/_Code/_Waafer/src -I/Volumes/Projects/_Code/_Waafer/third-party/LibAAF/include -I/opt/homebrew/Frameworks/Python.framework/Versions/3.13/include/python3.13 -I/Volumes/Projects/_Code/_Waafer/build/include -isystem /opt/homebrew/lib/QtCore.framework/Headers -iframework /opt/homebrew/lib -isystem /opt/homebrew/share/qt/mkspecs/macx-clang -isystem /opt/homebrew/include -isystem /opt/homebrew/lib/QtWidgets.framework/Headers -isystem /opt/homebrew/lib/QtGui.framework/Headers -isystem /opt/homebrew/lib/QtQml.framework/Headers -isystem /opt/homebrew/include/QtQmlIntegration -isystem /opt/homebrew/lib/QtNetwork.framework/Headers -isystem /opt/homebrew/lib/QtQuick.framework/Headers -isystem /opt/homebrew/lib/QtQmlMeta.framework/Headers -isystem /opt/homebrew/lib/QtQmlModels.framework/Headers -isystem /opt/homebrew/lib/QtQmlWorkerScript.framework/Headers -isystem /opt/homebrew/lib/QtOpenGL.framework/Headers -isystem /opt/homebrew/lib/QtMultimedia.framework/Headers -g -std=gnu++17 -arch arm64 -mmacosx-version-min=11.0 -o CMakeFiles/WAAFer.dir/src/ui/TimelineWidget.cpp.o -c /Volumes/Projects/_Code/_Waafer/src/ui/TimelineWidget.cpp", "file": "/Volumes/Projects/_Code/_Waafer/src/ui/TimelineWidget.cpp", "output": "CMakeFiles/WAAFer.dir/src/ui/TimelineWidget.cpp.o"}, {"directory": "/Volumes/Projects/_Code/_Waafer/build", "command": "/usr/bin/c++ -DQT_CORE_LIB -DQT_GUI_LIB -DQT_MULTIMEDIA_LIB -DQT_NETWORK_LIB -DQT_OPENGL_LIB -DQT_QMLINTEGRATION_LIB -DQT_QMLMETA_LIB -DQT_QMLMODELS_LIB -DQT_QMLWORKERSCRIPT_LIB -DQT_QML_LIB -DQT_QUICK_LIB -DQT_WIDGETS_LIB -I/Volumes/Projects/_Code/_Waafer/build/WAAFer_autogen/include -I/Volumes/Projects/_Code/_Waafer/src -I/Volumes/Projects/_Code/_Waafer/third-party/LibAAF/include -I/opt/homebrew/Frameworks/Python.framework/Versions/3.13/include/python3.13 -I/Volumes/Projects/_Code/_Waafer/build/include -isystem /opt/homebrew/lib/QtCore.framework/Headers -iframework /opt/homebrew/lib -isystem /opt/homebrew/share/qt/mkspecs/macx-clang -isystem /opt/homebrew/include -isystem /opt/homebrew/lib/QtWidgets.framework/Headers -isystem /opt/homebrew/lib/QtGui.framework/Headers -isystem /opt/homebrew/lib/QtQml.framework/Headers -isystem /opt/homebrew/include/QtQmlIntegration -isystem /opt/homebrew/lib/QtNetwork.framework/Headers -isystem /opt/homebrew/lib/QtQuick.framework/Headers -isystem /opt/homebrew/lib/QtQmlMeta.framework/Headers -isystem /opt/homebrew/lib/QtQmlModels.framework/Headers -isystem /opt/homebrew/lib/QtQmlWorkerScript.framework/Headers -isystem /opt/homebrew/lib/QtOpenGL.framework/Headers -isystem /opt/homebrew/lib/QtMultimedia.framework/Headers -g -std=gnu++17 -arch arm64 -mmacosx-version-min=11.0 -o CMakeFiles/WAAFer.dir/src/ui/ClassificationReviewDialog.cpp.o -c /Volumes/Projects/_Code/_Waafer/src/ui/ClassificationReviewDialog.cpp", "file": "/Volumes/Projects/_Code/_Waafer/src/ui/ClassificationReviewDialog.cpp", "output": "CMakeFiles/WAAFer.dir/src/ui/ClassificationReviewDialog.cpp.o"}, {"directory": "/Volumes/Projects/_Code/_Waafer/build", "command": "/usr/bin/c++ -DQT_CORE_LIB -DQT_GUI_LIB -DQT_MULTIMEDIA_LIB -DQT_NETWORK_LIB -DQT_OPENGL_LIB -DQT_QMLINTEGRATION_LIB -DQT_QMLMETA_LIB -DQT_QMLMODELS_LIB -DQT_QMLWORKERSCRIPT_LIB -DQT_QML_LIB -DQT_QUICK_LIB -DQT_WIDGETS_LIB -I/Volumes/Projects/_Code/_Waafer/build/WAAFer_autogen/include -I/Volumes/Projects/_Code/_Waafer/src -I/Volumes/Projects/_Code/_Waafer/third-party/LibAAF/include -I/opt/homebrew/Frameworks/Python.framework/Versions/3.13/include/python3.13 -I/Volumes/Projects/_Code/_Waafer/build/include -isystem /opt/homebrew/lib/QtCore.framework/Headers -iframework /opt/homebrew/lib -isystem /opt/homebrew/share/qt/mkspecs/macx-clang -isystem /opt/homebrew/include -isystem /opt/homebrew/lib/QtWidgets.framework/Headers -isystem /opt/homebrew/lib/QtGui.framework/Headers -isystem /opt/homebrew/lib/QtQml.framework/Headers -isystem /opt/homebrew/include/QtQmlIntegration -isystem /opt/homebrew/lib/QtNetwork.framework/Headers -isystem /opt/homebrew/lib/QtQuick.framework/Headers -isystem /opt/homebrew/lib/QtQmlMeta.framework/Headers -isystem /opt/homebrew/lib/QtQmlModels.framework/Headers -isystem /opt/homebrew/lib/QtQmlWorkerScript.framework/Headers -isystem /opt/homebrew/lib/QtOpenGL.framework/Headers -isystem /opt/homebrew/lib/QtMultimedia.framework/Headers -g -std=gnu++17 -arch arm64 -mmacosx-version-min=11.0 -o CMakeFiles/WAAFer.dir/src/ui/AnalysisSettingsDialog.cpp.o -c /Volumes/Projects/_Code/_Waafer/src/ui/AnalysisSettingsDialog.cpp", "file": "/Volumes/Projects/_Code/_Waafer/src/ui/AnalysisSettingsDialog.cpp", "output": "CMakeFiles/WAAFer.dir/src/ui/AnalysisSettingsDialog.cpp.o"}, {"directory": "/Volumes/Projects/_Code/_Waafer/build", "command": "/usr/bin/c++ -DQT_CORE_LIB -DQT_GUI_LIB -DQT_MULTIMEDIA_LIB -DQT_NETWORK_LIB -DQT_OPENGL_LIB -DQT_QMLINTEGRATION_LIB -DQT_QMLMETA_LIB -DQT_QMLMODELS_LIB -DQT_QMLWORKERSCRIPT_LIB -DQT_QML_LIB -DQT_QUICK_LIB -DQT_WIDGETS_LIB -I/Volumes/Projects/_Code/_Waafer/build/WAAFer_autogen/include -I/Volumes/Projects/_Code/_Waafer/src -I/Volumes/Projects/_Code/_Waafer/third-party/LibAAF/include -I/opt/homebrew/Frameworks/Python.framework/Versions/3.13/include/python3.13 -I/Volumes/Projects/_Code/_Waafer/build/include -isystem /opt/homebrew/lib/QtCore.framework/Headers -iframework /opt/homebrew/lib -isystem /opt/homebrew/share/qt/mkspecs/macx-clang -isystem /opt/homebrew/include -isystem /opt/homebrew/lib/QtWidgets.framework/Headers -isystem /opt/homebrew/lib/QtGui.framework/Headers -isystem /opt/homebrew/lib/QtQml.framework/Headers -isystem /opt/homebrew/include/QtQmlIntegration -isystem /opt/homebrew/lib/QtNetwork.framework/Headers -isystem /opt/homebrew/lib/QtQuick.framework/Headers -isystem /opt/homebrew/lib/QtQmlMeta.framework/Headers -isystem /opt/homebrew/lib/QtQmlModels.framework/Headers -isystem /opt/homebrew/lib/QtQmlWorkerScript.framework/Headers -isystem /opt/homebrew/lib/QtOpenGL.framework/Headers -isystem /opt/homebrew/lib/QtMultimedia.framework/Headers -g -std=gnu++17 -arch arm64 -mmacosx-version-min=11.0 -o CMakeFiles/WAAFer.dir/src/utils/TimecodeUtils.cpp.o -c /Volumes/Projects/_Code/_Waafer/src/utils/TimecodeUtils.cpp", "file": "/Volumes/Projects/_Code/_Waafer/src/utils/TimecodeUtils.cpp", "output": "CMakeFiles/WAAFer.dir/src/utils/TimecodeUtils.cpp.o"}, {"directory": "/Volumes/Projects/_Code/_Waafer/build", "command": "/usr/bin/c++ -DQT_CORE_LIB -DQT_GUI_LIB -DQT_MULTIMEDIA_LIB -DQT_NETWORK_LIB -DQT_OPENGL_LIB -DQT_QMLINTEGRATION_LIB -DQT_QMLMETA_LIB -DQT_QMLMODELS_LIB -DQT_QMLWORKERSCRIPT_LIB -DQT_QML_LIB -DQT_QUICK_LIB -DQT_WIDGETS_LIB -I/Volumes/Projects/_Code/_Waafer/build/WAAFer_autogen/include -I/Volumes/Projects/_Code/_Waafer/src -I/Volumes/Projects/_Code/_Waafer/third-party/LibAAF/include -I/opt/homebrew/Frameworks/Python.framework/Versions/3.13/include/python3.13 -I/Volumes/Projects/_Code/_Waafer/build/include -isystem /opt/homebrew/lib/QtCore.framework/Headers -iframework /opt/homebrew/lib -isystem /opt/homebrew/share/qt/mkspecs/macx-clang -isystem /opt/homebrew/include -isystem /opt/homebrew/lib/QtWidgets.framework/Headers -isystem /opt/homebrew/lib/QtGui.framework/Headers -isystem /opt/homebrew/lib/QtQml.framework/Headers -isystem /opt/homebrew/include/QtQmlIntegration -isystem /opt/homebrew/lib/QtNetwork.framework/Headers -isystem /opt/homebrew/lib/QtQuick.framework/Headers -isystem /opt/homebrew/lib/QtQmlMeta.framework/Headers -isystem /opt/homebrew/lib/QtQmlModels.framework/Headers -isystem /opt/homebrew/lib/QtQmlWorkerScript.framework/Headers -isystem /opt/homebrew/lib/QtOpenGL.framework/Headers -isystem /opt/homebrew/lib/QtMultimedia.framework/Headers -g -std=gnu++17 -arch arm64 -mmacosx-version-min=11.0 -o CMakeFiles/WAAFer.dir/src/ui/PresetManagementDialog.cpp.o -c /Volumes/Projects/_Code/_Waafer/src/ui/PresetManagementDialog.cpp", "file": "/Volumes/Projects/_Code/_Waafer/src/ui/PresetManagementDialog.cpp", "output": "CMakeFiles/WAAFer.dir/src/ui/PresetManagementDialog.cpp.o"}, {"directory": "/Volumes/Projects/_Code/_Waafer/build", "command": "/usr/bin/c++ -DQT_CORE_LIB -DQT_GUI_LIB -DQT_MULTIMEDIA_LIB -DQT_NETWORK_LIB -DQT_OPENGL_LIB -DQT_QMLINTEGRATION_LIB -DQT_QMLMETA_LIB -DQT_QMLMODELS_LIB -DQT_QMLWORKERSCRIPT_LIB -DQT_QML_LIB -DQT_QUICK_LIB -DQT_WIDGETS_LIB -I/Volumes/Projects/_Code/_Waafer/build/WAAFer_autogen/include -I/Volumes/Projects/_Code/_Waafer/src -I/Volumes/Projects/_Code/_Waafer/third-party/LibAAF/include -I/opt/homebrew/Frameworks/Python.framework/Versions/3.13/include/python3.13 -I/Volumes/Projects/_Code/_Waafer/build/include -isystem /opt/homebrew/lib/QtCore.framework/Headers -iframework /opt/homebrew/lib -isystem /opt/homebrew/share/qt/mkspecs/macx-clang -isystem /opt/homebrew/include -isystem /opt/homebrew/lib/QtWidgets.framework/Headers -isystem /opt/homebrew/lib/QtGui.framework/Headers -isystem /opt/homebrew/lib/QtQml.framework/Headers -isystem /opt/homebrew/include/QtQmlIntegration -isystem /opt/homebrew/lib/QtNetwork.framework/Headers -isystem /opt/homebrew/lib/QtQuick.framework/Headers -isystem /opt/homebrew/lib/QtQmlMeta.framework/Headers -isystem /opt/homebrew/lib/QtQmlModels.framework/Headers -isystem /opt/homebrew/lib/QtQmlWorkerScript.framework/Headers -isystem /opt/homebrew/lib/QtOpenGL.framework/Headers -isystem /opt/homebrew/lib/QtMultimedia.framework/Headers -g -std=gnu++17 -arch arm64 -mmacosx-version-min=11.0 -o CMakeFiles/WAAFer.dir/qrc_qml.cpp.o -c /Volumes/Projects/_Code/_Waafer/build/qrc_qml.cpp", "file": "/Volumes/Projects/_Code/_Waafer/build/qrc_qml.cpp", "output": "CMakeFiles/WAAFer.dir/qrc_qml.cpp.o"}, {"directory": "/Volumes/Projects/_Code/_Waafer/build/third-party/LibAAF", "command": "/usr/bin/c++  -I/Volumes/Projects/_Code/_Waafer/build/third-party/LibAAF/aaf-static_autogen/include -I/Volumes/Projects/_Code/_Waafer/third-party/LibAAF/include -I/Volumes/Projects/_Code/_Waafer/build/include -g -std=gnu++17 -arch arm64 -mmacosx-version-min=11.0 -o CMakeFiles/aaf-static.dir/aaf-static_autogen/mocs_compilation.cpp.o -c /Volumes/Projects/_Code/_Waafer/build/third-party/LibAAF/aaf-static_autogen/mocs_compilation.cpp", "file": "/Volumes/Projects/_Code/_Waafer/build/third-party/LibAAF/aaf-static_autogen/mocs_compilation.cpp", "output": "third-party/LibAAF/CMakeFiles/aaf-static.dir/aaf-static_autogen/mocs_compilation.cpp.o"}, {"directory": "/Volumes/Projects/_Code/_Waafer/build/third-party/LibAAF", "command": "/usr/bin/cc  -I/Volumes/Projects/_Code/_Waafer/build/third-party/LibAAF/aaf-static_autogen/include -I/Volumes/Projects/_Code/_Waafer/third-party/LibAAF/include -I/Volumes/Projects/_Code/_Waafer/build/include -g -std=gnu99 -arch arm64 -mmacosx-version-min=11.0 -o CMakeFiles/aaf-static.dir/src/LibCFB/LibCFB.c.o -c /Volumes/Projects/_Code/_Waafer/third-party/LibAAF/src/LibCFB/LibCFB.c", "file": "/Volumes/Projects/_Code/_Waafer/third-party/LibAAF/src/LibCFB/LibCFB.c", "output": "third-party/LibAAF/CMakeFiles/aaf-static.dir/src/LibCFB/LibCFB.c.o"}, {"directory": "/Volumes/Projects/_Code/_Waafer/build/third-party/LibAAF", "command": "/usr/bin/cc  -I/Volumes/Projects/_Code/_Waafer/build/third-party/LibAAF/aaf-static_autogen/include -I/Volumes/Projects/_Code/_Waafer/third-party/LibAAF/include -I/Volumes/Projects/_Code/_Waafer/build/include -g -std=gnu99 -arch arm64 -mmacosx-version-min=11.0 -o CMakeFiles/aaf-static.dir/src/LibCFB/CFBDump.c.o -c /Volumes/Projects/_Code/_Waafer/third-party/LibAAF/src/LibCFB/CFBDump.c", "file": "/Volumes/Projects/_Code/_Waafer/third-party/LibAAF/src/LibCFB/CFBDump.c", "output": "third-party/LibAAF/CMakeFiles/aaf-static.dir/src/LibCFB/CFBDump.c.o"}, {"directory": "/Volumes/Projects/_Code/_Waafer/build/third-party/LibAAF", "command": "/usr/bin/cc  -I/Volumes/Projects/_Code/_Waafer/build/third-party/LibAAF/aaf-static_autogen/include -I/Volumes/Projects/_Code/_Waafer/third-party/LibAAF/include -I/Volumes/Projects/_Code/_Waafer/build/include -g -std=gnu99 -arch arm64 -mmacosx-version-min=11.0 -o CMakeFiles/aaf-static.dir/src/AAFCore/AAFCore.c.o -c /Volumes/Projects/_Code/_Waafer/third-party/LibAAF/src/AAFCore/AAFCore.c", "file": "/Volumes/Projects/_Code/_Waafer/third-party/LibAAF/src/AAFCore/AAFCore.c", "output": "third-party/LibAAF/CMakeFiles/aaf-static.dir/src/AAFCore/AAFCore.c.o"}, {"directory": "/Volumes/Projects/_Code/_Waafer/build/third-party/LibAAF", "command": "/usr/bin/cc  -I/Volumes/Projects/_Code/_Waafer/build/third-party/LibAAF/aaf-static_autogen/include -I/Volumes/Projects/_Code/_Waafer/third-party/LibAAF/include -I/Volumes/Projects/_Code/_Waafer/build/include -g -std=gnu99 -arch arm64 -mmacosx-version-min=11.0 -o CMakeFiles/aaf-static.dir/src/AAFCore/AAFClass.c.o -c /Volumes/Projects/_Code/_Waafer/third-party/LibAAF/src/AAFCore/AAFClass.c", "file": "/Volumes/Projects/_Code/_Waafer/third-party/LibAAF/src/AAFCore/AAFClass.c", "output": "third-party/LibAAF/CMakeFiles/aaf-static.dir/src/AAFCore/AAFClass.c.o"}, {"directory": "/Volumes/Projects/_Code/_Waafer/build/third-party/LibAAF", "command": "/usr/bin/cc  -I/Volumes/Projects/_Code/_Waafer/build/third-party/LibAAF/aaf-static_autogen/include -I/Volumes/Projects/_Code/_Waafer/third-party/LibAAF/include -I/Volumes/Projects/_Code/_Waafer/build/include -g -std=gnu99 -arch arm64 -mmacosx-version-min=11.0 -o CMakeFiles/aaf-static.dir/src/AAFCore/AAFToText.c.o -c /Volumes/Projects/_Code/_Waafer/third-party/LibAAF/src/AAFCore/AAFToText.c", "file": "/Volumes/Projects/_Code/_Waafer/third-party/LibAAF/src/AAFCore/AAFToText.c", "output": "third-party/LibAAF/CMakeFiles/aaf-static.dir/src/AAFCore/AAFToText.c.o"}, {"directory": "/Volumes/Projects/_Code/_Waafer/build/third-party/LibAAF", "command": "/usr/bin/cc  -I/Volumes/Projects/_Code/_Waafer/build/third-party/LibAAF/aaf-static_autogen/include -I/Volumes/Projects/_Code/_Waafer/third-party/LibAAF/include -I/Volumes/Projects/_Code/_Waafer/build/include -g -std=gnu99 -arch arm64 -mmacosx-version-min=11.0 -o CMakeFiles/aaf-static.dir/src/AAFCore/AAFDump.c.o -c /Volumes/Projects/_Code/_Waafer/third-party/LibAAF/src/AAFCore/AAFDump.c", "file": "/Volumes/Projects/_Code/_Waafer/third-party/LibAAF/src/AAFCore/AAFDump.c", "output": "third-party/LibAAF/CMakeFiles/aaf-static.dir/src/AAFCore/AAFDump.c.o"}, {"directory": "/Volumes/Projects/_Code/_Waafer/build/third-party/LibAAF", "command": "/usr/bin/cc  -I/Volumes/Projects/_Code/_Waafer/build/third-party/LibAAF/aaf-static_autogen/include -I/Volumes/Projects/_Code/_Waafer/third-party/LibAAF/include -I/Volumes/Projects/_Code/_Waafer/build/include -g -std=gnu99 -arch arm64 -mmacosx-version-min=11.0 -o CMakeFiles/aaf-static.dir/src/AAFIface/AAFIface.c.o -c /Volumes/Projects/_Code/_Waafer/third-party/LibAAF/src/AAFIface/AAFIface.c", "file": "/Volumes/Projects/_Code/_Waafer/third-party/LibAAF/src/AAFIface/AAFIface.c", "output": "third-party/LibAAF/CMakeFiles/aaf-static.dir/src/AAFIface/AAFIface.c.o"}, {"directory": "/Volumes/Projects/_Code/_Waafer/build/third-party/LibAAF", "command": "/usr/bin/cc  -I/Volumes/Projects/_Code/_Waafer/build/third-party/LibAAF/aaf-static_autogen/include -I/Volumes/Projects/_Code/_Waafer/third-party/LibAAF/include -I/Volumes/Projects/_Code/_Waafer/build/include -g -std=gnu99 -arch arm64 -mmacosx-version-min=11.0 -o CMakeFiles/aaf-static.dir/src/AAFIface/AAFIParser.c.o -c /Volumes/Projects/_Code/_Waafer/third-party/LibAAF/src/AAFIface/AAFIParser.c", "file": "/Volumes/Projects/_Code/_Waafer/third-party/LibAAF/src/AAFIface/AAFIParser.c", "output": "third-party/LibAAF/CMakeFiles/aaf-static.dir/src/AAFIface/AAFIParser.c.o"}, {"directory": "/Volumes/Projects/_Code/_Waafer/build/third-party/LibAAF", "command": "/usr/bin/cc  -I/Volumes/Projects/_Code/_Waafer/build/third-party/LibAAF/aaf-static_autogen/include -I/Volumes/Projects/_Code/_Waafer/third-party/LibAAF/include -I/Volumes/Projects/_Code/_Waafer/build/include -g -std=gnu99 -arch arm64 -mmacosx-version-min=11.0 -o CMakeFiles/aaf-static.dir/src/AAFIface/AAFIEssenceFile.c.o -c /Volumes/Projects/_Code/_Waafer/third-party/LibAAF/src/AAFIface/AAFIEssenceFile.c", "file": "/Volumes/Projects/_Code/_Waafer/third-party/LibAAF/src/AAFIface/AAFIEssenceFile.c", "output": "third-party/LibAAF/CMakeFiles/aaf-static.dir/src/AAFIface/AAFIEssenceFile.c.o"}, {"directory": "/Volumes/Projects/_Code/_Waafer/build/third-party/LibAAF", "command": "/usr/bin/cc  -I/Volumes/Projects/_Code/_Waafer/build/third-party/LibAAF/aaf-static_autogen/include -I/Volumes/Projects/_Code/_Waafer/third-party/LibAAF/include -I/Volumes/Projects/_Code/_Waafer/build/include -g -std=gnu99 -arch arm64 -mmacosx-version-min=11.0 -o CMakeFiles/aaf-static.dir/src/AAFIface/RIFFParser.c.o -c /Volumes/Projects/_Code/_Waafer/third-party/LibAAF/src/AAFIface/RIFFParser.c", "file": "/Volumes/Projects/_Code/_Waafer/third-party/LibAAF/src/AAFIface/RIFFParser.c", "output": "third-party/LibAAF/CMakeFiles/aaf-static.dir/src/AAFIface/RIFFParser.c.o"}, {"directory": "/Volumes/Projects/_Code/_Waafer/build/third-party/LibAAF", "command": "/usr/bin/cc  -I/Volumes/Projects/_Code/_Waafer/build/third-party/LibAAF/aaf-static_autogen/include -I/Volumes/Projects/_Code/_Waafer/third-party/LibAAF/include -I/Volumes/Projects/_Code/_Waafer/build/include -g -std=gnu99 -arch arm64 -mmacosx-version-min=11.0 -o CMakeFiles/aaf-static.dir/src/AAFIface/URIParser.c.o -c /Volumes/Projects/_Code/_Waafer/third-party/LibAAF/src/AAFIface/URIParser.c", "file": "/Volumes/Projects/_Code/_Waafer/third-party/LibAAF/src/AAFIface/URIParser.c", "output": "third-party/LibAAF/CMakeFiles/aaf-static.dir/src/AAFIface/URIParser.c.o"}, {"directory": "/Volumes/Projects/_Code/_Waafer/build/third-party/LibAAF", "command": "/usr/bin/cc  -I/Volumes/Projects/_Code/_Waafer/build/third-party/LibAAF/aaf-static_autogen/include -I/Volumes/Projects/_Code/_Waafer/third-party/LibAAF/include -I/Volumes/Projects/_Code/_Waafer/build/include -g -std=gnu99 -arch arm64 -mmacosx-version-min=11.0 -o CMakeFiles/aaf-static.dir/src/AAFIface/ProTools.c.o -c /Volumes/Projects/_Code/_Waafer/third-party/LibAAF/src/AAFIface/ProTools.c", "file": "/Volumes/Projects/_Code/_Waafer/third-party/LibAAF/src/AAFIface/ProTools.c", "output": "third-party/LibAAF/CMakeFiles/aaf-static.dir/src/AAFIface/ProTools.c.o"}, {"directory": "/Volumes/Projects/_Code/_Waafer/build/third-party/LibAAF", "command": "/usr/bin/cc  -I/Volumes/Projects/_Code/_Waafer/build/third-party/LibAAF/aaf-static_autogen/include -I/Volumes/Projects/_Code/_Waafer/third-party/LibAAF/include -I/Volumes/Projects/_Code/_Waafer/build/include -g -std=gnu99 -arch arm64 -mmacosx-version-min=11.0 -o CMakeFiles/aaf-static.dir/src/AAFIface/Resolve.c.o -c /Volumes/Projects/_Code/_Waafer/third-party/LibAAF/src/AAFIface/Resolve.c", "file": "/Volumes/Projects/_Code/_Waafer/third-party/LibAAF/src/AAFIface/Resolve.c", "output": "third-party/LibAAF/CMakeFiles/aaf-static.dir/src/AAFIface/Resolve.c.o"}, {"directory": "/Volumes/Projects/_Code/_Waafer/build/third-party/LibAAF", "command": "/usr/bin/cc  -I/Volumes/Projects/_Code/_Waafer/build/third-party/LibAAF/aaf-static_autogen/include -I/Volumes/Projects/_Code/_Waafer/third-party/LibAAF/include -I/Volumes/Projects/_Code/_Waafer/build/include -g -std=gnu99 -arch arm64 -mmacosx-version-min=11.0 -o CMakeFiles/aaf-static.dir/src/AAFIface/MediaComposer.c.o -c /Volumes/Projects/_Code/_Waafer/third-party/LibAAF/src/AAFIface/MediaComposer.c", "file": "/Volumes/Projects/_Code/_Waafer/third-party/LibAAF/src/AAFIface/MediaComposer.c", "output": "third-party/LibAAF/CMakeFiles/aaf-static.dir/src/AAFIface/MediaComposer.c.o"}, {"directory": "/Volumes/Projects/_Code/_Waafer/build/third-party/LibAAF", "command": "/usr/bin/cc  -I/Volumes/Projects/_Code/_Waafer/build/third-party/LibAAF/aaf-static_autogen/include -I/Volumes/Projects/_Code/_Waafer/third-party/LibAAF/include -I/Volumes/Projects/_Code/_Waafer/build/include -g -std=gnu99 -arch arm64 -mmacosx-version-min=11.0 -o CMakeFiles/aaf-static.dir/src/common/utils.c.o -c /Volumes/Projects/_Code/_Waafer/third-party/LibAAF/src/common/utils.c", "file": "/Volumes/Projects/_Code/_Waafer/third-party/LibAAF/src/common/utils.c", "output": "third-party/LibAAF/CMakeFiles/aaf-static.dir/src/common/utils.c.o"}, {"directory": "/Volumes/Projects/_Code/_Waafer/build/third-party/LibAAF", "command": "/usr/bin/cc  -I/Volumes/Projects/_Code/_Waafer/build/third-party/LibAAF/aaf-static_autogen/include -I/Volumes/Projects/_Code/_Waafer/third-party/LibAAF/include -I/Volumes/Projects/_Code/_Waafer/build/include -g -std=gnu99 -arch arm64 -mmacosx-version-min=11.0 -o CMakeFiles/aaf-static.dir/src/common/log.c.o -c /Volumes/Projects/_Code/_Waafer/third-party/LibAAF/src/common/log.c", "file": "/Volumes/Projects/_Code/_Waafer/third-party/LibAAF/src/common/log.c", "output": "third-party/LibAAF/CMakeFiles/aaf-static.dir/src/common/log.c.o"}]