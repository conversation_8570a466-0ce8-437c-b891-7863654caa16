{"archive": {}, "artifacts": [{"path": "lib/aaf"}], "backtrace": 1, "backtraceGraph": {"commands": ["add_library", "include_directories", "target_include_directories"], "files": ["third-party/LibAAF/CMakeLists.txt"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 200, "parent": 0}, {"command": 1, "file": 0, "line": 153, "parent": 0}, {"command": 2, "file": 0, "line": 203, "parent": 0}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "-g -std=gnu++17 -arch arm64 -mmacosx-version-min=11.0"}], "includes": [{"backtrace": 0, "path": "/Volumes/Projects/_Code/_Waafer/build/third-party/LibAAF/aaf-static_autogen/include"}, {"backtrace": 2, "path": "/Volumes/Projects/_Code/_Waafer/third-party/LibAAF/include"}, {"backtrace": 3, "path": "/Volumes/Projects/_Code/_Waafer/build/include"}], "language": "CXX", "languageStandard": {"backtraces": [1], "standard": "17"}, "sourceIndexes": [0]}, {"compileCommandFragments": [{"fragment": "-g -std=gnu99 -arch arm64 -mmacosx-version-min=11.0"}], "includes": [{"backtrace": 0, "path": "/Volumes/Projects/_Code/_Waafer/build/third-party/LibAAF/aaf-static_autogen/include"}, {"backtrace": 2, "path": "/Volumes/Projects/_Code/_Waafer/third-party/LibAAF/include"}, {"backtrace": 3, "path": "/Volumes/Projects/_Code/_Waafer/build/include"}], "language": "C", "languageStandard": {"backtraces": [1], "standard": "99"}, "sourceIndexes": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16]}], "dependencies": [{"id": "aaf-static_autogen_timestamp_deps::@bb53bcb17644034f00b0"}, {"backtrace": 0, "id": "aaf-static_autogen::@bb53bcb17644034f00b0"}], "id": "aaf-static::@bb53bcb17644034f00b0", "name": "aaf-static", "nameOnDisk": "aaf", "paths": {"build": "third-party/LibAAF", "source": "third-party/LibAAF"}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16]}, {"name": "", "sourceIndexes": [17]}, {"name": "CMake Rules", "sourceIndexes": [18]}], "sources": [{"backtrace": 0, "compileGroupIndex": 0, "isGenerated": true, "path": "build/third-party/LibAAF/aaf-static_autogen/mocs_compilation.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 1, "path": "third-party/LibAAF/src/LibCFB/LibCFB.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 1, "path": "third-party/LibAAF/src/LibCFB/CFBDump.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 1, "path": "third-party/LibAAF/src/AAFCore/AAFCore.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 1, "path": "third-party/LibAAF/src/AAFCore/AAFClass.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 1, "path": "third-party/LibAAF/src/AAFCore/AAFToText.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 1, "path": "third-party/LibAAF/src/AAFCore/AAFDump.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 1, "path": "third-party/LibAAF/src/AAFIface/AAFIface.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 1, "path": "third-party/LibAAF/src/AAFIface/AAFIParser.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 1, "path": "third-party/LibAAF/src/AAFIface/AAFIEssenceFile.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 1, "path": "third-party/LibAAF/src/AAFIface/RIFFParser.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 1, "path": "third-party/LibAAF/src/AAFIface/URIParser.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 1, "path": "third-party/LibAAF/src/AAFIface/ProTools.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 1, "path": "third-party/LibAAF/src/AAFIface/Resolve.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 1, "path": "third-party/LibAAF/src/AAFIface/MediaComposer.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 1, "path": "third-party/LibAAF/src/common/utils.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 1, "path": "third-party/LibAAF/src/common/log.c", "sourceGroupIndex": 0}, {"backtrace": 0, "isGenerated": true, "path": "build/third-party/LibAAF/aaf-static_autogen/timestamp", "sourceGroupIndex": 1}, {"backtrace": 0, "isGenerated": true, "path": "build/third-party/LibAAF/aaf-static_autogen/timestamp.rule", "sourceGroupIndex": 2}], "type": "STATIC_LIBRARY"}