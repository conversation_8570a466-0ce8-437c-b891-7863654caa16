{"configurations": [{"directories": [{"build": ".", "childIndexes": [1], "hasInstallRule": true, "jsonFile": "directory-.-Debug-096fd681e2fe914b0a4c.json", "minimumCMakeVersion": {"string": "3.16"}, "projectIndex": 0, "source": ".", "targetIndexes": [0, 1, 2, 3]}, {"build": "third-party/LibAAF", "jsonFile": "directory-third-party.LibAAF-Debug-ba9a36a23688b5c92554.json", "minimumCMakeVersion": {"string": "3.18"}, "parentIndex": 0, "projectIndex": 1, "source": "third-party/LibAAF", "targetIndexes": [4, 5, 6, 7, 8, 9]}], "name": "Debug", "projects": [{"childIndexes": [1], "directoryIndexes": [0], "name": "WA<PERSON>er", "targetIndexes": [0, 1, 2, 3]}, {"directoryIndexes": [1], "name": "libaaf", "parentIndex": 0, "targetIndexes": [4, 5, 6, 7, 8, 9]}], "targets": [{"directoryIndex": 0, "id": "WAAFer::@6890427a1f51a3e7e1df", "jsonFile": "target-WAAFer-Debug-1c3840004d62988d7149.json", "name": "WA<PERSON>er", "projectIndex": 0}, {"directoryIndex": 0, "id": "WAAFer_autogen::@6890427a1f51a3e7e1df", "jsonFile": "target-WAAFer_autogen-Debug-c18e70ca0feea803e27b.json", "name": "WAAFer_autogen", "projectIndex": 0}, {"directoryIndex": 0, "id": "WAAFer_autogen_timestamp_deps::@6890427a1f51a3e7e1df", "jsonFile": "target-WAAFer_autogen_timestamp_deps-Debug-6a78e07a74b7df9e02da.json", "name": "WAAFer_autogen_timestamp_deps", "projectIndex": 0}, {"directoryIndex": 0, "id": "WAAFer_qmlimportscan::@6890427a1f51a3e7e1df", "jsonFile": "target-WAAFer_qmlimportscan-Debug-3394b3ec4adbda920046.json", "name": "WAAFer_qmlimportscan", "projectIndex": 0}, {"directoryIndex": 1, "id": "aaf-static::@bb53bcb17644034f00b0", "jsonFile": "target-aaf-static-Debug-24b34d3c03f6a4df6a15.json", "name": "aaf-static", "projectIndex": 1}, {"directoryIndex": 1, "id": "aaf-static_autogen::@bb53bcb17644034f00b0", "jsonFile": "target-aaf-static_autogen-Debug-c044557c8dc1defe8877.json", "name": "aaf-static_autogen", "projectIndex": 1}, {"directoryIndex": 1, "id": "aaf-static_autogen_timestamp_deps::@bb53bcb17644034f00b0", "jsonFile": "target-aaf-static_autogen_timestamp_deps-Debug-e16c68c77032178e8b23.json", "name": "aaf-static_autogen_timestamp_deps", "projectIndex": 1}, {"directoryIndex": 1, "id": "clean-cmake-files::@bb53bcb17644034f00b0", "jsonFile": "target-clean-cmake-files-Debug-04fd3688b7ee12a9bc19.json", "name": "clean-cmake-files", "projectIndex": 1}, {"directoryIndex": 1, "id": "source_release::@bb53bcb17644034f00b0", "jsonFile": "target-source_release-Debug-753b9e585bc760e9aada.json", "name": "source_release", "projectIndex": 1}, {"directoryIndex": 1, "id": "test::@bb53bcb17644034f00b0", "jsonFile": "target-test-Debug-d947518d84f7d6370055.json", "name": "test", "projectIndex": 1}]}], "kind": "codemodel", "paths": {"build": "/Volumes/Projects/_Code/_Waafer/build", "source": "/Volumes/Projects/_Code/_Waafer"}, "version": {"major": 2, "minor": 8}}