{"kind": "toolchains", "toolchains": [{"compiler": {"id": "AppleClang", "implicit": {"includeDirectories": ["/usr/local/include", "/Library/Developer/CommandLineTools/usr/lib/clang/17/include", "/Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/include", "/Library/Developer/CommandLineTools/usr/include"], "linkDirectories": ["/Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/lib", "/Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/lib/swift"], "linkFrameworkDirectories": ["/Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/System/Library/Frameworks"], "linkLibraries": []}, "path": "/usr/bin/cc", "version": "17.0.0.17000013"}, "language": "C", "sourceFileExtensions": ["c", "m"]}, {"compiler": {"id": "AppleClang", "implicit": {"includeDirectories": ["/usr/local/include", "/Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/include/c++/v1", "/Library/Developer/CommandLineTools/usr/lib/clang/17/include", "/Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/include", "/Library/Developer/CommandLineTools/usr/include"], "linkDirectories": ["/Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/lib", "/Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/lib/swift"], "linkFrameworkDirectories": ["/Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/System/Library/Frameworks"], "linkLibraries": ["c++"]}, "path": "/usr/bin/c++", "version": "17.0.0.17000013"}, "language": "CXX", "sourceFileExtensions": ["C", "M", "c++", "cc", "cpp", "cxx", "m", "mm", "mpp", "CPP", "ixx", "cppm", "ccm", "cxxm", "c++m"]}], "version": {"major": 1, "minor": 0}}