{"backtrace": 1, "backtraceGraph": {"commands": ["add_custom_target"], "files": ["third-party/LibAAF/CMakeLists.txt"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 314, "parent": 0}]}, "id": "clean-cmake-files::@bb53bcb17644034f00b0", "name": "clean-cmake-files", "paths": {"build": "third-party/LibAAF", "source": "third-party/LibAAF"}, "sourceGroups": [{"name": "", "sourceIndexes": [0]}, {"name": "CMake Rules", "sourceIndexes": [1]}], "sources": [{"backtrace": 1, "isGenerated": true, "path": "build/third-party/LibAAF/CMakeFiles/clean-cmake-files", "sourceGroupIndex": 0}, {"backtrace": 0, "isGenerated": true, "path": "build/third-party/LibAAF/CMakeFiles/clean-cmake-files.rule", "sourceGroupIndex": 1}], "type": "UTILITY"}