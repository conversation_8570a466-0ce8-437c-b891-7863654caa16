{"backtrace": 0, "backtraceGraph": {"commands": [], "files": ["third-party/LibAAF/CMakeLists.txt"], "nodes": [{"file": 0}]}, "dependencies": [{"id": "aaf-static_autogen_timestamp_deps::@bb53bcb17644034f00b0"}], "id": "aaf-static_autogen::@bb53bcb17644034f00b0", "isGeneratorProvided": true, "name": "aaf-static_autogen", "paths": {"build": "third-party/LibAAF", "source": "third-party/LibAAF"}, "sourceGroups": [{"name": "", "sourceIndexes": [0]}, {"name": "CMake Rules", "sourceIndexes": [1, 2]}], "sources": [{"backtrace": 0, "isGenerated": true, "path": "build/third-party/LibAAF/CMakeFiles/aaf-static_autogen", "sourceGroupIndex": 0}, {"backtrace": 0, "isGenerated": true, "path": "build/third-party/LibAAF/CMakeFiles/aaf-static_autogen.rule", "sourceGroupIndex": 1}, {"backtrace": 0, "isGenerated": true, "path": "build/third-party/LibAAF/aaf-static_autogen/timestamp.rule", "sourceGroupIndex": 1}], "type": "UTILITY"}