/****************************************************************************
** Meta object code from reading C++ file 'LMStudioClient.h'
**
** Created by: The Qt Meta Object Compiler version 69 (Qt 6.9.0)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include "../../../src/ai/LMStudioClient.h"
#include <QtNetwork/QSslError>
#include <QtCore/qmetatype.h>

#include <QtCore/qtmochelpers.h>

#include <memory>


#include <QtCore/qxptype_traits.h>
#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'LMStudioClient.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 69
#error "This file was generated using the moc from 6.9.0. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

#ifndef Q_CONSTINIT
#define Q_CONSTINIT
#endif

QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
QT_WARNING_DISABLE_GCC("-Wuseless-cast")
namespace {
struct qt_meta_tag_ZN14LMStudioClientE_t {};
} // unnamed namespace

template <> constexpr inline auto LMStudioClient::qt_create_metaobjectdata<qt_meta_tag_ZN14LMStudioClientE_t>()
{
    namespace QMC = QtMocConstants;
    QtMocHelpers::StringRefStorage qt_stringData {
        "LMStudioClient",
        "serverStatusChanged",
        "",
        "ServerStatus",
        "status",
        "classificationEnhanced",
        "regionId",
        "QVariantMap",
        "enhancedResult",
        "classificationReviewCompleted",
        "QVariantList",
        "suggestions",
        "organizationSuggestionsReady",
        "errorOccurred",
        "error",
        "handleAvailabilityResponse",
        "handleEnhancementResponse",
        "handleReviewResponse",
        "handleOrganizationResponse",
        "handleNetworkError",
        "QNetworkReply::NetworkError",
        "Unknown",
        "Available",
        "Unavailable",
        "Error",
        "AIProvider",
        "LMStudio",
        "OpenAI",
        "Anthropic"
    };

    QtMocHelpers::UintData qt_methods {
        // Signal 'serverStatusChanged'
        QtMocHelpers::SignalData<void(ServerStatus)>(1, 2, QMC::AccessPublic, QMetaType::Void, {{
            { 0x80000000 | 3, 4 },
        }}),
        // Signal 'classificationEnhanced'
        QtMocHelpers::SignalData<void(const QString &, const QVariantMap &)>(5, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::QString, 6 }, { 0x80000000 | 7, 8 },
        }}),
        // Signal 'classificationReviewCompleted'
        QtMocHelpers::SignalData<void(const QVariantList &)>(9, 2, QMC::AccessPublic, QMetaType::Void, {{
            { 0x80000000 | 10, 11 },
        }}),
        // Signal 'organizationSuggestionsReady'
        QtMocHelpers::SignalData<void(const QVariantList &)>(12, 2, QMC::AccessPublic, QMetaType::Void, {{
            { 0x80000000 | 10, 11 },
        }}),
        // Signal 'errorOccurred'
        QtMocHelpers::SignalData<void(const QString &)>(13, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::QString, 14 },
        }}),
        // Slot 'handleAvailabilityResponse'
        QtMocHelpers::SlotData<void()>(15, 2, QMC::AccessPrivate, QMetaType::Void),
        // Slot 'handleEnhancementResponse'
        QtMocHelpers::SlotData<void()>(16, 2, QMC::AccessPrivate, QMetaType::Void),
        // Slot 'handleReviewResponse'
        QtMocHelpers::SlotData<void()>(17, 2, QMC::AccessPrivate, QMetaType::Void),
        // Slot 'handleOrganizationResponse'
        QtMocHelpers::SlotData<void()>(18, 2, QMC::AccessPrivate, QMetaType::Void),
        // Slot 'handleNetworkError'
        QtMocHelpers::SlotData<void(QNetworkReply::NetworkError)>(19, 2, QMC::AccessPrivate, QMetaType::Void, {{
            { 0x80000000 | 20, 14 },
        }}),
    };
    QtMocHelpers::UintData qt_properties {
    };
    QtMocHelpers::UintData qt_enums {
        // enum 'ServerStatus'
        QtMocHelpers::EnumData<ServerStatus>(3, 3, QMC::EnumFlags{}).add({
            {   21, ServerStatus::Unknown },
            {   22, ServerStatus::Available },
            {   23, ServerStatus::Unavailable },
            {   24, ServerStatus::Error },
        }),
        // enum 'AIProvider'
        QtMocHelpers::EnumData<AIProvider>(25, 25, QMC::EnumFlags{}).add({
            {   26, AIProvider::LMStudio },
            {   27, AIProvider::OpenAI },
            {   28, AIProvider::Anthropic },
        }),
    };
    return QtMocHelpers::metaObjectData<LMStudioClient, qt_meta_tag_ZN14LMStudioClientE_t>(QMC::MetaObjectFlag{}, qt_stringData,
            qt_methods, qt_properties, qt_enums);
}
Q_CONSTINIT const QMetaObject LMStudioClient::staticMetaObject = { {
    QMetaObject::SuperData::link<QObject::staticMetaObject>(),
    qt_staticMetaObjectStaticContent<qt_meta_tag_ZN14LMStudioClientE_t>.stringdata,
    qt_staticMetaObjectStaticContent<qt_meta_tag_ZN14LMStudioClientE_t>.data,
    qt_static_metacall,
    nullptr,
    qt_staticMetaObjectRelocatingContent<qt_meta_tag_ZN14LMStudioClientE_t>.metaTypes,
    nullptr
} };

void LMStudioClient::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    auto *_t = static_cast<LMStudioClient *>(_o);
    if (_c == QMetaObject::InvokeMetaMethod) {
        switch (_id) {
        case 0: _t->serverStatusChanged((*reinterpret_cast< std::add_pointer_t<ServerStatus>>(_a[1]))); break;
        case 1: _t->classificationEnhanced((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<QVariantMap>>(_a[2]))); break;
        case 2: _t->classificationReviewCompleted((*reinterpret_cast< std::add_pointer_t<QVariantList>>(_a[1]))); break;
        case 3: _t->organizationSuggestionsReady((*reinterpret_cast< std::add_pointer_t<QVariantList>>(_a[1]))); break;
        case 4: _t->errorOccurred((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1]))); break;
        case 5: _t->handleAvailabilityResponse(); break;
        case 6: _t->handleEnhancementResponse(); break;
        case 7: _t->handleReviewResponse(); break;
        case 8: _t->handleOrganizationResponse(); break;
        case 9: _t->handleNetworkError((*reinterpret_cast< std::add_pointer_t<QNetworkReply::NetworkError>>(_a[1]))); break;
        default: ;
        }
    }
    if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        switch (_id) {
        default: *reinterpret_cast<QMetaType *>(_a[0]) = QMetaType(); break;
        case 9:
            switch (*reinterpret_cast<int*>(_a[1])) {
            default: *reinterpret_cast<QMetaType *>(_a[0]) = QMetaType(); break;
            case 0:
                *reinterpret_cast<QMetaType *>(_a[0]) = QMetaType::fromType< QNetworkReply::NetworkError >(); break;
            }
            break;
        }
    }
    if (_c == QMetaObject::IndexOfMethod) {
        if (QtMocHelpers::indexOfMethod<void (LMStudioClient::*)(ServerStatus )>(_a, &LMStudioClient::serverStatusChanged, 0))
            return;
        if (QtMocHelpers::indexOfMethod<void (LMStudioClient::*)(const QString & , const QVariantMap & )>(_a, &LMStudioClient::classificationEnhanced, 1))
            return;
        if (QtMocHelpers::indexOfMethod<void (LMStudioClient::*)(const QVariantList & )>(_a, &LMStudioClient::classificationReviewCompleted, 2))
            return;
        if (QtMocHelpers::indexOfMethod<void (LMStudioClient::*)(const QVariantList & )>(_a, &LMStudioClient::organizationSuggestionsReady, 3))
            return;
        if (QtMocHelpers::indexOfMethod<void (LMStudioClient::*)(const QString & )>(_a, &LMStudioClient::errorOccurred, 4))
            return;
    }
}

const QMetaObject *LMStudioClient::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *LMStudioClient::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_staticMetaObjectStaticContent<qt_meta_tag_ZN14LMStudioClientE_t>.strings))
        return static_cast<void*>(this);
    return QObject::qt_metacast(_clname);
}

int LMStudioClient::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QObject::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 10)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 10;
    }
    if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 10)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 10;
    }
    return _id;
}

// SIGNAL 0
void LMStudioClient::serverStatusChanged(ServerStatus _t1)
{
    QMetaObject::activate<void>(this, &staticMetaObject, 0, nullptr, _t1);
}

// SIGNAL 1
void LMStudioClient::classificationEnhanced(const QString & _t1, const QVariantMap & _t2)
{
    QMetaObject::activate<void>(this, &staticMetaObject, 1, nullptr, _t1, _t2);
}

// SIGNAL 2
void LMStudioClient::classificationReviewCompleted(const QVariantList & _t1)
{
    QMetaObject::activate<void>(this, &staticMetaObject, 2, nullptr, _t1);
}

// SIGNAL 3
void LMStudioClient::organizationSuggestionsReady(const QVariantList & _t1)
{
    QMetaObject::activate<void>(this, &staticMetaObject, 3, nullptr, _t1);
}

// SIGNAL 4
void LMStudioClient::errorOccurred(const QString & _t1)
{
    QMetaObject::activate<void>(this, &staticMetaObject, 4, nullptr, _t1);
}
QT_WARNING_POP
