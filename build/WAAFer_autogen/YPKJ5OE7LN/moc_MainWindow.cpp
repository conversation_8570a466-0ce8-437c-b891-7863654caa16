/****************************************************************************
** Meta object code from reading C++ file 'MainWindow.h'
**
** Created by: The Qt Meta Object Compiler version 69 (Qt 6.9.0)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include "../../../src/ui/MainWindow.h"
#include <QtGui/qtextcursor.h>
#include <QtNetwork/QSslError>
#include <QtCore/qmetatype.h>

#include <QtCore/qtmochelpers.h>

#include <memory>


#include <QtCore/qxptype_traits.h>
#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'MainWindow.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 69
#error "This file was generated using the moc from 6.9.0. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

#ifndef Q_CONSTINIT
#define Q_CONSTINIT
#endif

QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
QT_WARNING_DISABLE_GCC("-Wuseless-cast")
namespace {
struct qt_meta_tag_ZN10MainWindowE_t {};
} // unnamed namespace

template <> constexpr inline auto MainWindow::qt_create_metaobjectdata<qt_meta_tag_ZN10MainWindowE_t>()
{
    namespace QMC = QtMocConstants;
    QtMocHelpers::StringRefStorage qt_stringData {
        "MainWindow",
        "loadAAFFile",
        "",
        "filePath",
        "forceWindowVisible",
        "createTestWindow",
        "QWidget*",
        "openAAFFile",
        "closeAAFFile",
        "testPythonIntegration",
        "testAudioLibraries",
        "showMemoryStats",
        "updateStatus",
        "onAAFFileLoadCompleted",
        "success",
        "message",
        "onAAFParsingProgress",
        "progress",
        "status",
        "onPythonOperationCompleted",
        "onMemoryWarning",
        "usage",
        "onProgressChanged",
        "onProgressTaskChanged",
        "onProgressTimeChanged",
        "pauseAnalysis",
        "stopAnalysis",
        "resumeAnalysis",
        "startClassification",
        "onClassificationProgressChanged",
        "onClassificationCompleted",
        "QVariantList",
        "results",
        "onClassificationError",
        "regionId",
        "error",
        "applyAnalysisSettings",
        "resetAnalysisSettings",
        "onTimeRangeChanged",
        "setTimeRangeFromTimeline",
        "clearTimeRange",
        "onExportTimeRangeModeChanged",
        "updateExportFileName",
        "previewTrackOrganization",
        "applyTrackOrganization",
        "openPresetManagement",
        "onOrganizationCompleted",
        "assignments",
        "QVariantMap",
        "stats",
        "openClassificationReview",
        "onClassificationsUpdated",
        "updatedResults",
        "onLLMProviderChanged",
        "openLLMSettings",
        "browseExportPath",
        "updateExportButtonState",
        "startExport",
        "onExportFormatChanged",
        "openAnalysisSettings",
        "onTimelineRegionSelected",
        "onTimelineRegionUpdated",
        "metadata",
        "onTimelinePlaybackRequested"
    };

    QtMocHelpers::UintData qt_methods {
        // Slot 'loadAAFFile'
        QtMocHelpers::SlotData<void(const QString &)>(1, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::QString, 3 },
        }}),
        // Slot 'forceWindowVisible'
        QtMocHelpers::SlotData<void()>(4, 2, QMC::AccessPublic, QMetaType::Void),
        // Slot 'createTestWindow'
        QtMocHelpers::SlotData<QWidget *()>(5, 2, QMC::AccessPublic, 0x80000000 | 6),
        // Slot 'openAAFFile'
        QtMocHelpers::SlotData<void()>(7, 2, QMC::AccessPrivate, QMetaType::Void),
        // Slot 'closeAAFFile'
        QtMocHelpers::SlotData<void()>(8, 2, QMC::AccessPrivate, QMetaType::Void),
        // Slot 'testPythonIntegration'
        QtMocHelpers::SlotData<void()>(9, 2, QMC::AccessPrivate, QMetaType::Void),
        // Slot 'testAudioLibraries'
        QtMocHelpers::SlotData<void()>(10, 2, QMC::AccessPrivate, QMetaType::Void),
        // Slot 'showMemoryStats'
        QtMocHelpers::SlotData<void()>(11, 2, QMC::AccessPrivate, QMetaType::Void),
        // Slot 'updateStatus'
        QtMocHelpers::SlotData<void()>(12, 2, QMC::AccessPrivate, QMetaType::Void),
        // Slot 'onAAFFileLoadCompleted'
        QtMocHelpers::SlotData<void(bool, const QString &)>(13, 2, QMC::AccessPrivate, QMetaType::Void, {{
            { QMetaType::Bool, 14 }, { QMetaType::QString, 15 },
        }}),
        // Slot 'onAAFParsingProgress'
        QtMocHelpers::SlotData<void(int, const QString &)>(16, 2, QMC::AccessPrivate, QMetaType::Void, {{
            { QMetaType::Int, 17 }, { QMetaType::QString, 18 },
        }}),
        // Slot 'onPythonOperationCompleted'
        QtMocHelpers::SlotData<void(bool, const QString &)>(19, 2, QMC::AccessPrivate, QMetaType::Void, {{
            { QMetaType::Bool, 14 }, { QMetaType::QString, 15 },
        }}),
        // Slot 'onMemoryWarning'
        QtMocHelpers::SlotData<void(int, const QString &)>(20, 2, QMC::AccessPrivate, QMetaType::Void, {{
            { QMetaType::Int, 21 }, { QMetaType::QString, 15 },
        }}),
        // Slot 'onProgressChanged'
        QtMocHelpers::SlotData<void()>(22, 2, QMC::AccessPrivate, QMetaType::Void),
        // Slot 'onProgressTaskChanged'
        QtMocHelpers::SlotData<void()>(23, 2, QMC::AccessPrivate, QMetaType::Void),
        // Slot 'onProgressTimeChanged'
        QtMocHelpers::SlotData<void()>(24, 2, QMC::AccessPrivate, QMetaType::Void),
        // Slot 'pauseAnalysis'
        QtMocHelpers::SlotData<void()>(25, 2, QMC::AccessPrivate, QMetaType::Void),
        // Slot 'stopAnalysis'
        QtMocHelpers::SlotData<void()>(26, 2, QMC::AccessPrivate, QMetaType::Void),
        // Slot 'resumeAnalysis'
        QtMocHelpers::SlotData<void()>(27, 2, QMC::AccessPrivate, QMetaType::Void),
        // Slot 'startClassification'
        QtMocHelpers::SlotData<void()>(28, 2, QMC::AccessPrivate, QMetaType::Void),
        // Slot 'onClassificationProgressChanged'
        QtMocHelpers::SlotData<void()>(29, 2, QMC::AccessPrivate, QMetaType::Void),
        // Slot 'onClassificationCompleted'
        QtMocHelpers::SlotData<void(const QVariantList &)>(30, 2, QMC::AccessPrivate, QMetaType::Void, {{
            { 0x80000000 | 31, 32 },
        }}),
        // Slot 'onClassificationError'
        QtMocHelpers::SlotData<void(const QString &, const QString &)>(33, 2, QMC::AccessPrivate, QMetaType::Void, {{
            { QMetaType::QString, 34 }, { QMetaType::QString, 35 },
        }}),
        // Slot 'applyAnalysisSettings'
        QtMocHelpers::SlotData<void()>(36, 2, QMC::AccessPrivate, QMetaType::Void),
        // Slot 'resetAnalysisSettings'
        QtMocHelpers::SlotData<void()>(37, 2, QMC::AccessPrivate, QMetaType::Void),
        // Slot 'onTimeRangeChanged'
        QtMocHelpers::SlotData<void()>(38, 2, QMC::AccessPrivate, QMetaType::Void),
        // Slot 'setTimeRangeFromTimeline'
        QtMocHelpers::SlotData<void()>(39, 2, QMC::AccessPrivate, QMetaType::Void),
        // Slot 'clearTimeRange'
        QtMocHelpers::SlotData<void()>(40, 2, QMC::AccessPrivate, QMetaType::Void),
        // Slot 'onExportTimeRangeModeChanged'
        QtMocHelpers::SlotData<void()>(41, 2, QMC::AccessPrivate, QMetaType::Void),
        // Slot 'updateExportFileName'
        QtMocHelpers::SlotData<void()>(42, 2, QMC::AccessPrivate, QMetaType::Void),
        // Slot 'previewTrackOrganization'
        QtMocHelpers::SlotData<void()>(43, 2, QMC::AccessPrivate, QMetaType::Void),
        // Slot 'applyTrackOrganization'
        QtMocHelpers::SlotData<void()>(44, 2, QMC::AccessPrivate, QMetaType::Void),
        // Slot 'openPresetManagement'
        QtMocHelpers::SlotData<void()>(45, 2, QMC::AccessPrivate, QMetaType::Void),
        // Slot 'onOrganizationCompleted'
        QtMocHelpers::SlotData<void(const QVariantList &, const QVariantMap &)>(46, 2, QMC::AccessPrivate, QMetaType::Void, {{
            { 0x80000000 | 31, 47 }, { 0x80000000 | 48, 49 },
        }}),
        // Slot 'openClassificationReview'
        QtMocHelpers::SlotData<void()>(50, 2, QMC::AccessPrivate, QMetaType::Void),
        // Slot 'onClassificationsUpdated'
        QtMocHelpers::SlotData<void(const QVariantList &)>(51, 2, QMC::AccessPrivate, QMetaType::Void, {{
            { 0x80000000 | 31, 52 },
        }}),
        // Slot 'onLLMProviderChanged'
        QtMocHelpers::SlotData<void()>(53, 2, QMC::AccessPrivate, QMetaType::Void),
        // Slot 'openLLMSettings'
        QtMocHelpers::SlotData<void()>(54, 2, QMC::AccessPrivate, QMetaType::Void),
        // Slot 'browseExportPath'
        QtMocHelpers::SlotData<void()>(55, 2, QMC::AccessPrivate, QMetaType::Void),
        // Slot 'updateExportButtonState'
        QtMocHelpers::SlotData<void()>(56, 2, QMC::AccessPrivate, QMetaType::Void),
        // Slot 'startExport'
        QtMocHelpers::SlotData<void()>(57, 2, QMC::AccessPrivate, QMetaType::Void),
        // Slot 'onExportFormatChanged'
        QtMocHelpers::SlotData<void()>(58, 2, QMC::AccessPrivate, QMetaType::Void),
        // Slot 'openAnalysisSettings'
        QtMocHelpers::SlotData<void()>(59, 2, QMC::AccessPrivate, QMetaType::Void),
        // Slot 'onTimelineRegionSelected'
        QtMocHelpers::SlotData<void(const QString &)>(60, 2, QMC::AccessPrivate, QMetaType::Void, {{
            { QMetaType::QString, 34 },
        }}),
        // Slot 'onTimelineRegionUpdated'
        QtMocHelpers::SlotData<void(const QString &, const QVariantMap &)>(61, 2, QMC::AccessPrivate, QMetaType::Void, {{
            { QMetaType::QString, 34 }, { 0x80000000 | 48, 62 },
        }}),
        // Slot 'onTimelinePlaybackRequested'
        QtMocHelpers::SlotData<void(const QString &)>(63, 2, QMC::AccessPrivate, QMetaType::Void, {{
            { QMetaType::QString, 34 },
        }}),
    };
    QtMocHelpers::UintData qt_properties {
    };
    QtMocHelpers::UintData qt_enums {
    };
    return QtMocHelpers::metaObjectData<MainWindow, qt_meta_tag_ZN10MainWindowE_t>(QMC::MetaObjectFlag{}, qt_stringData,
            qt_methods, qt_properties, qt_enums);
}
Q_CONSTINIT const QMetaObject MainWindow::staticMetaObject = { {
    QMetaObject::SuperData::link<QMainWindow::staticMetaObject>(),
    qt_staticMetaObjectStaticContent<qt_meta_tag_ZN10MainWindowE_t>.stringdata,
    qt_staticMetaObjectStaticContent<qt_meta_tag_ZN10MainWindowE_t>.data,
    qt_static_metacall,
    nullptr,
    qt_staticMetaObjectRelocatingContent<qt_meta_tag_ZN10MainWindowE_t>.metaTypes,
    nullptr
} };

void MainWindow::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    auto *_t = static_cast<MainWindow *>(_o);
    if (_c == QMetaObject::InvokeMetaMethod) {
        switch (_id) {
        case 0: _t->loadAAFFile((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1]))); break;
        case 1: _t->forceWindowVisible(); break;
        case 2: { QWidget* _r = _t->createTestWindow();
            if (_a[0]) *reinterpret_cast< QWidget**>(_a[0]) = std::move(_r); }  break;
        case 3: _t->openAAFFile(); break;
        case 4: _t->closeAAFFile(); break;
        case 5: _t->testPythonIntegration(); break;
        case 6: _t->testAudioLibraries(); break;
        case 7: _t->showMemoryStats(); break;
        case 8: _t->updateStatus(); break;
        case 9: _t->onAAFFileLoadCompleted((*reinterpret_cast< std::add_pointer_t<bool>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<QString>>(_a[2]))); break;
        case 10: _t->onAAFParsingProgress((*reinterpret_cast< std::add_pointer_t<int>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<QString>>(_a[2]))); break;
        case 11: _t->onPythonOperationCompleted((*reinterpret_cast< std::add_pointer_t<bool>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<QString>>(_a[2]))); break;
        case 12: _t->onMemoryWarning((*reinterpret_cast< std::add_pointer_t<int>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<QString>>(_a[2]))); break;
        case 13: _t->onProgressChanged(); break;
        case 14: _t->onProgressTaskChanged(); break;
        case 15: _t->onProgressTimeChanged(); break;
        case 16: _t->pauseAnalysis(); break;
        case 17: _t->stopAnalysis(); break;
        case 18: _t->resumeAnalysis(); break;
        case 19: _t->startClassification(); break;
        case 20: _t->onClassificationProgressChanged(); break;
        case 21: _t->onClassificationCompleted((*reinterpret_cast< std::add_pointer_t<QVariantList>>(_a[1]))); break;
        case 22: _t->onClassificationError((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<QString>>(_a[2]))); break;
        case 23: _t->applyAnalysisSettings(); break;
        case 24: _t->resetAnalysisSettings(); break;
        case 25: _t->onTimeRangeChanged(); break;
        case 26: _t->setTimeRangeFromTimeline(); break;
        case 27: _t->clearTimeRange(); break;
        case 28: _t->onExportTimeRangeModeChanged(); break;
        case 29: _t->updateExportFileName(); break;
        case 30: _t->previewTrackOrganization(); break;
        case 31: _t->applyTrackOrganization(); break;
        case 32: _t->openPresetManagement(); break;
        case 33: _t->onOrganizationCompleted((*reinterpret_cast< std::add_pointer_t<QVariantList>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<QVariantMap>>(_a[2]))); break;
        case 34: _t->openClassificationReview(); break;
        case 35: _t->onClassificationsUpdated((*reinterpret_cast< std::add_pointer_t<QVariantList>>(_a[1]))); break;
        case 36: _t->onLLMProviderChanged(); break;
        case 37: _t->openLLMSettings(); break;
        case 38: _t->browseExportPath(); break;
        case 39: _t->updateExportButtonState(); break;
        case 40: _t->startExport(); break;
        case 41: _t->onExportFormatChanged(); break;
        case 42: _t->openAnalysisSettings(); break;
        case 43: _t->onTimelineRegionSelected((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1]))); break;
        case 44: _t->onTimelineRegionUpdated((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<QVariantMap>>(_a[2]))); break;
        case 45: _t->onTimelinePlaybackRequested((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1]))); break;
        default: ;
        }
    }
}

const QMetaObject *MainWindow::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *MainWindow::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_staticMetaObjectStaticContent<qt_meta_tag_ZN10MainWindowE_t>.strings))
        return static_cast<void*>(this);
    return QMainWindow::qt_metacast(_clname);
}

int MainWindow::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QMainWindow::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 46)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 46;
    }
    if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 46)
            *reinterpret_cast<QMetaType *>(_a[0]) = QMetaType();
        _id -= 46;
    }
    return _id;
}
QT_WARNING_POP
