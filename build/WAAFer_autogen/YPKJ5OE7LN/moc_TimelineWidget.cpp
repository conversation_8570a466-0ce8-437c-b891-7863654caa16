/****************************************************************************
** Meta object code from reading C++ file 'TimelineWidget.h'
**
** Created by: The Qt Meta Object Compiler version 69 (Qt 6.9.0)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include "../../../src/ui/TimelineWidget.h"
#include <QtGui/qtextcursor.h>
#include <QtCore/qmetatype.h>

#include <QtCore/qtmochelpers.h>

#include <memory>


#include <QtCore/qxptype_traits.h>
#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'TimelineWidget.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 69
#error "This file was generated using the moc from 6.9.0. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

#ifndef Q_CONSTINIT
#define Q_CONSTINIT
#endif

QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
QT_WARNING_DISABLE_GCC("-Wuseless-cast")
namespace {
struct qt_meta_tag_ZN13TimelineRulerE_t {};
} // unnamed namespace

template <> constexpr inline auto TimelineRuler::qt_create_metaobjectdata<qt_meta_tag_ZN13TimelineRulerE_t>()
{
    namespace QMC = QtMocConstants;
    QtMocHelpers::StringRefStorage qt_stringData {
        "TimelineRuler",
        "playheadMoved",
        "",
        "position",
        "zoomRequested",
        "factor"
    };

    QtMocHelpers::UintData qt_methods {
        // Signal 'playheadMoved'
        QtMocHelpers::SignalData<void(double)>(1, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::Double, 3 },
        }}),
        // Signal 'zoomRequested'
        QtMocHelpers::SignalData<void(double)>(4, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::Double, 5 },
        }}),
    };
    QtMocHelpers::UintData qt_properties {
    };
    QtMocHelpers::UintData qt_enums {
    };
    return QtMocHelpers::metaObjectData<TimelineRuler, qt_meta_tag_ZN13TimelineRulerE_t>(QMC::MetaObjectFlag{}, qt_stringData,
            qt_methods, qt_properties, qt_enums);
}
Q_CONSTINIT const QMetaObject TimelineRuler::staticMetaObject = { {
    QMetaObject::SuperData::link<QWidget::staticMetaObject>(),
    qt_staticMetaObjectStaticContent<qt_meta_tag_ZN13TimelineRulerE_t>.stringdata,
    qt_staticMetaObjectStaticContent<qt_meta_tag_ZN13TimelineRulerE_t>.data,
    qt_static_metacall,
    nullptr,
    qt_staticMetaObjectRelocatingContent<qt_meta_tag_ZN13TimelineRulerE_t>.metaTypes,
    nullptr
} };

void TimelineRuler::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    auto *_t = static_cast<TimelineRuler *>(_o);
    if (_c == QMetaObject::InvokeMetaMethod) {
        switch (_id) {
        case 0: _t->playheadMoved((*reinterpret_cast< std::add_pointer_t<double>>(_a[1]))); break;
        case 1: _t->zoomRequested((*reinterpret_cast< std::add_pointer_t<double>>(_a[1]))); break;
        default: ;
        }
    }
    if (_c == QMetaObject::IndexOfMethod) {
        if (QtMocHelpers::indexOfMethod<void (TimelineRuler::*)(double )>(_a, &TimelineRuler::playheadMoved, 0))
            return;
        if (QtMocHelpers::indexOfMethod<void (TimelineRuler::*)(double )>(_a, &TimelineRuler::zoomRequested, 1))
            return;
    }
}

const QMetaObject *TimelineRuler::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *TimelineRuler::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_staticMetaObjectStaticContent<qt_meta_tag_ZN13TimelineRulerE_t>.strings))
        return static_cast<void*>(this);
    return QWidget::qt_metacast(_clname);
}

int TimelineRuler::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QWidget::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 2)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 2;
    }
    if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 2)
            *reinterpret_cast<QMetaType *>(_a[0]) = QMetaType();
        _id -= 2;
    }
    return _id;
}

// SIGNAL 0
void TimelineRuler::playheadMoved(double _t1)
{
    QMetaObject::activate<void>(this, &staticMetaObject, 0, nullptr, _t1);
}

// SIGNAL 1
void TimelineRuler::zoomRequested(double _t1)
{
    QMetaObject::activate<void>(this, &staticMetaObject, 1, nullptr, _t1);
}
namespace {
struct qt_meta_tag_ZN11TrackHeaderE_t {};
} // unnamed namespace

template <> constexpr inline auto TrackHeader::qt_create_metaobjectdata<qt_meta_tag_ZN11TrackHeaderE_t>()
{
    namespace QMC = QtMocConstants;
    QtMocHelpers::StringRefStorage qt_stringData {
        "TrackHeader",
        "muteToggled",
        "",
        "trackName",
        "muted",
        "soloToggled",
        "solo",
        "visibilityToggled",
        "visible",
        "trackRenamed",
        "oldName",
        "newName",
        "selectionToggled",
        "selected",
        "trackHeightChanged",
        "height",
        "onMuteClicked",
        "onSoloClicked",
        "onVisibilityClicked",
        "onSelectionClicked",
        "onNameEditFinished"
    };

    QtMocHelpers::UintData qt_methods {
        // Signal 'muteToggled'
        QtMocHelpers::SignalData<void(const QString &, bool)>(1, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::QString, 3 }, { QMetaType::Bool, 4 },
        }}),
        // Signal 'soloToggled'
        QtMocHelpers::SignalData<void(const QString &, bool)>(5, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::QString, 3 }, { QMetaType::Bool, 6 },
        }}),
        // Signal 'visibilityToggled'
        QtMocHelpers::SignalData<void(const QString &, bool)>(7, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::QString, 3 }, { QMetaType::Bool, 8 },
        }}),
        // Signal 'trackRenamed'
        QtMocHelpers::SignalData<void(const QString &, const QString &)>(9, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::QString, 10 }, { QMetaType::QString, 11 },
        }}),
        // Signal 'selectionToggled'
        QtMocHelpers::SignalData<void(const QString &, bool)>(12, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::QString, 3 }, { QMetaType::Bool, 13 },
        }}),
        // Signal 'trackHeightChanged'
        QtMocHelpers::SignalData<void(const QString &, int)>(14, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::QString, 3 }, { QMetaType::Int, 15 },
        }}),
        // Slot 'onMuteClicked'
        QtMocHelpers::SlotData<void()>(16, 2, QMC::AccessPrivate, QMetaType::Void),
        // Slot 'onSoloClicked'
        QtMocHelpers::SlotData<void()>(17, 2, QMC::AccessPrivate, QMetaType::Void),
        // Slot 'onVisibilityClicked'
        QtMocHelpers::SlotData<void()>(18, 2, QMC::AccessPrivate, QMetaType::Void),
        // Slot 'onSelectionClicked'
        QtMocHelpers::SlotData<void()>(19, 2, QMC::AccessPrivate, QMetaType::Void),
        // Slot 'onNameEditFinished'
        QtMocHelpers::SlotData<void()>(20, 2, QMC::AccessPrivate, QMetaType::Void),
    };
    QtMocHelpers::UintData qt_properties {
    };
    QtMocHelpers::UintData qt_enums {
    };
    return QtMocHelpers::metaObjectData<TrackHeader, qt_meta_tag_ZN11TrackHeaderE_t>(QMC::MetaObjectFlag{}, qt_stringData,
            qt_methods, qt_properties, qt_enums);
}
Q_CONSTINIT const QMetaObject TrackHeader::staticMetaObject = { {
    QMetaObject::SuperData::link<QWidget::staticMetaObject>(),
    qt_staticMetaObjectStaticContent<qt_meta_tag_ZN11TrackHeaderE_t>.stringdata,
    qt_staticMetaObjectStaticContent<qt_meta_tag_ZN11TrackHeaderE_t>.data,
    qt_static_metacall,
    nullptr,
    qt_staticMetaObjectRelocatingContent<qt_meta_tag_ZN11TrackHeaderE_t>.metaTypes,
    nullptr
} };

void TrackHeader::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    auto *_t = static_cast<TrackHeader *>(_o);
    if (_c == QMetaObject::InvokeMetaMethod) {
        switch (_id) {
        case 0: _t->muteToggled((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<bool>>(_a[2]))); break;
        case 1: _t->soloToggled((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<bool>>(_a[2]))); break;
        case 2: _t->visibilityToggled((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<bool>>(_a[2]))); break;
        case 3: _t->trackRenamed((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<QString>>(_a[2]))); break;
        case 4: _t->selectionToggled((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<bool>>(_a[2]))); break;
        case 5: _t->trackHeightChanged((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<int>>(_a[2]))); break;
        case 6: _t->onMuteClicked(); break;
        case 7: _t->onSoloClicked(); break;
        case 8: _t->onVisibilityClicked(); break;
        case 9: _t->onSelectionClicked(); break;
        case 10: _t->onNameEditFinished(); break;
        default: ;
        }
    }
    if (_c == QMetaObject::IndexOfMethod) {
        if (QtMocHelpers::indexOfMethod<void (TrackHeader::*)(const QString & , bool )>(_a, &TrackHeader::muteToggled, 0))
            return;
        if (QtMocHelpers::indexOfMethod<void (TrackHeader::*)(const QString & , bool )>(_a, &TrackHeader::soloToggled, 1))
            return;
        if (QtMocHelpers::indexOfMethod<void (TrackHeader::*)(const QString & , bool )>(_a, &TrackHeader::visibilityToggled, 2))
            return;
        if (QtMocHelpers::indexOfMethod<void (TrackHeader::*)(const QString & , const QString & )>(_a, &TrackHeader::trackRenamed, 3))
            return;
        if (QtMocHelpers::indexOfMethod<void (TrackHeader::*)(const QString & , bool )>(_a, &TrackHeader::selectionToggled, 4))
            return;
        if (QtMocHelpers::indexOfMethod<void (TrackHeader::*)(const QString & , int )>(_a, &TrackHeader::trackHeightChanged, 5))
            return;
    }
}

const QMetaObject *TrackHeader::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *TrackHeader::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_staticMetaObjectStaticContent<qt_meta_tag_ZN11TrackHeaderE_t>.strings))
        return static_cast<void*>(this);
    return QWidget::qt_metacast(_clname);
}

int TrackHeader::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QWidget::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 11)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 11;
    }
    if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 11)
            *reinterpret_cast<QMetaType *>(_a[0]) = QMetaType();
        _id -= 11;
    }
    return _id;
}

// SIGNAL 0
void TrackHeader::muteToggled(const QString & _t1, bool _t2)
{
    QMetaObject::activate<void>(this, &staticMetaObject, 0, nullptr, _t1, _t2);
}

// SIGNAL 1
void TrackHeader::soloToggled(const QString & _t1, bool _t2)
{
    QMetaObject::activate<void>(this, &staticMetaObject, 1, nullptr, _t1, _t2);
}

// SIGNAL 2
void TrackHeader::visibilityToggled(const QString & _t1, bool _t2)
{
    QMetaObject::activate<void>(this, &staticMetaObject, 2, nullptr, _t1, _t2);
}

// SIGNAL 3
void TrackHeader::trackRenamed(const QString & _t1, const QString & _t2)
{
    QMetaObject::activate<void>(this, &staticMetaObject, 3, nullptr, _t1, _t2);
}

// SIGNAL 4
void TrackHeader::selectionToggled(const QString & _t1, bool _t2)
{
    QMetaObject::activate<void>(this, &staticMetaObject, 4, nullptr, _t1, _t2);
}

// SIGNAL 5
void TrackHeader::trackHeightChanged(const QString & _t1, int _t2)
{
    QMetaObject::activate<void>(this, &staticMetaObject, 5, nullptr, _t1, _t2);
}
namespace {
struct qt_meta_tag_ZN12TrackContentE_t {};
} // unnamed namespace

template <> constexpr inline auto TrackContent::qt_create_metaobjectdata<qt_meta_tag_ZN12TrackContentE_t>()
{
    namespace QMC = QtMocConstants;
    QtMocHelpers::StringRefStorage qt_stringData {
        "TrackContent",
        "regionSelected",
        "",
        "regionId",
        "regionMoved",
        "newStartTime",
        "regionContextMenu",
        "position",
        "zoomRequested",
        "factor"
    };

    QtMocHelpers::UintData qt_methods {
        // Signal 'regionSelected'
        QtMocHelpers::SignalData<void(const QString &)>(1, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::QString, 3 },
        }}),
        // Signal 'regionMoved'
        QtMocHelpers::SignalData<void(const QString &, double)>(4, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::QString, 3 }, { QMetaType::Double, 5 },
        }}),
        // Signal 'regionContextMenu'
        QtMocHelpers::SignalData<void(const QString &, const QPoint &)>(6, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::QString, 3 }, { QMetaType::QPoint, 7 },
        }}),
        // Signal 'zoomRequested'
        QtMocHelpers::SignalData<void(double)>(8, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::Double, 9 },
        }}),
    };
    QtMocHelpers::UintData qt_properties {
    };
    QtMocHelpers::UintData qt_enums {
    };
    return QtMocHelpers::metaObjectData<TrackContent, qt_meta_tag_ZN12TrackContentE_t>(QMC::MetaObjectFlag{}, qt_stringData,
            qt_methods, qt_properties, qt_enums);
}
Q_CONSTINIT const QMetaObject TrackContent::staticMetaObject = { {
    QMetaObject::SuperData::link<QWidget::staticMetaObject>(),
    qt_staticMetaObjectStaticContent<qt_meta_tag_ZN12TrackContentE_t>.stringdata,
    qt_staticMetaObjectStaticContent<qt_meta_tag_ZN12TrackContentE_t>.data,
    qt_static_metacall,
    nullptr,
    qt_staticMetaObjectRelocatingContent<qt_meta_tag_ZN12TrackContentE_t>.metaTypes,
    nullptr
} };

void TrackContent::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    auto *_t = static_cast<TrackContent *>(_o);
    if (_c == QMetaObject::InvokeMetaMethod) {
        switch (_id) {
        case 0: _t->regionSelected((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1]))); break;
        case 1: _t->regionMoved((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<double>>(_a[2]))); break;
        case 2: _t->regionContextMenu((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<QPoint>>(_a[2]))); break;
        case 3: _t->zoomRequested((*reinterpret_cast< std::add_pointer_t<double>>(_a[1]))); break;
        default: ;
        }
    }
    if (_c == QMetaObject::IndexOfMethod) {
        if (QtMocHelpers::indexOfMethod<void (TrackContent::*)(const QString & )>(_a, &TrackContent::regionSelected, 0))
            return;
        if (QtMocHelpers::indexOfMethod<void (TrackContent::*)(const QString & , double )>(_a, &TrackContent::regionMoved, 1))
            return;
        if (QtMocHelpers::indexOfMethod<void (TrackContent::*)(const QString & , const QPoint & )>(_a, &TrackContent::regionContextMenu, 2))
            return;
        if (QtMocHelpers::indexOfMethod<void (TrackContent::*)(double )>(_a, &TrackContent::zoomRequested, 3))
            return;
    }
}

const QMetaObject *TrackContent::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *TrackContent::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_staticMetaObjectStaticContent<qt_meta_tag_ZN12TrackContentE_t>.strings))
        return static_cast<void*>(this);
    return QWidget::qt_metacast(_clname);
}

int TrackContent::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QWidget::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 4)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 4;
    }
    if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 4)
            *reinterpret_cast<QMetaType *>(_a[0]) = QMetaType();
        _id -= 4;
    }
    return _id;
}

// SIGNAL 0
void TrackContent::regionSelected(const QString & _t1)
{
    QMetaObject::activate<void>(this, &staticMetaObject, 0, nullptr, _t1);
}

// SIGNAL 1
void TrackContent::regionMoved(const QString & _t1, double _t2)
{
    QMetaObject::activate<void>(this, &staticMetaObject, 1, nullptr, _t1, _t2);
}

// SIGNAL 2
void TrackContent::regionContextMenu(const QString & _t1, const QPoint & _t2)
{
    QMetaObject::activate<void>(this, &staticMetaObject, 2, nullptr, _t1, _t2);
}

// SIGNAL 3
void TrackContent::zoomRequested(double _t1)
{
    QMetaObject::activate<void>(this, &staticMetaObject, 3, nullptr, _t1);
}
namespace {
struct qt_meta_tag_ZN17TransportControlsE_t {};
} // unnamed namespace

template <> constexpr inline auto TransportControls::qt_create_metaobjectdata<qt_meta_tag_ZN17TransportControlsE_t>()
{
    namespace QMC = QtMocConstants;
    QtMocHelpers::StringRefStorage qt_stringData {
        "TransportControls",
        "playRequested",
        "",
        "pauseRequested",
        "stopRequested",
        "positionChanged",
        "position",
        "frameRateChanged",
        "frameRate",
        "timecodeFormatChanged",
        "format",
        "play",
        "pause",
        "stop",
        "setPlayheadPosition",
        "onPlayClicked",
        "onPauseClicked",
        "onStopClicked",
        "onPositionSliderChanged",
        "value",
        "onFrameRateChanged",
        "onTimecodeFormatChanged"
    };

    QtMocHelpers::UintData qt_methods {
        // Signal 'playRequested'
        QtMocHelpers::SignalData<void()>(1, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'pauseRequested'
        QtMocHelpers::SignalData<void()>(3, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'stopRequested'
        QtMocHelpers::SignalData<void()>(4, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'positionChanged'
        QtMocHelpers::SignalData<void(double)>(5, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::Double, 6 },
        }}),
        // Signal 'frameRateChanged'
        QtMocHelpers::SignalData<void(double)>(7, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::Double, 8 },
        }}),
        // Signal 'timecodeFormatChanged'
        QtMocHelpers::SignalData<void(int)>(9, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::Int, 10 },
        }}),
        // Slot 'play'
        QtMocHelpers::SlotData<void()>(11, 2, QMC::AccessPublic, QMetaType::Void),
        // Slot 'pause'
        QtMocHelpers::SlotData<void()>(12, 2, QMC::AccessPublic, QMetaType::Void),
        // Slot 'stop'
        QtMocHelpers::SlotData<void()>(13, 2, QMC::AccessPublic, QMetaType::Void),
        // Slot 'setPlayheadPosition'
        QtMocHelpers::SlotData<void(double)>(14, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::Double, 6 },
        }}),
        // Slot 'onPlayClicked'
        QtMocHelpers::SlotData<void()>(15, 2, QMC::AccessPrivate, QMetaType::Void),
        // Slot 'onPauseClicked'
        QtMocHelpers::SlotData<void()>(16, 2, QMC::AccessPrivate, QMetaType::Void),
        // Slot 'onStopClicked'
        QtMocHelpers::SlotData<void()>(17, 2, QMC::AccessPrivate, QMetaType::Void),
        // Slot 'onPositionSliderChanged'
        QtMocHelpers::SlotData<void(int)>(18, 2, QMC::AccessPrivate, QMetaType::Void, {{
            { QMetaType::Int, 19 },
        }}),
        // Slot 'onFrameRateChanged'
        QtMocHelpers::SlotData<void()>(20, 2, QMC::AccessPrivate, QMetaType::Void),
        // Slot 'onTimecodeFormatChanged'
        QtMocHelpers::SlotData<void()>(21, 2, QMC::AccessPrivate, QMetaType::Void),
    };
    QtMocHelpers::UintData qt_properties {
    };
    QtMocHelpers::UintData qt_enums {
    };
    return QtMocHelpers::metaObjectData<TransportControls, qt_meta_tag_ZN17TransportControlsE_t>(QMC::MetaObjectFlag{}, qt_stringData,
            qt_methods, qt_properties, qt_enums);
}
Q_CONSTINIT const QMetaObject TransportControls::staticMetaObject = { {
    QMetaObject::SuperData::link<QWidget::staticMetaObject>(),
    qt_staticMetaObjectStaticContent<qt_meta_tag_ZN17TransportControlsE_t>.stringdata,
    qt_staticMetaObjectStaticContent<qt_meta_tag_ZN17TransportControlsE_t>.data,
    qt_static_metacall,
    nullptr,
    qt_staticMetaObjectRelocatingContent<qt_meta_tag_ZN17TransportControlsE_t>.metaTypes,
    nullptr
} };

void TransportControls::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    auto *_t = static_cast<TransportControls *>(_o);
    if (_c == QMetaObject::InvokeMetaMethod) {
        switch (_id) {
        case 0: _t->playRequested(); break;
        case 1: _t->pauseRequested(); break;
        case 2: _t->stopRequested(); break;
        case 3: _t->positionChanged((*reinterpret_cast< std::add_pointer_t<double>>(_a[1]))); break;
        case 4: _t->frameRateChanged((*reinterpret_cast< std::add_pointer_t<double>>(_a[1]))); break;
        case 5: _t->timecodeFormatChanged((*reinterpret_cast< std::add_pointer_t<int>>(_a[1]))); break;
        case 6: _t->play(); break;
        case 7: _t->pause(); break;
        case 8: _t->stop(); break;
        case 9: _t->setPlayheadPosition((*reinterpret_cast< std::add_pointer_t<double>>(_a[1]))); break;
        case 10: _t->onPlayClicked(); break;
        case 11: _t->onPauseClicked(); break;
        case 12: _t->onStopClicked(); break;
        case 13: _t->onPositionSliderChanged((*reinterpret_cast< std::add_pointer_t<int>>(_a[1]))); break;
        case 14: _t->onFrameRateChanged(); break;
        case 15: _t->onTimecodeFormatChanged(); break;
        default: ;
        }
    }
    if (_c == QMetaObject::IndexOfMethod) {
        if (QtMocHelpers::indexOfMethod<void (TransportControls::*)()>(_a, &TransportControls::playRequested, 0))
            return;
        if (QtMocHelpers::indexOfMethod<void (TransportControls::*)()>(_a, &TransportControls::pauseRequested, 1))
            return;
        if (QtMocHelpers::indexOfMethod<void (TransportControls::*)()>(_a, &TransportControls::stopRequested, 2))
            return;
        if (QtMocHelpers::indexOfMethod<void (TransportControls::*)(double )>(_a, &TransportControls::positionChanged, 3))
            return;
        if (QtMocHelpers::indexOfMethod<void (TransportControls::*)(double )>(_a, &TransportControls::frameRateChanged, 4))
            return;
        if (QtMocHelpers::indexOfMethod<void (TransportControls::*)(int )>(_a, &TransportControls::timecodeFormatChanged, 5))
            return;
    }
}

const QMetaObject *TransportControls::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *TransportControls::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_staticMetaObjectStaticContent<qt_meta_tag_ZN17TransportControlsE_t>.strings))
        return static_cast<void*>(this);
    return QWidget::qt_metacast(_clname);
}

int TransportControls::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QWidget::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 16)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 16;
    }
    if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 16)
            *reinterpret_cast<QMetaType *>(_a[0]) = QMetaType();
        _id -= 16;
    }
    return _id;
}

// SIGNAL 0
void TransportControls::playRequested()
{
    QMetaObject::activate(this, &staticMetaObject, 0, nullptr);
}

// SIGNAL 1
void TransportControls::pauseRequested()
{
    QMetaObject::activate(this, &staticMetaObject, 1, nullptr);
}

// SIGNAL 2
void TransportControls::stopRequested()
{
    QMetaObject::activate(this, &staticMetaObject, 2, nullptr);
}

// SIGNAL 3
void TransportControls::positionChanged(double _t1)
{
    QMetaObject::activate<void>(this, &staticMetaObject, 3, nullptr, _t1);
}

// SIGNAL 4
void TransportControls::frameRateChanged(double _t1)
{
    QMetaObject::activate<void>(this, &staticMetaObject, 4, nullptr, _t1);
}

// SIGNAL 5
void TransportControls::timecodeFormatChanged(int _t1)
{
    QMetaObject::activate<void>(this, &staticMetaObject, 5, nullptr, _t1);
}
namespace {
struct qt_meta_tag_ZN15TimelineToolbarE_t {};
} // unnamed namespace

template <> constexpr inline auto TimelineToolbar::qt_create_metaobjectdata<qt_meta_tag_ZN15TimelineToolbarE_t>()
{
    namespace QMC = QtMocConstants;
    QtMocHelpers::StringRefStorage qt_stringData {
        "TimelineToolbar",
        "zoomInRequested",
        "",
        "zoomOutRequested",
        "fitToWindowRequested",
        "showWaveformsToggled",
        "show",
        "showRegionNamesToggled",
        "showConfidenceToggled",
        "trackSelectionRequested",
        "selectAll",
        "onZoomIn",
        "onZoomOut",
        "onFitToWindow",
        "onSelectAllTracks",
        "onDeselectAllTracks"
    };

    QtMocHelpers::UintData qt_methods {
        // Signal 'zoomInRequested'
        QtMocHelpers::SignalData<void()>(1, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'zoomOutRequested'
        QtMocHelpers::SignalData<void()>(3, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'fitToWindowRequested'
        QtMocHelpers::SignalData<void()>(4, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'showWaveformsToggled'
        QtMocHelpers::SignalData<void(bool)>(5, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::Bool, 6 },
        }}),
        // Signal 'showRegionNamesToggled'
        QtMocHelpers::SignalData<void(bool)>(7, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::Bool, 6 },
        }}),
        // Signal 'showConfidenceToggled'
        QtMocHelpers::SignalData<void(bool)>(8, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::Bool, 6 },
        }}),
        // Signal 'trackSelectionRequested'
        QtMocHelpers::SignalData<void(bool)>(9, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::Bool, 10 },
        }}),
        // Slot 'onZoomIn'
        QtMocHelpers::SlotData<void()>(11, 2, QMC::AccessPrivate, QMetaType::Void),
        // Slot 'onZoomOut'
        QtMocHelpers::SlotData<void()>(12, 2, QMC::AccessPrivate, QMetaType::Void),
        // Slot 'onFitToWindow'
        QtMocHelpers::SlotData<void()>(13, 2, QMC::AccessPrivate, QMetaType::Void),
        // Slot 'onSelectAllTracks'
        QtMocHelpers::SlotData<void()>(14, 2, QMC::AccessPrivate, QMetaType::Void),
        // Slot 'onDeselectAllTracks'
        QtMocHelpers::SlotData<void()>(15, 2, QMC::AccessPrivate, QMetaType::Void),
    };
    QtMocHelpers::UintData qt_properties {
    };
    QtMocHelpers::UintData qt_enums {
    };
    return QtMocHelpers::metaObjectData<TimelineToolbar, qt_meta_tag_ZN15TimelineToolbarE_t>(QMC::MetaObjectFlag{}, qt_stringData,
            qt_methods, qt_properties, qt_enums);
}
Q_CONSTINIT const QMetaObject TimelineToolbar::staticMetaObject = { {
    QMetaObject::SuperData::link<QToolBar::staticMetaObject>(),
    qt_staticMetaObjectStaticContent<qt_meta_tag_ZN15TimelineToolbarE_t>.stringdata,
    qt_staticMetaObjectStaticContent<qt_meta_tag_ZN15TimelineToolbarE_t>.data,
    qt_static_metacall,
    nullptr,
    qt_staticMetaObjectRelocatingContent<qt_meta_tag_ZN15TimelineToolbarE_t>.metaTypes,
    nullptr
} };

void TimelineToolbar::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    auto *_t = static_cast<TimelineToolbar *>(_o);
    if (_c == QMetaObject::InvokeMetaMethod) {
        switch (_id) {
        case 0: _t->zoomInRequested(); break;
        case 1: _t->zoomOutRequested(); break;
        case 2: _t->fitToWindowRequested(); break;
        case 3: _t->showWaveformsToggled((*reinterpret_cast< std::add_pointer_t<bool>>(_a[1]))); break;
        case 4: _t->showRegionNamesToggled((*reinterpret_cast< std::add_pointer_t<bool>>(_a[1]))); break;
        case 5: _t->showConfidenceToggled((*reinterpret_cast< std::add_pointer_t<bool>>(_a[1]))); break;
        case 6: _t->trackSelectionRequested((*reinterpret_cast< std::add_pointer_t<bool>>(_a[1]))); break;
        case 7: _t->onZoomIn(); break;
        case 8: _t->onZoomOut(); break;
        case 9: _t->onFitToWindow(); break;
        case 10: _t->onSelectAllTracks(); break;
        case 11: _t->onDeselectAllTracks(); break;
        default: ;
        }
    }
    if (_c == QMetaObject::IndexOfMethod) {
        if (QtMocHelpers::indexOfMethod<void (TimelineToolbar::*)()>(_a, &TimelineToolbar::zoomInRequested, 0))
            return;
        if (QtMocHelpers::indexOfMethod<void (TimelineToolbar::*)()>(_a, &TimelineToolbar::zoomOutRequested, 1))
            return;
        if (QtMocHelpers::indexOfMethod<void (TimelineToolbar::*)()>(_a, &TimelineToolbar::fitToWindowRequested, 2))
            return;
        if (QtMocHelpers::indexOfMethod<void (TimelineToolbar::*)(bool )>(_a, &TimelineToolbar::showWaveformsToggled, 3))
            return;
        if (QtMocHelpers::indexOfMethod<void (TimelineToolbar::*)(bool )>(_a, &TimelineToolbar::showRegionNamesToggled, 4))
            return;
        if (QtMocHelpers::indexOfMethod<void (TimelineToolbar::*)(bool )>(_a, &TimelineToolbar::showConfidenceToggled, 5))
            return;
        if (QtMocHelpers::indexOfMethod<void (TimelineToolbar::*)(bool )>(_a, &TimelineToolbar::trackSelectionRequested, 6))
            return;
    }
}

const QMetaObject *TimelineToolbar::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *TimelineToolbar::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_staticMetaObjectStaticContent<qt_meta_tag_ZN15TimelineToolbarE_t>.strings))
        return static_cast<void*>(this);
    return QToolBar::qt_metacast(_clname);
}

int TimelineToolbar::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QToolBar::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 12)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 12;
    }
    if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 12)
            *reinterpret_cast<QMetaType *>(_a[0]) = QMetaType();
        _id -= 12;
    }
    return _id;
}

// SIGNAL 0
void TimelineToolbar::zoomInRequested()
{
    QMetaObject::activate(this, &staticMetaObject, 0, nullptr);
}

// SIGNAL 1
void TimelineToolbar::zoomOutRequested()
{
    QMetaObject::activate(this, &staticMetaObject, 1, nullptr);
}

// SIGNAL 2
void TimelineToolbar::fitToWindowRequested()
{
    QMetaObject::activate(this, &staticMetaObject, 2, nullptr);
}

// SIGNAL 3
void TimelineToolbar::showWaveformsToggled(bool _t1)
{
    QMetaObject::activate<void>(this, &staticMetaObject, 3, nullptr, _t1);
}

// SIGNAL 4
void TimelineToolbar::showRegionNamesToggled(bool _t1)
{
    QMetaObject::activate<void>(this, &staticMetaObject, 4, nullptr, _t1);
}

// SIGNAL 5
void TimelineToolbar::showConfidenceToggled(bool _t1)
{
    QMetaObject::activate<void>(this, &staticMetaObject, 5, nullptr, _t1);
}

// SIGNAL 6
void TimelineToolbar::trackSelectionRequested(bool _t1)
{
    QMetaObject::activate<void>(this, &staticMetaObject, 6, nullptr, _t1);
}
namespace {
struct qt_meta_tag_ZN19RegionMetadataPanelE_t {};
} // unnamed namespace

template <> constexpr inline auto RegionMetadataPanel::qt_create_metaobjectdata<qt_meta_tag_ZN19RegionMetadataPanelE_t>()
{
    namespace QMC = QtMocConstants;
    QtMocHelpers::StringRefStorage qt_stringData {
        "RegionMetadataPanel",
        "regionUpdated",
        "",
        "regionId",
        "QVariantMap",
        "metadata",
        "onMetadataChanged"
    };

    QtMocHelpers::UintData qt_methods {
        // Signal 'regionUpdated'
        QtMocHelpers::SignalData<void(const QString &, const QVariantMap &)>(1, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::QString, 3 }, { 0x80000000 | 4, 5 },
        }}),
        // Slot 'onMetadataChanged'
        QtMocHelpers::SlotData<void()>(6, 2, QMC::AccessPrivate, QMetaType::Void),
    };
    QtMocHelpers::UintData qt_properties {
    };
    QtMocHelpers::UintData qt_enums {
    };
    return QtMocHelpers::metaObjectData<RegionMetadataPanel, qt_meta_tag_ZN19RegionMetadataPanelE_t>(QMC::MetaObjectFlag{}, qt_stringData,
            qt_methods, qt_properties, qt_enums);
}
Q_CONSTINIT const QMetaObject RegionMetadataPanel::staticMetaObject = { {
    QMetaObject::SuperData::link<QWidget::staticMetaObject>(),
    qt_staticMetaObjectStaticContent<qt_meta_tag_ZN19RegionMetadataPanelE_t>.stringdata,
    qt_staticMetaObjectStaticContent<qt_meta_tag_ZN19RegionMetadataPanelE_t>.data,
    qt_static_metacall,
    nullptr,
    qt_staticMetaObjectRelocatingContent<qt_meta_tag_ZN19RegionMetadataPanelE_t>.metaTypes,
    nullptr
} };

void RegionMetadataPanel::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    auto *_t = static_cast<RegionMetadataPanel *>(_o);
    if (_c == QMetaObject::InvokeMetaMethod) {
        switch (_id) {
        case 0: _t->regionUpdated((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<QVariantMap>>(_a[2]))); break;
        case 1: _t->onMetadataChanged(); break;
        default: ;
        }
    }
    if (_c == QMetaObject::IndexOfMethod) {
        if (QtMocHelpers::indexOfMethod<void (RegionMetadataPanel::*)(const QString & , const QVariantMap & )>(_a, &RegionMetadataPanel::regionUpdated, 0))
            return;
    }
}

const QMetaObject *RegionMetadataPanel::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *RegionMetadataPanel::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_staticMetaObjectStaticContent<qt_meta_tag_ZN19RegionMetadataPanelE_t>.strings))
        return static_cast<void*>(this);
    return QWidget::qt_metacast(_clname);
}

int RegionMetadataPanel::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QWidget::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 2)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 2;
    }
    if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 2)
            *reinterpret_cast<QMetaType *>(_a[0]) = QMetaType();
        _id -= 2;
    }
    return _id;
}

// SIGNAL 0
void RegionMetadataPanel::regionUpdated(const QString & _t1, const QVariantMap & _t2)
{
    QMetaObject::activate<void>(this, &staticMetaObject, 0, nullptr, _t1, _t2);
}
namespace {
struct qt_meta_tag_ZN14TimelineWidgetE_t {};
} // unnamed namespace

template <> constexpr inline auto TimelineWidget::qt_create_metaobjectdata<qt_meta_tag_ZN14TimelineWidgetE_t>()
{
    namespace QMC = QtMocConstants;
    QtMocHelpers::StringRefStorage qt_stringData {
        "TimelineWidget",
        "regionSelected",
        "",
        "regionId",
        "regionUpdated",
        "QVariantMap",
        "metadata",
        "playbackRequested",
        "trackSelectionChanged",
        "selectedTracks",
        "timeRangeSelectionChanged",
        "startTime",
        "endTime",
        "zoomIn",
        "zoomOut",
        "fitToWindow",
        "playPause",
        "stop",
        "onShowWaveformsToggled",
        "show",
        "onShowRegionNamesToggled",
        "onShowConfidenceToggled",
        "onPlayheadMoved",
        "position",
        "onRegionSelected",
        "onTrackMuteToggled",
        "trackName",
        "muted",
        "onTrackSoloToggled",
        "solo",
        "onTrackSelectionToggled",
        "selected",
        "onRegionContextMenu",
        "onZoomRequested",
        "factor",
        "onTransportPlayRequested",
        "onTransportPauseRequested",
        "onTransportStopRequested",
        "onTransportPositionChanged",
        "onFrameRateChanged",
        "frameRate",
        "onTimecodeFormatChanged",
        "format",
        "onToolbarZoomIn",
        "onToolbarZoomOut",
        "onToolbarFitToWindow",
        "onToolbarTrackSelection",
        "selectAll",
        "onAudioPlaybackPositionChanged",
        "onAudioPlaybackDurationChanged",
        "duration",
        "onAudioPlaybackStateChanged",
        "isPlaying",
        "initializeAudioComponents",
        "onHorizontalScrollChanged",
        "value"
    };

    QtMocHelpers::UintData qt_methods {
        // Signal 'regionSelected'
        QtMocHelpers::SignalData<void(const QString &)>(1, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::QString, 3 },
        }}),
        // Signal 'regionUpdated'
        QtMocHelpers::SignalData<void(const QString &, const QVariantMap &)>(4, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::QString, 3 }, { 0x80000000 | 5, 6 },
        }}),
        // Signal 'playbackRequested'
        QtMocHelpers::SignalData<void(const QString &)>(7, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::QString, 3 },
        }}),
        // Signal 'trackSelectionChanged'
        QtMocHelpers::SignalData<void(const QStringList &)>(8, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::QStringList, 9 },
        }}),
        // Signal 'timeRangeSelectionChanged'
        QtMocHelpers::SignalData<void(double, double)>(10, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::Double, 11 }, { QMetaType::Double, 12 },
        }}),
        // Slot 'zoomIn'
        QtMocHelpers::SlotData<void()>(13, 2, QMC::AccessPublic, QMetaType::Void),
        // Slot 'zoomOut'
        QtMocHelpers::SlotData<void()>(14, 2, QMC::AccessPublic, QMetaType::Void),
        // Slot 'fitToWindow'
        QtMocHelpers::SlotData<void()>(15, 2, QMC::AccessPublic, QMetaType::Void),
        // Slot 'playPause'
        QtMocHelpers::SlotData<void()>(16, 2, QMC::AccessPublic, QMetaType::Void),
        // Slot 'stop'
        QtMocHelpers::SlotData<void()>(17, 2, QMC::AccessPublic, QMetaType::Void),
        // Slot 'onShowWaveformsToggled'
        QtMocHelpers::SlotData<void(bool)>(18, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::Bool, 19 },
        }}),
        // Slot 'onShowRegionNamesToggled'
        QtMocHelpers::SlotData<void(bool)>(20, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::Bool, 19 },
        }}),
        // Slot 'onShowConfidenceToggled'
        QtMocHelpers::SlotData<void(bool)>(21, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::Bool, 19 },
        }}),
        // Slot 'onPlayheadMoved'
        QtMocHelpers::SlotData<void(double)>(22, 2, QMC::AccessPrivate, QMetaType::Void, {{
            { QMetaType::Double, 23 },
        }}),
        // Slot 'onRegionSelected'
        QtMocHelpers::SlotData<void(const QString &)>(24, 2, QMC::AccessPrivate, QMetaType::Void, {{
            { QMetaType::QString, 3 },
        }}),
        // Slot 'onTrackMuteToggled'
        QtMocHelpers::SlotData<void(const QString &, bool)>(25, 2, QMC::AccessPrivate, QMetaType::Void, {{
            { QMetaType::QString, 26 }, { QMetaType::Bool, 27 },
        }}),
        // Slot 'onTrackSoloToggled'
        QtMocHelpers::SlotData<void(const QString &, bool)>(28, 2, QMC::AccessPrivate, QMetaType::Void, {{
            { QMetaType::QString, 26 }, { QMetaType::Bool, 29 },
        }}),
        // Slot 'onTrackSelectionToggled'
        QtMocHelpers::SlotData<void(const QString &, bool)>(30, 2, QMC::AccessPrivate, QMetaType::Void, {{
            { QMetaType::QString, 26 }, { QMetaType::Bool, 31 },
        }}),
        // Slot 'onRegionContextMenu'
        QtMocHelpers::SlotData<void(const QString &, const QPoint &)>(32, 2, QMC::AccessPrivate, QMetaType::Void, {{
            { QMetaType::QString, 3 }, { QMetaType::QPoint, 23 },
        }}),
        // Slot 'onZoomRequested'
        QtMocHelpers::SlotData<void(double)>(33, 2, QMC::AccessPrivate, QMetaType::Void, {{
            { QMetaType::Double, 34 },
        }}),
        // Slot 'onTransportPlayRequested'
        QtMocHelpers::SlotData<void()>(35, 2, QMC::AccessPrivate, QMetaType::Void),
        // Slot 'onTransportPauseRequested'
        QtMocHelpers::SlotData<void()>(36, 2, QMC::AccessPrivate, QMetaType::Void),
        // Slot 'onTransportStopRequested'
        QtMocHelpers::SlotData<void()>(37, 2, QMC::AccessPrivate, QMetaType::Void),
        // Slot 'onTransportPositionChanged'
        QtMocHelpers::SlotData<void(double)>(38, 2, QMC::AccessPrivate, QMetaType::Void, {{
            { QMetaType::Double, 23 },
        }}),
        // Slot 'onFrameRateChanged'
        QtMocHelpers::SlotData<void(double)>(39, 2, QMC::AccessPrivate, QMetaType::Void, {{
            { QMetaType::Double, 40 },
        }}),
        // Slot 'onTimecodeFormatChanged'
        QtMocHelpers::SlotData<void(int)>(41, 2, QMC::AccessPrivate, QMetaType::Void, {{
            { QMetaType::Int, 42 },
        }}),
        // Slot 'onToolbarZoomIn'
        QtMocHelpers::SlotData<void()>(43, 2, QMC::AccessPrivate, QMetaType::Void),
        // Slot 'onToolbarZoomOut'
        QtMocHelpers::SlotData<void()>(44, 2, QMC::AccessPrivate, QMetaType::Void),
        // Slot 'onToolbarFitToWindow'
        QtMocHelpers::SlotData<void()>(45, 2, QMC::AccessPrivate, QMetaType::Void),
        // Slot 'onToolbarTrackSelection'
        QtMocHelpers::SlotData<void(bool)>(46, 2, QMC::AccessPrivate, QMetaType::Void, {{
            { QMetaType::Bool, 47 },
        }}),
        // Slot 'onAudioPlaybackPositionChanged'
        QtMocHelpers::SlotData<void(double)>(48, 2, QMC::AccessPrivate, QMetaType::Void, {{
            { QMetaType::Double, 23 },
        }}),
        // Slot 'onAudioPlaybackDurationChanged'
        QtMocHelpers::SlotData<void(double)>(49, 2, QMC::AccessPrivate, QMetaType::Void, {{
            { QMetaType::Double, 50 },
        }}),
        // Slot 'onAudioPlaybackStateChanged'
        QtMocHelpers::SlotData<void(bool)>(51, 2, QMC::AccessPrivate, QMetaType::Void, {{
            { QMetaType::Bool, 52 },
        }}),
        // Slot 'initializeAudioComponents'
        QtMocHelpers::SlotData<void()>(53, 2, QMC::AccessPrivate, QMetaType::Void),
        // Slot 'onHorizontalScrollChanged'
        QtMocHelpers::SlotData<void(int)>(54, 2, QMC::AccessPrivate, QMetaType::Void, {{
            { QMetaType::Int, 55 },
        }}),
    };
    QtMocHelpers::UintData qt_properties {
    };
    QtMocHelpers::UintData qt_enums {
    };
    return QtMocHelpers::metaObjectData<TimelineWidget, qt_meta_tag_ZN14TimelineWidgetE_t>(QMC::MetaObjectFlag{}, qt_stringData,
            qt_methods, qt_properties, qt_enums);
}
Q_CONSTINIT const QMetaObject TimelineWidget::staticMetaObject = { {
    QMetaObject::SuperData::link<QWidget::staticMetaObject>(),
    qt_staticMetaObjectStaticContent<qt_meta_tag_ZN14TimelineWidgetE_t>.stringdata,
    qt_staticMetaObjectStaticContent<qt_meta_tag_ZN14TimelineWidgetE_t>.data,
    qt_static_metacall,
    nullptr,
    qt_staticMetaObjectRelocatingContent<qt_meta_tag_ZN14TimelineWidgetE_t>.metaTypes,
    nullptr
} };

void TimelineWidget::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    auto *_t = static_cast<TimelineWidget *>(_o);
    if (_c == QMetaObject::InvokeMetaMethod) {
        switch (_id) {
        case 0: _t->regionSelected((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1]))); break;
        case 1: _t->regionUpdated((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<QVariantMap>>(_a[2]))); break;
        case 2: _t->playbackRequested((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1]))); break;
        case 3: _t->trackSelectionChanged((*reinterpret_cast< std::add_pointer_t<QStringList>>(_a[1]))); break;
        case 4: _t->timeRangeSelectionChanged((*reinterpret_cast< std::add_pointer_t<double>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<double>>(_a[2]))); break;
        case 5: _t->zoomIn(); break;
        case 6: _t->zoomOut(); break;
        case 7: _t->fitToWindow(); break;
        case 8: _t->playPause(); break;
        case 9: _t->stop(); break;
        case 10: _t->onShowWaveformsToggled((*reinterpret_cast< std::add_pointer_t<bool>>(_a[1]))); break;
        case 11: _t->onShowRegionNamesToggled((*reinterpret_cast< std::add_pointer_t<bool>>(_a[1]))); break;
        case 12: _t->onShowConfidenceToggled((*reinterpret_cast< std::add_pointer_t<bool>>(_a[1]))); break;
        case 13: _t->onPlayheadMoved((*reinterpret_cast< std::add_pointer_t<double>>(_a[1]))); break;
        case 14: _t->onRegionSelected((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1]))); break;
        case 15: _t->onTrackMuteToggled((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<bool>>(_a[2]))); break;
        case 16: _t->onTrackSoloToggled((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<bool>>(_a[2]))); break;
        case 17: _t->onTrackSelectionToggled((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<bool>>(_a[2]))); break;
        case 18: _t->onRegionContextMenu((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<QPoint>>(_a[2]))); break;
        case 19: _t->onZoomRequested((*reinterpret_cast< std::add_pointer_t<double>>(_a[1]))); break;
        case 20: _t->onTransportPlayRequested(); break;
        case 21: _t->onTransportPauseRequested(); break;
        case 22: _t->onTransportStopRequested(); break;
        case 23: _t->onTransportPositionChanged((*reinterpret_cast< std::add_pointer_t<double>>(_a[1]))); break;
        case 24: _t->onFrameRateChanged((*reinterpret_cast< std::add_pointer_t<double>>(_a[1]))); break;
        case 25: _t->onTimecodeFormatChanged((*reinterpret_cast< std::add_pointer_t<int>>(_a[1]))); break;
        case 26: _t->onToolbarZoomIn(); break;
        case 27: _t->onToolbarZoomOut(); break;
        case 28: _t->onToolbarFitToWindow(); break;
        case 29: _t->onToolbarTrackSelection((*reinterpret_cast< std::add_pointer_t<bool>>(_a[1]))); break;
        case 30: _t->onAudioPlaybackPositionChanged((*reinterpret_cast< std::add_pointer_t<double>>(_a[1]))); break;
        case 31: _t->onAudioPlaybackDurationChanged((*reinterpret_cast< std::add_pointer_t<double>>(_a[1]))); break;
        case 32: _t->onAudioPlaybackStateChanged((*reinterpret_cast< std::add_pointer_t<bool>>(_a[1]))); break;
        case 33: _t->initializeAudioComponents(); break;
        case 34: _t->onHorizontalScrollChanged((*reinterpret_cast< std::add_pointer_t<int>>(_a[1]))); break;
        default: ;
        }
    }
    if (_c == QMetaObject::IndexOfMethod) {
        if (QtMocHelpers::indexOfMethod<void (TimelineWidget::*)(const QString & )>(_a, &TimelineWidget::regionSelected, 0))
            return;
        if (QtMocHelpers::indexOfMethod<void (TimelineWidget::*)(const QString & , const QVariantMap & )>(_a, &TimelineWidget::regionUpdated, 1))
            return;
        if (QtMocHelpers::indexOfMethod<void (TimelineWidget::*)(const QString & )>(_a, &TimelineWidget::playbackRequested, 2))
            return;
        if (QtMocHelpers::indexOfMethod<void (TimelineWidget::*)(const QStringList & )>(_a, &TimelineWidget::trackSelectionChanged, 3))
            return;
        if (QtMocHelpers::indexOfMethod<void (TimelineWidget::*)(double , double )>(_a, &TimelineWidget::timeRangeSelectionChanged, 4))
            return;
    }
}

const QMetaObject *TimelineWidget::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *TimelineWidget::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_staticMetaObjectStaticContent<qt_meta_tag_ZN14TimelineWidgetE_t>.strings))
        return static_cast<void*>(this);
    return QWidget::qt_metacast(_clname);
}

int TimelineWidget::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QWidget::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 35)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 35;
    }
    if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 35)
            *reinterpret_cast<QMetaType *>(_a[0]) = QMetaType();
        _id -= 35;
    }
    return _id;
}

// SIGNAL 0
void TimelineWidget::regionSelected(const QString & _t1)
{
    QMetaObject::activate<void>(this, &staticMetaObject, 0, nullptr, _t1);
}

// SIGNAL 1
void TimelineWidget::regionUpdated(const QString & _t1, const QVariantMap & _t2)
{
    QMetaObject::activate<void>(this, &staticMetaObject, 1, nullptr, _t1, _t2);
}

// SIGNAL 2
void TimelineWidget::playbackRequested(const QString & _t1)
{
    QMetaObject::activate<void>(this, &staticMetaObject, 2, nullptr, _t1);
}

// SIGNAL 3
void TimelineWidget::trackSelectionChanged(const QStringList & _t1)
{
    QMetaObject::activate<void>(this, &staticMetaObject, 3, nullptr, _t1);
}

// SIGNAL 4
void TimelineWidget::timeRangeSelectionChanged(double _t1, double _t2)
{
    QMetaObject::activate<void>(this, &staticMetaObject, 4, nullptr, _t1, _t2);
}
QT_WARNING_POP
