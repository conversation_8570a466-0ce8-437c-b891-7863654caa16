/****************************************************************************
** Meta object code from reading C++ file 'AAFReader.h'
**
** Created by: The Qt Meta Object Compiler version 69 (Qt 6.9.0)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include "../../../src/core/AAFReader.h"
#include <QtCore/qmetatype.h>

#include <QtCore/qtmochelpers.h>

#include <memory>


#include <QtCore/qxptype_traits.h>
#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'AAFReader.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 69
#error "This file was generated using the moc from 6.9.0. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

#ifndef Q_CONSTINIT
#define Q_CONSTINIT
#endif

QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
QT_WARNING_DISABLE_GCC("-Wuseless-cast")
namespace {
struct qt_meta_tag_ZN9AAFReaderE_t {};
} // unnamed namespace

template <> constexpr inline auto AAFReader::qt_create_metaobjectdata<qt_meta_tag_ZN9AAFReaderE_t>()
{
    namespace QMC = QtMocConstants;
    QtMocHelpers::StringRefStorage qt_stringData {
        "AAFReader",
        "currentFileChanged",
        "",
        "isLoadedChanged",
        "tracksChanged",
        "durationChanged",
        "frameRateChanged",
        "timecodeFormatChanged",
        "fileLoadCompleted",
        "success",
        "message",
        "parsingProgress",
        "progress",
        "status",
        "error",
        "loadFile",
        "filePath",
        "loadFileFromUrl",
        "fileUrl",
        "closeFile",
        "getTrackInfo",
        "QVariantMap",
        "trackName",
        "getTrackRegions",
        "QVariantList",
        "getAllRegions",
        "getAudioFileForRegion",
        "regionId",
        "validateAAFFile",
        "getSupportedExtensions",
        "formatTimecode",
        "seconds",
        "format",
        "currentFile",
        "isLoaded",
        "tracks",
        "duration",
        "frameRate",
        "timecodeFormat"
    };

    QtMocHelpers::UintData qt_methods {
        // Signal 'currentFileChanged'
        QtMocHelpers::SignalData<void()>(1, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'isLoadedChanged'
        QtMocHelpers::SignalData<void()>(3, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'tracksChanged'
        QtMocHelpers::SignalData<void()>(4, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'durationChanged'
        QtMocHelpers::SignalData<void()>(5, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'frameRateChanged'
        QtMocHelpers::SignalData<void()>(6, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'timecodeFormatChanged'
        QtMocHelpers::SignalData<void()>(7, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'fileLoadCompleted'
        QtMocHelpers::SignalData<void(bool, const QString &)>(8, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::Bool, 9 }, { QMetaType::QString, 10 },
        }}),
        // Signal 'parsingProgress'
        QtMocHelpers::SignalData<void(int, const QString &)>(11, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::Int, 12 }, { QMetaType::QString, 13 },
        }}),
        // Signal 'error'
        QtMocHelpers::SignalData<void(const QString &)>(14, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::QString, 14 },
        }}),
        // Slot 'loadFile'
        QtMocHelpers::SlotData<bool(const QString &)>(15, 2, QMC::AccessPublic, QMetaType::Bool, {{
            { QMetaType::QString, 16 },
        }}),
        // Slot 'loadFileFromUrl'
        QtMocHelpers::SlotData<bool(const QUrl &)>(17, 2, QMC::AccessPublic, QMetaType::Bool, {{
            { QMetaType::QUrl, 18 },
        }}),
        // Slot 'closeFile'
        QtMocHelpers::SlotData<void()>(19, 2, QMC::AccessPublic, QMetaType::Void),
        // Slot 'getTrackInfo'
        QtMocHelpers::SlotData<QVariantMap(const QString &)>(20, 2, QMC::AccessPublic, 0x80000000 | 21, {{
            { QMetaType::QString, 22 },
        }}),
        // Slot 'getTrackRegions'
        QtMocHelpers::SlotData<QVariantList(const QString &)>(23, 2, QMC::AccessPublic, 0x80000000 | 24, {{
            { QMetaType::QString, 22 },
        }}),
        // Slot 'getAllRegions'
        QtMocHelpers::SlotData<QVariantList()>(25, 2, QMC::AccessPublic, 0x80000000 | 24),
        // Slot 'getAudioFileForRegion'
        QtMocHelpers::SlotData<QString(const QString &)>(26, 2, QMC::AccessPublic, QMetaType::QString, {{
            { QMetaType::QString, 27 },
        }}),
        // Slot 'validateAAFFile'
        QtMocHelpers::SlotData<bool(const QString &)>(28, 2, QMC::AccessPublic, QMetaType::Bool, {{
            { QMetaType::QString, 16 },
        }}),
        // Slot 'getSupportedExtensions'
        QtMocHelpers::SlotData<QStringList()>(29, 2, QMC::AccessPublic, QMetaType::QStringList),
        // Slot 'formatTimecode'
        QtMocHelpers::SlotData<QString(double, const QString &)>(30, 2, QMC::AccessPublic, QMetaType::QString, {{
            { QMetaType::Double, 31 }, { QMetaType::QString, 32 },
        }}),
        // Slot 'formatTimecode'
        QtMocHelpers::SlotData<QString(double)>(30, 2, QMC::AccessPublic | QMC::MethodCloned, QMetaType::QString, {{
            { QMetaType::Double, 31 },
        }}),
    };
    QtMocHelpers::UintData qt_properties {
        // property 'currentFile'
        QtMocHelpers::PropertyData<QString>(33, QMetaType::QString, QMC::DefaultPropertyFlags, 0),
        // property 'isLoaded'
        QtMocHelpers::PropertyData<bool>(34, QMetaType::Bool, QMC::DefaultPropertyFlags, 1),
        // property 'tracks'
        QtMocHelpers::PropertyData<QStringList>(35, QMetaType::QStringList, QMC::DefaultPropertyFlags, 2),
        // property 'duration'
        QtMocHelpers::PropertyData<double>(36, QMetaType::Double, QMC::DefaultPropertyFlags, 3),
        // property 'frameRate'
        QtMocHelpers::PropertyData<double>(37, QMetaType::Double, QMC::DefaultPropertyFlags, 4),
        // property 'timecodeFormat'
        QtMocHelpers::PropertyData<QString>(38, QMetaType::QString, QMC::DefaultPropertyFlags, 5),
    };
    QtMocHelpers::UintData qt_enums {
    };
    return QtMocHelpers::metaObjectData<AAFReader, qt_meta_tag_ZN9AAFReaderE_t>(QMC::MetaObjectFlag{}, qt_stringData,
            qt_methods, qt_properties, qt_enums);
}
Q_CONSTINIT const QMetaObject AAFReader::staticMetaObject = { {
    QMetaObject::SuperData::link<QObject::staticMetaObject>(),
    qt_staticMetaObjectStaticContent<qt_meta_tag_ZN9AAFReaderE_t>.stringdata,
    qt_staticMetaObjectStaticContent<qt_meta_tag_ZN9AAFReaderE_t>.data,
    qt_static_metacall,
    nullptr,
    qt_staticMetaObjectRelocatingContent<qt_meta_tag_ZN9AAFReaderE_t>.metaTypes,
    nullptr
} };

void AAFReader::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    auto *_t = static_cast<AAFReader *>(_o);
    if (_c == QMetaObject::InvokeMetaMethod) {
        switch (_id) {
        case 0: _t->currentFileChanged(); break;
        case 1: _t->isLoadedChanged(); break;
        case 2: _t->tracksChanged(); break;
        case 3: _t->durationChanged(); break;
        case 4: _t->frameRateChanged(); break;
        case 5: _t->timecodeFormatChanged(); break;
        case 6: _t->fileLoadCompleted((*reinterpret_cast< std::add_pointer_t<bool>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<QString>>(_a[2]))); break;
        case 7: _t->parsingProgress((*reinterpret_cast< std::add_pointer_t<int>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<QString>>(_a[2]))); break;
        case 8: _t->error((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1]))); break;
        case 9: { bool _r = _t->loadFile((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1])));
            if (_a[0]) *reinterpret_cast< bool*>(_a[0]) = std::move(_r); }  break;
        case 10: { bool _r = _t->loadFileFromUrl((*reinterpret_cast< std::add_pointer_t<QUrl>>(_a[1])));
            if (_a[0]) *reinterpret_cast< bool*>(_a[0]) = std::move(_r); }  break;
        case 11: _t->closeFile(); break;
        case 12: { QVariantMap _r = _t->getTrackInfo((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1])));
            if (_a[0]) *reinterpret_cast< QVariantMap*>(_a[0]) = std::move(_r); }  break;
        case 13: { QVariantList _r = _t->getTrackRegions((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1])));
            if (_a[0]) *reinterpret_cast< QVariantList*>(_a[0]) = std::move(_r); }  break;
        case 14: { QVariantList _r = _t->getAllRegions();
            if (_a[0]) *reinterpret_cast< QVariantList*>(_a[0]) = std::move(_r); }  break;
        case 15: { QString _r = _t->getAudioFileForRegion((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1])));
            if (_a[0]) *reinterpret_cast< QString*>(_a[0]) = std::move(_r); }  break;
        case 16: { bool _r = _t->validateAAFFile((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1])));
            if (_a[0]) *reinterpret_cast< bool*>(_a[0]) = std::move(_r); }  break;
        case 17: { QStringList _r = _t->getSupportedExtensions();
            if (_a[0]) *reinterpret_cast< QStringList*>(_a[0]) = std::move(_r); }  break;
        case 18: { QString _r = _t->formatTimecode((*reinterpret_cast< std::add_pointer_t<double>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<QString>>(_a[2])));
            if (_a[0]) *reinterpret_cast< QString*>(_a[0]) = std::move(_r); }  break;
        case 19: { QString _r = _t->formatTimecode((*reinterpret_cast< std::add_pointer_t<double>>(_a[1])));
            if (_a[0]) *reinterpret_cast< QString*>(_a[0]) = std::move(_r); }  break;
        default: ;
        }
    }
    if (_c == QMetaObject::IndexOfMethod) {
        if (QtMocHelpers::indexOfMethod<void (AAFReader::*)()>(_a, &AAFReader::currentFileChanged, 0))
            return;
        if (QtMocHelpers::indexOfMethod<void (AAFReader::*)()>(_a, &AAFReader::isLoadedChanged, 1))
            return;
        if (QtMocHelpers::indexOfMethod<void (AAFReader::*)()>(_a, &AAFReader::tracksChanged, 2))
            return;
        if (QtMocHelpers::indexOfMethod<void (AAFReader::*)()>(_a, &AAFReader::durationChanged, 3))
            return;
        if (QtMocHelpers::indexOfMethod<void (AAFReader::*)()>(_a, &AAFReader::frameRateChanged, 4))
            return;
        if (QtMocHelpers::indexOfMethod<void (AAFReader::*)()>(_a, &AAFReader::timecodeFormatChanged, 5))
            return;
        if (QtMocHelpers::indexOfMethod<void (AAFReader::*)(bool , const QString & )>(_a, &AAFReader::fileLoadCompleted, 6))
            return;
        if (QtMocHelpers::indexOfMethod<void (AAFReader::*)(int , const QString & )>(_a, &AAFReader::parsingProgress, 7))
            return;
        if (QtMocHelpers::indexOfMethod<void (AAFReader::*)(const QString & )>(_a, &AAFReader::error, 8))
            return;
    }
    if (_c == QMetaObject::ReadProperty) {
        void *_v = _a[0];
        switch (_id) {
        case 0: *reinterpret_cast<QString*>(_v) = _t->currentFile(); break;
        case 1: *reinterpret_cast<bool*>(_v) = _t->isLoaded(); break;
        case 2: *reinterpret_cast<QStringList*>(_v) = _t->tracks(); break;
        case 3: *reinterpret_cast<double*>(_v) = _t->duration(); break;
        case 4: *reinterpret_cast<double*>(_v) = _t->frameRate(); break;
        case 5: *reinterpret_cast<QString*>(_v) = _t->timecodeFormat(); break;
        default: break;
        }
    }
}

const QMetaObject *AAFReader::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *AAFReader::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_staticMetaObjectStaticContent<qt_meta_tag_ZN9AAFReaderE_t>.strings))
        return static_cast<void*>(this);
    return QObject::qt_metacast(_clname);
}

int AAFReader::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QObject::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 20)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 20;
    }
    if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 20)
            *reinterpret_cast<QMetaType *>(_a[0]) = QMetaType();
        _id -= 20;
    }
    if (_c == QMetaObject::ReadProperty || _c == QMetaObject::WriteProperty
            || _c == QMetaObject::ResetProperty || _c == QMetaObject::BindableProperty
            || _c == QMetaObject::RegisterPropertyMetaType) {
        qt_static_metacall(this, _c, _id, _a);
        _id -= 6;
    }
    return _id;
}

// SIGNAL 0
void AAFReader::currentFileChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 0, nullptr);
}

// SIGNAL 1
void AAFReader::isLoadedChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 1, nullptr);
}

// SIGNAL 2
void AAFReader::tracksChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 2, nullptr);
}

// SIGNAL 3
void AAFReader::durationChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 3, nullptr);
}

// SIGNAL 4
void AAFReader::frameRateChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 4, nullptr);
}

// SIGNAL 5
void AAFReader::timecodeFormatChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 5, nullptr);
}

// SIGNAL 6
void AAFReader::fileLoadCompleted(bool _t1, const QString & _t2)
{
    QMetaObject::activate<void>(this, &staticMetaObject, 6, nullptr, _t1, _t2);
}

// SIGNAL 7
void AAFReader::parsingProgress(int _t1, const QString & _t2)
{
    QMetaObject::activate<void>(this, &staticMetaObject, 7, nullptr, _t1, _t2);
}

// SIGNAL 8
void AAFReader::error(const QString & _t1)
{
    QMetaObject::activate<void>(this, &staticMetaObject, 8, nullptr, _t1);
}
QT_WARNING_POP
