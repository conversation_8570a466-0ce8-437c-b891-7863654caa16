/****************************************************************************
** Meta object code from reading C++ file 'ProgressTracker.h'
**
** Created by: The Qt Meta Object Compiler version 69 (Qt 6.9.0)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include "../../../src/core/ProgressTracker.h"
#include <QtCore/qmetatype.h>

#include <QtCore/qtmochelpers.h>

#include <memory>


#include <QtCore/qxptype_traits.h>
#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'ProgressTracker.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 69
#error "This file was generated using the moc from 6.9.0. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

#ifndef Q_CONSTINIT
#define Q_CONSTINIT
#endif

QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
QT_WARNING_DISABLE_GCC("-Wuseless-cast")
namespace {
struct qt_meta_tag_ZN15ProgressTrackerE_t {};
} // unnamed namespace

template <> constexpr inline auto ProgressTracker::qt_create_metaobjectdata<qt_meta_tag_ZN15ProgressTrackerE_t>()
{
    namespace QMC = QtMocConstants;
    QtMocHelpers::StringRefStorage qt_stringData {
        "ProgressTracker",
        "progressChanged",
        "",
        "currentTaskChanged",
        "timeRemainingChanged",
        "elapsedTimeChanged",
        "isPausedChanged",
        "isStoppedChanged",
        "isRunningChanged",
        "progressCompleted",
        "success",
        "message",
        "startProgress",
        "totalSteps",
        "taskDescription",
        "updateProgress",
        "currentStep",
        "setProgress",
        "progress",
        "pauseProgress",
        "resumeProgress",
        "stopProgress",
        "completeProgress",
        "finalMessage",
        "resetProgress",
        "addSubTask",
        "subTaskName",
        "subTaskSteps",
        "updateSubTask",
        "updateTimeCalculations",
        "currentTask",
        "timeRemaining",
        "elapsedTime",
        "isPaused",
        "isStopped",
        "isRunning"
    };

    QtMocHelpers::UintData qt_methods {
        // Signal 'progressChanged'
        QtMocHelpers::SignalData<void()>(1, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'currentTaskChanged'
        QtMocHelpers::SignalData<void()>(3, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'timeRemainingChanged'
        QtMocHelpers::SignalData<void()>(4, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'elapsedTimeChanged'
        QtMocHelpers::SignalData<void()>(5, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'isPausedChanged'
        QtMocHelpers::SignalData<void()>(6, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'isStoppedChanged'
        QtMocHelpers::SignalData<void()>(7, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'isRunningChanged'
        QtMocHelpers::SignalData<void()>(8, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'progressCompleted'
        QtMocHelpers::SignalData<void(bool, const QString &)>(9, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::Bool, 10 }, { QMetaType::QString, 11 },
        }}),
        // Slot 'startProgress'
        QtMocHelpers::SlotData<void(int, const QString &)>(12, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::Int, 13 }, { QMetaType::QString, 14 },
        }}),
        // Slot 'startProgress'
        QtMocHelpers::SlotData<void(int)>(12, 2, QMC::AccessPublic | QMC::MethodCloned, QMetaType::Void, {{
            { QMetaType::Int, 13 },
        }}),
        // Slot 'updateProgress'
        QtMocHelpers::SlotData<void(int, const QString &)>(15, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::Int, 16 }, { QMetaType::QString, 14 },
        }}),
        // Slot 'updateProgress'
        QtMocHelpers::SlotData<void(int)>(15, 2, QMC::AccessPublic | QMC::MethodCloned, QMetaType::Void, {{
            { QMetaType::Int, 16 },
        }}),
        // Slot 'setProgress'
        QtMocHelpers::SlotData<void(double, const QString &)>(17, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::Double, 18 }, { QMetaType::QString, 14 },
        }}),
        // Slot 'setProgress'
        QtMocHelpers::SlotData<void(double)>(17, 2, QMC::AccessPublic | QMC::MethodCloned, QMetaType::Void, {{
            { QMetaType::Double, 18 },
        }}),
        // Slot 'pauseProgress'
        QtMocHelpers::SlotData<void()>(19, 2, QMC::AccessPublic, QMetaType::Void),
        // Slot 'resumeProgress'
        QtMocHelpers::SlotData<void()>(20, 2, QMC::AccessPublic, QMetaType::Void),
        // Slot 'stopProgress'
        QtMocHelpers::SlotData<void()>(21, 2, QMC::AccessPublic, QMetaType::Void),
        // Slot 'completeProgress'
        QtMocHelpers::SlotData<void(const QString &)>(22, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::QString, 23 },
        }}),
        // Slot 'completeProgress'
        QtMocHelpers::SlotData<void()>(22, 2, QMC::AccessPublic | QMC::MethodCloned, QMetaType::Void),
        // Slot 'resetProgress'
        QtMocHelpers::SlotData<void()>(24, 2, QMC::AccessPublic, QMetaType::Void),
        // Slot 'addSubTask'
        QtMocHelpers::SlotData<void(const QString &, int)>(25, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::QString, 26 }, { QMetaType::Int, 27 },
        }}),
        // Slot 'updateSubTask'
        QtMocHelpers::SlotData<void(const QString &, int)>(28, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::QString, 26 }, { QMetaType::Int, 16 },
        }}),
        // Slot 'updateTimeCalculations'
        QtMocHelpers::SlotData<void()>(29, 2, QMC::AccessPrivate, QMetaType::Void),
    };
    QtMocHelpers::UintData qt_properties {
        // property 'progress'
        QtMocHelpers::PropertyData<double>(18, QMetaType::Double, QMC::DefaultPropertyFlags, 0),
        // property 'currentTask'
        QtMocHelpers::PropertyData<QString>(30, QMetaType::QString, QMC::DefaultPropertyFlags, 1),
        // property 'timeRemaining'
        QtMocHelpers::PropertyData<QString>(31, QMetaType::QString, QMC::DefaultPropertyFlags, 2),
        // property 'elapsedTime'
        QtMocHelpers::PropertyData<QString>(32, QMetaType::QString, QMC::DefaultPropertyFlags, 3),
        // property 'isPaused'
        QtMocHelpers::PropertyData<bool>(33, QMetaType::Bool, QMC::DefaultPropertyFlags, 4),
        // property 'isStopped'
        QtMocHelpers::PropertyData<bool>(34, QMetaType::Bool, QMC::DefaultPropertyFlags, 5),
        // property 'isRunning'
        QtMocHelpers::PropertyData<bool>(35, QMetaType::Bool, QMC::DefaultPropertyFlags, 6),
    };
    QtMocHelpers::UintData qt_enums {
    };
    return QtMocHelpers::metaObjectData<ProgressTracker, qt_meta_tag_ZN15ProgressTrackerE_t>(QMC::MetaObjectFlag{}, qt_stringData,
            qt_methods, qt_properties, qt_enums);
}
Q_CONSTINIT const QMetaObject ProgressTracker::staticMetaObject = { {
    QMetaObject::SuperData::link<QObject::staticMetaObject>(),
    qt_staticMetaObjectStaticContent<qt_meta_tag_ZN15ProgressTrackerE_t>.stringdata,
    qt_staticMetaObjectStaticContent<qt_meta_tag_ZN15ProgressTrackerE_t>.data,
    qt_static_metacall,
    nullptr,
    qt_staticMetaObjectRelocatingContent<qt_meta_tag_ZN15ProgressTrackerE_t>.metaTypes,
    nullptr
} };

void ProgressTracker::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    auto *_t = static_cast<ProgressTracker *>(_o);
    if (_c == QMetaObject::InvokeMetaMethod) {
        switch (_id) {
        case 0: _t->progressChanged(); break;
        case 1: _t->currentTaskChanged(); break;
        case 2: _t->timeRemainingChanged(); break;
        case 3: _t->elapsedTimeChanged(); break;
        case 4: _t->isPausedChanged(); break;
        case 5: _t->isStoppedChanged(); break;
        case 6: _t->isRunningChanged(); break;
        case 7: _t->progressCompleted((*reinterpret_cast< std::add_pointer_t<bool>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<QString>>(_a[2]))); break;
        case 8: _t->startProgress((*reinterpret_cast< std::add_pointer_t<int>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<QString>>(_a[2]))); break;
        case 9: _t->startProgress((*reinterpret_cast< std::add_pointer_t<int>>(_a[1]))); break;
        case 10: _t->updateProgress((*reinterpret_cast< std::add_pointer_t<int>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<QString>>(_a[2]))); break;
        case 11: _t->updateProgress((*reinterpret_cast< std::add_pointer_t<int>>(_a[1]))); break;
        case 12: _t->setProgress((*reinterpret_cast< std::add_pointer_t<double>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<QString>>(_a[2]))); break;
        case 13: _t->setProgress((*reinterpret_cast< std::add_pointer_t<double>>(_a[1]))); break;
        case 14: _t->pauseProgress(); break;
        case 15: _t->resumeProgress(); break;
        case 16: _t->stopProgress(); break;
        case 17: _t->completeProgress((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1]))); break;
        case 18: _t->completeProgress(); break;
        case 19: _t->resetProgress(); break;
        case 20: _t->addSubTask((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<int>>(_a[2]))); break;
        case 21: _t->updateSubTask((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<int>>(_a[2]))); break;
        case 22: _t->updateTimeCalculations(); break;
        default: ;
        }
    }
    if (_c == QMetaObject::IndexOfMethod) {
        if (QtMocHelpers::indexOfMethod<void (ProgressTracker::*)()>(_a, &ProgressTracker::progressChanged, 0))
            return;
        if (QtMocHelpers::indexOfMethod<void (ProgressTracker::*)()>(_a, &ProgressTracker::currentTaskChanged, 1))
            return;
        if (QtMocHelpers::indexOfMethod<void (ProgressTracker::*)()>(_a, &ProgressTracker::timeRemainingChanged, 2))
            return;
        if (QtMocHelpers::indexOfMethod<void (ProgressTracker::*)()>(_a, &ProgressTracker::elapsedTimeChanged, 3))
            return;
        if (QtMocHelpers::indexOfMethod<void (ProgressTracker::*)()>(_a, &ProgressTracker::isPausedChanged, 4))
            return;
        if (QtMocHelpers::indexOfMethod<void (ProgressTracker::*)()>(_a, &ProgressTracker::isStoppedChanged, 5))
            return;
        if (QtMocHelpers::indexOfMethod<void (ProgressTracker::*)()>(_a, &ProgressTracker::isRunningChanged, 6))
            return;
        if (QtMocHelpers::indexOfMethod<void (ProgressTracker::*)(bool , const QString & )>(_a, &ProgressTracker::progressCompleted, 7))
            return;
    }
    if (_c == QMetaObject::ReadProperty) {
        void *_v = _a[0];
        switch (_id) {
        case 0: *reinterpret_cast<double*>(_v) = _t->progress(); break;
        case 1: *reinterpret_cast<QString*>(_v) = _t->currentTask(); break;
        case 2: *reinterpret_cast<QString*>(_v) = _t->timeRemaining(); break;
        case 3: *reinterpret_cast<QString*>(_v) = _t->elapsedTime(); break;
        case 4: *reinterpret_cast<bool*>(_v) = _t->isPaused(); break;
        case 5: *reinterpret_cast<bool*>(_v) = _t->isStopped(); break;
        case 6: *reinterpret_cast<bool*>(_v) = _t->isRunning(); break;
        default: break;
        }
    }
}

const QMetaObject *ProgressTracker::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *ProgressTracker::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_staticMetaObjectStaticContent<qt_meta_tag_ZN15ProgressTrackerE_t>.strings))
        return static_cast<void*>(this);
    return QObject::qt_metacast(_clname);
}

int ProgressTracker::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QObject::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 23)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 23;
    }
    if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 23)
            *reinterpret_cast<QMetaType *>(_a[0]) = QMetaType();
        _id -= 23;
    }
    if (_c == QMetaObject::ReadProperty || _c == QMetaObject::WriteProperty
            || _c == QMetaObject::ResetProperty || _c == QMetaObject::BindableProperty
            || _c == QMetaObject::RegisterPropertyMetaType) {
        qt_static_metacall(this, _c, _id, _a);
        _id -= 7;
    }
    return _id;
}

// SIGNAL 0
void ProgressTracker::progressChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 0, nullptr);
}

// SIGNAL 1
void ProgressTracker::currentTaskChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 1, nullptr);
}

// SIGNAL 2
void ProgressTracker::timeRemainingChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 2, nullptr);
}

// SIGNAL 3
void ProgressTracker::elapsedTimeChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 3, nullptr);
}

// SIGNAL 4
void ProgressTracker::isPausedChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 4, nullptr);
}

// SIGNAL 5
void ProgressTracker::isStoppedChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 5, nullptr);
}

// SIGNAL 6
void ProgressTracker::isRunningChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 6, nullptr);
}

// SIGNAL 7
void ProgressTracker::progressCompleted(bool _t1, const QString & _t2)
{
    QMetaObject::activate<void>(this, &staticMetaObject, 7, nullptr, _t1, _t2);
}
QT_WARNING_POP
