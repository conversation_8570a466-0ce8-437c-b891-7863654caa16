/****************************************************************************
** Meta object code from reading C++ file 'AnalysisSettings.h'
**
** Created by: The Qt Meta Object Compiler version 69 (Qt 6.9.0)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include "../../../src/core/AnalysisSettings.h"
#include <QtCore/qmetatype.h>

#include <QtCore/qtmochelpers.h>

#include <memory>


#include <QtCore/qxptype_traits.h>
#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'AnalysisSettings.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 69
#error "This file was generated using the moc from 6.9.0. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

#ifndef Q_CONSTINIT
#define Q_CONSTINIT
#endif

QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
QT_WARNING_DISABLE_GCC("-Wuseless-cast")
namespace {
struct qt_meta_tag_ZN16AnalysisSettingsE_t {};
} // unnamed namespace

template <> constexpr inline auto AnalysisSettings::qt_create_metaobjectdata<qt_meta_tag_ZN16AnalysisSettingsE_t>()
{
    namespace QMC = QtMocConstants;
    QtMocHelpers::StringRefStorage qt_stringData {
        "AnalysisSettings",
        "chunkSkipFactorChanged",
        "",
        "maxConcurrentAnalysisChanged",
        "enableFastModeChanged",
        "analysisQualityChanged",
        "confidenceThresholdChanged",
        "enableSpeakerDiarizationChanged",
        "enableMusicDetectionChanged",
        "enableSFXDetectionChanged",
        "sampleRateChanged",
        "bitDepthChanged",
        "mfccCoefficientsChanged",
        "windowSizeChanged",
        "hopLengthChanged",
        "spectralOverlapChanged",
        "dialogueFreqMinChanged",
        "dialogueFreqMaxChanged",
        "musicFreqMinChanged",
        "musicFreqMaxChanged",
        "sfxFreqMinChanged",
        "sfxFreqMaxChanged",
        "noiseFloorThresholdChanged",
        "silenceThresholdChanged",
        "silenceMinDurationChanged",
        "modelSelectionChanged",
        "enableSpectralFeaturesChanged",
        "enableTemporalFeaturesChanged",
        "temporalSmoothingWindowChanged",
        "contextWindowSizeChanged",
        "errorHandlingStrategyChanged",
        "lowConfidenceBehaviorChanged",
        "enableValidationRulesChanged",
        "enableQualityAssuranceChanged",
        "speedAccuracyBalanceChanged",
        "enableMultiPassAnalysisChanged",
        "enableCrossValidationChanged",
        "batchChunkSizeChanged",
        "batchOverlapRatioChanged",
        "settingsChanged",
        "presetsChanged",
        "setChunkSkipFactor",
        "factor",
        "setMaxConcurrentAnalysis",
        "maxConcurrent",
        "setEnableFastMode",
        "enable",
        "setAnalysisQuality",
        "quality",
        "setConfidenceThreshold",
        "threshold",
        "setEnableSpeakerDiarization",
        "setEnableMusicDetection",
        "setEnableSFXDetection",
        "setSampleRate",
        "sampleRate",
        "setBitDepth",
        "bitDepth",
        "setMfccCoefficients",
        "coefficients",
        "setWindowSize",
        "windowSize",
        "setHopLength",
        "hopLength",
        "setSpectralOverlap",
        "overlap",
        "setDialogueFreqMin",
        "freq",
        "setDialogueFreqMax",
        "setMusicFreqMin",
        "setMusicFreqMax",
        "setSfxFreqMin",
        "setSfxFreqMax",
        "setNoiseFloorThreshold",
        "setSilenceThreshold",
        "setSilenceMinDuration",
        "duration",
        "setModelSelection",
        "model",
        "setEnableSpectralFeatures",
        "setEnableTemporalFeatures",
        "setTemporalSmoothingWindow",
        "window",
        "setContextWindowSize",
        "size",
        "setErrorHandlingStrategy",
        "strategy",
        "setLowConfidenceBehavior",
        "behavior",
        "setEnableValidationRules",
        "setEnableQualityAssurance",
        "setSpeedAccuracyBalance",
        "balance",
        "setEnableMultiPassAnalysis",
        "setEnableCrossValidation",
        "setBatchChunkSize",
        "setBatchOverlapRatio",
        "ratio",
        "loadSettings",
        "saveSettings",
        "resetToDefaults",
        "applyQualityPreset",
        "QualityLevel",
        "getAllSettings",
        "QVariantMap",
        "applySettings",
        "settings",
        "getProcessingTimeMultiplier",
        "getOptimizedChunkSize",
        "baseChunkSize",
        "savePreset",
        "presetName",
        "loadPreset",
        "deletePreset",
        "getAvailablePresets",
        "getPresetDescription",
        "isBuiltInPreset",
        "chunkSkipFactor",
        "maxConcurrentAnalysis",
        "enableFastMode",
        "analysisQuality",
        "confidenceThreshold",
        "enableSpeakerDiarization",
        "enableMusicDetection",
        "enableSFXDetection",
        "mfccCoefficients",
        "spectralOverlap",
        "dialogueFreqMin",
        "dialogueFreqMax",
        "musicFreqMin",
        "musicFreqMax",
        "sfxFreqMin",
        "sfxFreqMax",
        "noiseFloorThreshold",
        "silenceThreshold",
        "silenceMinDuration",
        "modelSelection",
        "enableSpectralFeatures",
        "enableTemporalFeatures",
        "temporalSmoothingWindow",
        "contextWindowSize",
        "errorHandlingStrategy",
        "lowConfidenceBehavior",
        "enableValidationRules",
        "enableQualityAssurance",
        "speedAccuracyBalance",
        "enableMultiPassAnalysis",
        "enableCrossValidation",
        "batchChunkSize",
        "batchOverlapRatio",
        "Fast",
        "Balanced",
        "Accurate",
        "Maximum",
        "ClassificationType",
        "Speech",
        "Music",
        "SFX",
        "Ambience",
        "Silence",
        "Unknown"
    };

    QtMocHelpers::UintData qt_methods {
        // Signal 'chunkSkipFactorChanged'
        QtMocHelpers::SignalData<void()>(1, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'maxConcurrentAnalysisChanged'
        QtMocHelpers::SignalData<void()>(3, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'enableFastModeChanged'
        QtMocHelpers::SignalData<void()>(4, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'analysisQualityChanged'
        QtMocHelpers::SignalData<void()>(5, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'confidenceThresholdChanged'
        QtMocHelpers::SignalData<void()>(6, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'enableSpeakerDiarizationChanged'
        QtMocHelpers::SignalData<void()>(7, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'enableMusicDetectionChanged'
        QtMocHelpers::SignalData<void()>(8, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'enableSFXDetectionChanged'
        QtMocHelpers::SignalData<void()>(9, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'sampleRateChanged'
        QtMocHelpers::SignalData<void()>(10, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'bitDepthChanged'
        QtMocHelpers::SignalData<void()>(11, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'mfccCoefficientsChanged'
        QtMocHelpers::SignalData<void()>(12, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'windowSizeChanged'
        QtMocHelpers::SignalData<void()>(13, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'hopLengthChanged'
        QtMocHelpers::SignalData<void()>(14, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'spectralOverlapChanged'
        QtMocHelpers::SignalData<void()>(15, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'dialogueFreqMinChanged'
        QtMocHelpers::SignalData<void()>(16, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'dialogueFreqMaxChanged'
        QtMocHelpers::SignalData<void()>(17, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'musicFreqMinChanged'
        QtMocHelpers::SignalData<void()>(18, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'musicFreqMaxChanged'
        QtMocHelpers::SignalData<void()>(19, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'sfxFreqMinChanged'
        QtMocHelpers::SignalData<void()>(20, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'sfxFreqMaxChanged'
        QtMocHelpers::SignalData<void()>(21, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'noiseFloorThresholdChanged'
        QtMocHelpers::SignalData<void()>(22, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'silenceThresholdChanged'
        QtMocHelpers::SignalData<void()>(23, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'silenceMinDurationChanged'
        QtMocHelpers::SignalData<void()>(24, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'modelSelectionChanged'
        QtMocHelpers::SignalData<void()>(25, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'enableSpectralFeaturesChanged'
        QtMocHelpers::SignalData<void()>(26, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'enableTemporalFeaturesChanged'
        QtMocHelpers::SignalData<void()>(27, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'temporalSmoothingWindowChanged'
        QtMocHelpers::SignalData<void()>(28, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'contextWindowSizeChanged'
        QtMocHelpers::SignalData<void()>(29, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'errorHandlingStrategyChanged'
        QtMocHelpers::SignalData<void()>(30, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'lowConfidenceBehaviorChanged'
        QtMocHelpers::SignalData<void()>(31, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'enableValidationRulesChanged'
        QtMocHelpers::SignalData<void()>(32, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'enableQualityAssuranceChanged'
        QtMocHelpers::SignalData<void()>(33, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'speedAccuracyBalanceChanged'
        QtMocHelpers::SignalData<void()>(34, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'enableMultiPassAnalysisChanged'
        QtMocHelpers::SignalData<void()>(35, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'enableCrossValidationChanged'
        QtMocHelpers::SignalData<void()>(36, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'batchChunkSizeChanged'
        QtMocHelpers::SignalData<void()>(37, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'batchOverlapRatioChanged'
        QtMocHelpers::SignalData<void()>(38, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'settingsChanged'
        QtMocHelpers::SignalData<void()>(39, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'presetsChanged'
        QtMocHelpers::SignalData<void()>(40, 2, QMC::AccessPublic, QMetaType::Void),
        // Slot 'setChunkSkipFactor'
        QtMocHelpers::SlotData<void(int)>(41, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::Int, 42 },
        }}),
        // Slot 'setMaxConcurrentAnalysis'
        QtMocHelpers::SlotData<void(int)>(43, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::Int, 44 },
        }}),
        // Slot 'setEnableFastMode'
        QtMocHelpers::SlotData<void(bool)>(45, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::Bool, 46 },
        }}),
        // Slot 'setAnalysisQuality'
        QtMocHelpers::SlotData<void(int)>(47, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::Int, 48 },
        }}),
        // Slot 'setConfidenceThreshold'
        QtMocHelpers::SlotData<void(double)>(49, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::Double, 50 },
        }}),
        // Slot 'setEnableSpeakerDiarization'
        QtMocHelpers::SlotData<void(bool)>(51, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::Bool, 46 },
        }}),
        // Slot 'setEnableMusicDetection'
        QtMocHelpers::SlotData<void(bool)>(52, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::Bool, 46 },
        }}),
        // Slot 'setEnableSFXDetection'
        QtMocHelpers::SlotData<void(bool)>(53, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::Bool, 46 },
        }}),
        // Slot 'setSampleRate'
        QtMocHelpers::SlotData<void(int)>(54, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::Int, 55 },
        }}),
        // Slot 'setBitDepth'
        QtMocHelpers::SlotData<void(int)>(56, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::Int, 57 },
        }}),
        // Slot 'setMfccCoefficients'
        QtMocHelpers::SlotData<void(int)>(58, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::Int, 59 },
        }}),
        // Slot 'setWindowSize'
        QtMocHelpers::SlotData<void(double)>(60, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::Double, 61 },
        }}),
        // Slot 'setHopLength'
        QtMocHelpers::SlotData<void(double)>(62, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::Double, 63 },
        }}),
        // Slot 'setSpectralOverlap'
        QtMocHelpers::SlotData<void(double)>(64, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::Double, 65 },
        }}),
        // Slot 'setDialogueFreqMin'
        QtMocHelpers::SlotData<void(int)>(66, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::Int, 67 },
        }}),
        // Slot 'setDialogueFreqMax'
        QtMocHelpers::SlotData<void(int)>(68, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::Int, 67 },
        }}),
        // Slot 'setMusicFreqMin'
        QtMocHelpers::SlotData<void(int)>(69, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::Int, 67 },
        }}),
        // Slot 'setMusicFreqMax'
        QtMocHelpers::SlotData<void(int)>(70, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::Int, 67 },
        }}),
        // Slot 'setSfxFreqMin'
        QtMocHelpers::SlotData<void(int)>(71, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::Int, 67 },
        }}),
        // Slot 'setSfxFreqMax'
        QtMocHelpers::SlotData<void(int)>(72, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::Int, 67 },
        }}),
        // Slot 'setNoiseFloorThreshold'
        QtMocHelpers::SlotData<void(double)>(73, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::Double, 50 },
        }}),
        // Slot 'setSilenceThreshold'
        QtMocHelpers::SlotData<void(double)>(74, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::Double, 50 },
        }}),
        // Slot 'setSilenceMinDuration'
        QtMocHelpers::SlotData<void(double)>(75, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::Double, 76 },
        }}),
        // Slot 'setModelSelection'
        QtMocHelpers::SlotData<void(const QString &)>(77, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::QString, 78 },
        }}),
        // Slot 'setEnableSpectralFeatures'
        QtMocHelpers::SlotData<void(bool)>(79, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::Bool, 46 },
        }}),
        // Slot 'setEnableTemporalFeatures'
        QtMocHelpers::SlotData<void(bool)>(80, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::Bool, 46 },
        }}),
        // Slot 'setTemporalSmoothingWindow'
        QtMocHelpers::SlotData<void(double)>(81, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::Double, 82 },
        }}),
        // Slot 'setContextWindowSize'
        QtMocHelpers::SlotData<void(double)>(83, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::Double, 84 },
        }}),
        // Slot 'setErrorHandlingStrategy'
        QtMocHelpers::SlotData<void(const QString &)>(85, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::QString, 86 },
        }}),
        // Slot 'setLowConfidenceBehavior'
        QtMocHelpers::SlotData<void(const QString &)>(87, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::QString, 88 },
        }}),
        // Slot 'setEnableValidationRules'
        QtMocHelpers::SlotData<void(bool)>(89, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::Bool, 46 },
        }}),
        // Slot 'setEnableQualityAssurance'
        QtMocHelpers::SlotData<void(bool)>(90, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::Bool, 46 },
        }}),
        // Slot 'setSpeedAccuracyBalance'
        QtMocHelpers::SlotData<void(double)>(91, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::Double, 92 },
        }}),
        // Slot 'setEnableMultiPassAnalysis'
        QtMocHelpers::SlotData<void(bool)>(93, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::Bool, 46 },
        }}),
        // Slot 'setEnableCrossValidation'
        QtMocHelpers::SlotData<void(bool)>(94, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::Bool, 46 },
        }}),
        // Slot 'setBatchChunkSize'
        QtMocHelpers::SlotData<void(int)>(95, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::Int, 84 },
        }}),
        // Slot 'setBatchOverlapRatio'
        QtMocHelpers::SlotData<void(double)>(96, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::Double, 97 },
        }}),
        // Slot 'loadSettings'
        QtMocHelpers::SlotData<void()>(98, 2, QMC::AccessPublic, QMetaType::Void),
        // Slot 'saveSettings'
        QtMocHelpers::SlotData<void()>(99, 2, QMC::AccessPublic, QMetaType::Void),
        // Slot 'resetToDefaults'
        QtMocHelpers::SlotData<void()>(100, 2, QMC::AccessPublic, QMetaType::Void),
        // Slot 'applyQualityPreset'
        QtMocHelpers::SlotData<void(QualityLevel)>(101, 2, QMC::AccessPublic, QMetaType::Void, {{
            { 0x80000000 | 102, 48 },
        }}),
        // Slot 'getAllSettings'
        QtMocHelpers::SlotData<QVariantMap() const>(103, 2, QMC::AccessPublic, 0x80000000 | 104),
        // Slot 'applySettings'
        QtMocHelpers::SlotData<void(const QVariantMap &)>(105, 2, QMC::AccessPublic, QMetaType::Void, {{
            { 0x80000000 | 104, 106 },
        }}),
        // Slot 'getProcessingTimeMultiplier'
        QtMocHelpers::SlotData<double() const>(107, 2, QMC::AccessPublic, QMetaType::Double),
        // Slot 'getOptimizedChunkSize'
        QtMocHelpers::SlotData<qint64(qint64) const>(108, 2, QMC::AccessPublic, QMetaType::LongLong, {{
            { QMetaType::LongLong, 109 },
        }}),
        // Slot 'savePreset'
        QtMocHelpers::SlotData<bool(const QString &)>(110, 2, QMC::AccessPublic, QMetaType::Bool, {{
            { QMetaType::QString, 111 },
        }}),
        // Slot 'loadPreset'
        QtMocHelpers::SlotData<bool(const QString &)>(112, 2, QMC::AccessPublic, QMetaType::Bool, {{
            { QMetaType::QString, 111 },
        }}),
        // Slot 'deletePreset'
        QtMocHelpers::SlotData<bool(const QString &)>(113, 2, QMC::AccessPublic, QMetaType::Bool, {{
            { QMetaType::QString, 111 },
        }}),
        // Slot 'getAvailablePresets'
        QtMocHelpers::SlotData<QStringList() const>(114, 2, QMC::AccessPublic, QMetaType::QStringList),
        // Slot 'getPresetDescription'
        QtMocHelpers::SlotData<QString(const QString &) const>(115, 2, QMC::AccessPublic, QMetaType::QString, {{
            { QMetaType::QString, 111 },
        }}),
        // Slot 'isBuiltInPreset'
        QtMocHelpers::SlotData<bool(const QString &) const>(116, 2, QMC::AccessPublic, QMetaType::Bool, {{
            { QMetaType::QString, 111 },
        }}),
    };
    QtMocHelpers::UintData qt_properties {
        // property 'chunkSkipFactor'
        QtMocHelpers::PropertyData<int>(117, QMetaType::Int, QMC::DefaultPropertyFlags | QMC::Writable | QMC::StdCppSet, 0),
        // property 'maxConcurrentAnalysis'
        QtMocHelpers::PropertyData<int>(118, QMetaType::Int, QMC::DefaultPropertyFlags | QMC::Writable | QMC::StdCppSet, 1),
        // property 'enableFastMode'
        QtMocHelpers::PropertyData<bool>(119, QMetaType::Bool, QMC::DefaultPropertyFlags | QMC::Writable | QMC::StdCppSet, 2),
        // property 'analysisQuality'
        QtMocHelpers::PropertyData<int>(120, QMetaType::Int, QMC::DefaultPropertyFlags | QMC::Writable | QMC::StdCppSet, 3),
        // property 'confidenceThreshold'
        QtMocHelpers::PropertyData<double>(121, QMetaType::Double, QMC::DefaultPropertyFlags | QMC::Writable | QMC::StdCppSet, 4),
        // property 'enableSpeakerDiarization'
        QtMocHelpers::PropertyData<bool>(122, QMetaType::Bool, QMC::DefaultPropertyFlags | QMC::Writable | QMC::StdCppSet, 5),
        // property 'enableMusicDetection'
        QtMocHelpers::PropertyData<bool>(123, QMetaType::Bool, QMC::DefaultPropertyFlags | QMC::Writable | QMC::StdCppSet, 6),
        // property 'enableSFXDetection'
        QtMocHelpers::PropertyData<bool>(124, QMetaType::Bool, QMC::DefaultPropertyFlags | QMC::Writable | QMC::StdCppSet, 7),
        // property 'sampleRate'
        QtMocHelpers::PropertyData<int>(55, QMetaType::Int, QMC::DefaultPropertyFlags | QMC::Writable | QMC::StdCppSet, 8),
        // property 'bitDepth'
        QtMocHelpers::PropertyData<int>(57, QMetaType::Int, QMC::DefaultPropertyFlags | QMC::Writable | QMC::StdCppSet, 9),
        // property 'mfccCoefficients'
        QtMocHelpers::PropertyData<int>(125, QMetaType::Int, QMC::DefaultPropertyFlags | QMC::Writable | QMC::StdCppSet, 10),
        // property 'windowSize'
        QtMocHelpers::PropertyData<double>(61, QMetaType::Double, QMC::DefaultPropertyFlags | QMC::Writable | QMC::StdCppSet, 11),
        // property 'hopLength'
        QtMocHelpers::PropertyData<double>(63, QMetaType::Double, QMC::DefaultPropertyFlags | QMC::Writable | QMC::StdCppSet, 12),
        // property 'spectralOverlap'
        QtMocHelpers::PropertyData<double>(126, QMetaType::Double, QMC::DefaultPropertyFlags | QMC::Writable | QMC::StdCppSet, 13),
        // property 'dialogueFreqMin'
        QtMocHelpers::PropertyData<int>(127, QMetaType::Int, QMC::DefaultPropertyFlags | QMC::Writable | QMC::StdCppSet, 14),
        // property 'dialogueFreqMax'
        QtMocHelpers::PropertyData<int>(128, QMetaType::Int, QMC::DefaultPropertyFlags | QMC::Writable | QMC::StdCppSet, 15),
        // property 'musicFreqMin'
        QtMocHelpers::PropertyData<int>(129, QMetaType::Int, QMC::DefaultPropertyFlags | QMC::Writable | QMC::StdCppSet, 16),
        // property 'musicFreqMax'
        QtMocHelpers::PropertyData<int>(130, QMetaType::Int, QMC::DefaultPropertyFlags | QMC::Writable | QMC::StdCppSet, 17),
        // property 'sfxFreqMin'
        QtMocHelpers::PropertyData<int>(131, QMetaType::Int, QMC::DefaultPropertyFlags | QMC::Writable | QMC::StdCppSet, 18),
        // property 'sfxFreqMax'
        QtMocHelpers::PropertyData<int>(132, QMetaType::Int, QMC::DefaultPropertyFlags | QMC::Writable | QMC::StdCppSet, 19),
        // property 'noiseFloorThreshold'
        QtMocHelpers::PropertyData<double>(133, QMetaType::Double, QMC::DefaultPropertyFlags | QMC::Writable | QMC::StdCppSet, 20),
        // property 'silenceThreshold'
        QtMocHelpers::PropertyData<double>(134, QMetaType::Double, QMC::DefaultPropertyFlags | QMC::Writable | QMC::StdCppSet, 21),
        // property 'silenceMinDuration'
        QtMocHelpers::PropertyData<double>(135, QMetaType::Double, QMC::DefaultPropertyFlags | QMC::Writable | QMC::StdCppSet, 22),
        // property 'modelSelection'
        QtMocHelpers::PropertyData<QString>(136, QMetaType::QString, QMC::DefaultPropertyFlags | QMC::Writable | QMC::StdCppSet, 23),
        // property 'enableSpectralFeatures'
        QtMocHelpers::PropertyData<bool>(137, QMetaType::Bool, QMC::DefaultPropertyFlags | QMC::Writable | QMC::StdCppSet, 24),
        // property 'enableTemporalFeatures'
        QtMocHelpers::PropertyData<bool>(138, QMetaType::Bool, QMC::DefaultPropertyFlags | QMC::Writable | QMC::StdCppSet, 25),
        // property 'temporalSmoothingWindow'
        QtMocHelpers::PropertyData<double>(139, QMetaType::Double, QMC::DefaultPropertyFlags | QMC::Writable | QMC::StdCppSet, 26),
        // property 'contextWindowSize'
        QtMocHelpers::PropertyData<double>(140, QMetaType::Double, QMC::DefaultPropertyFlags | QMC::Writable | QMC::StdCppSet, 27),
        // property 'errorHandlingStrategy'
        QtMocHelpers::PropertyData<QString>(141, QMetaType::QString, QMC::DefaultPropertyFlags | QMC::Writable | QMC::StdCppSet, 28),
        // property 'lowConfidenceBehavior'
        QtMocHelpers::PropertyData<QString>(142, QMetaType::QString, QMC::DefaultPropertyFlags | QMC::Writable | QMC::StdCppSet, 29),
        // property 'enableValidationRules'
        QtMocHelpers::PropertyData<bool>(143, QMetaType::Bool, QMC::DefaultPropertyFlags | QMC::Writable | QMC::StdCppSet, 30),
        // property 'enableQualityAssurance'
        QtMocHelpers::PropertyData<bool>(144, QMetaType::Bool, QMC::DefaultPropertyFlags | QMC::Writable | QMC::StdCppSet, 31),
        // property 'speedAccuracyBalance'
        QtMocHelpers::PropertyData<double>(145, QMetaType::Double, QMC::DefaultPropertyFlags | QMC::Writable | QMC::StdCppSet, 32),
        // property 'enableMultiPassAnalysis'
        QtMocHelpers::PropertyData<bool>(146, QMetaType::Bool, QMC::DefaultPropertyFlags | QMC::Writable | QMC::StdCppSet, 33),
        // property 'enableCrossValidation'
        QtMocHelpers::PropertyData<bool>(147, QMetaType::Bool, QMC::DefaultPropertyFlags | QMC::Writable | QMC::StdCppSet, 34),
        // property 'batchChunkSize'
        QtMocHelpers::PropertyData<int>(148, QMetaType::Int, QMC::DefaultPropertyFlags | QMC::Writable | QMC::StdCppSet, 35),
        // property 'batchOverlapRatio'
        QtMocHelpers::PropertyData<double>(149, QMetaType::Double, QMC::DefaultPropertyFlags | QMC::Writable | QMC::StdCppSet, 36),
    };
    QtMocHelpers::UintData qt_enums {
        // enum 'QualityLevel'
        QtMocHelpers::EnumData<QualityLevel>(102, 102, QMC::EnumFlags{}).add({
            {  150, QualityLevel::Fast },
            {  151, QualityLevel::Balanced },
            {  152, QualityLevel::Accurate },
            {  153, QualityLevel::Maximum },
        }),
        // enum 'ClassificationType'
        QtMocHelpers::EnumData<ClassificationType>(154, 154, QMC::EnumFlags{}).add({
            {  155, ClassificationType::Speech },
            {  156, ClassificationType::Music },
            {  157, ClassificationType::SFX },
            {  158, ClassificationType::Ambience },
            {  159, ClassificationType::Silence },
            {  160, ClassificationType::Unknown },
        }),
    };
    return QtMocHelpers::metaObjectData<AnalysisSettings, qt_meta_tag_ZN16AnalysisSettingsE_t>(QMC::MetaObjectFlag{}, qt_stringData,
            qt_methods, qt_properties, qt_enums);
}
Q_CONSTINIT const QMetaObject AnalysisSettings::staticMetaObject = { {
    QMetaObject::SuperData::link<QObject::staticMetaObject>(),
    qt_staticMetaObjectStaticContent<qt_meta_tag_ZN16AnalysisSettingsE_t>.stringdata,
    qt_staticMetaObjectStaticContent<qt_meta_tag_ZN16AnalysisSettingsE_t>.data,
    qt_static_metacall,
    nullptr,
    qt_staticMetaObjectRelocatingContent<qt_meta_tag_ZN16AnalysisSettingsE_t>.metaTypes,
    nullptr
} };

void AnalysisSettings::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    auto *_t = static_cast<AnalysisSettings *>(_o);
    if (_c == QMetaObject::InvokeMetaMethod) {
        switch (_id) {
        case 0: _t->chunkSkipFactorChanged(); break;
        case 1: _t->maxConcurrentAnalysisChanged(); break;
        case 2: _t->enableFastModeChanged(); break;
        case 3: _t->analysisQualityChanged(); break;
        case 4: _t->confidenceThresholdChanged(); break;
        case 5: _t->enableSpeakerDiarizationChanged(); break;
        case 6: _t->enableMusicDetectionChanged(); break;
        case 7: _t->enableSFXDetectionChanged(); break;
        case 8: _t->sampleRateChanged(); break;
        case 9: _t->bitDepthChanged(); break;
        case 10: _t->mfccCoefficientsChanged(); break;
        case 11: _t->windowSizeChanged(); break;
        case 12: _t->hopLengthChanged(); break;
        case 13: _t->spectralOverlapChanged(); break;
        case 14: _t->dialogueFreqMinChanged(); break;
        case 15: _t->dialogueFreqMaxChanged(); break;
        case 16: _t->musicFreqMinChanged(); break;
        case 17: _t->musicFreqMaxChanged(); break;
        case 18: _t->sfxFreqMinChanged(); break;
        case 19: _t->sfxFreqMaxChanged(); break;
        case 20: _t->noiseFloorThresholdChanged(); break;
        case 21: _t->silenceThresholdChanged(); break;
        case 22: _t->silenceMinDurationChanged(); break;
        case 23: _t->modelSelectionChanged(); break;
        case 24: _t->enableSpectralFeaturesChanged(); break;
        case 25: _t->enableTemporalFeaturesChanged(); break;
        case 26: _t->temporalSmoothingWindowChanged(); break;
        case 27: _t->contextWindowSizeChanged(); break;
        case 28: _t->errorHandlingStrategyChanged(); break;
        case 29: _t->lowConfidenceBehaviorChanged(); break;
        case 30: _t->enableValidationRulesChanged(); break;
        case 31: _t->enableQualityAssuranceChanged(); break;
        case 32: _t->speedAccuracyBalanceChanged(); break;
        case 33: _t->enableMultiPassAnalysisChanged(); break;
        case 34: _t->enableCrossValidationChanged(); break;
        case 35: _t->batchChunkSizeChanged(); break;
        case 36: _t->batchOverlapRatioChanged(); break;
        case 37: _t->settingsChanged(); break;
        case 38: _t->presetsChanged(); break;
        case 39: _t->setChunkSkipFactor((*reinterpret_cast< std::add_pointer_t<int>>(_a[1]))); break;
        case 40: _t->setMaxConcurrentAnalysis((*reinterpret_cast< std::add_pointer_t<int>>(_a[1]))); break;
        case 41: _t->setEnableFastMode((*reinterpret_cast< std::add_pointer_t<bool>>(_a[1]))); break;
        case 42: _t->setAnalysisQuality((*reinterpret_cast< std::add_pointer_t<int>>(_a[1]))); break;
        case 43: _t->setConfidenceThreshold((*reinterpret_cast< std::add_pointer_t<double>>(_a[1]))); break;
        case 44: _t->setEnableSpeakerDiarization((*reinterpret_cast< std::add_pointer_t<bool>>(_a[1]))); break;
        case 45: _t->setEnableMusicDetection((*reinterpret_cast< std::add_pointer_t<bool>>(_a[1]))); break;
        case 46: _t->setEnableSFXDetection((*reinterpret_cast< std::add_pointer_t<bool>>(_a[1]))); break;
        case 47: _t->setSampleRate((*reinterpret_cast< std::add_pointer_t<int>>(_a[1]))); break;
        case 48: _t->setBitDepth((*reinterpret_cast< std::add_pointer_t<int>>(_a[1]))); break;
        case 49: _t->setMfccCoefficients((*reinterpret_cast< std::add_pointer_t<int>>(_a[1]))); break;
        case 50: _t->setWindowSize((*reinterpret_cast< std::add_pointer_t<double>>(_a[1]))); break;
        case 51: _t->setHopLength((*reinterpret_cast< std::add_pointer_t<double>>(_a[1]))); break;
        case 52: _t->setSpectralOverlap((*reinterpret_cast< std::add_pointer_t<double>>(_a[1]))); break;
        case 53: _t->setDialogueFreqMin((*reinterpret_cast< std::add_pointer_t<int>>(_a[1]))); break;
        case 54: _t->setDialogueFreqMax((*reinterpret_cast< std::add_pointer_t<int>>(_a[1]))); break;
        case 55: _t->setMusicFreqMin((*reinterpret_cast< std::add_pointer_t<int>>(_a[1]))); break;
        case 56: _t->setMusicFreqMax((*reinterpret_cast< std::add_pointer_t<int>>(_a[1]))); break;
        case 57: _t->setSfxFreqMin((*reinterpret_cast< std::add_pointer_t<int>>(_a[1]))); break;
        case 58: _t->setSfxFreqMax((*reinterpret_cast< std::add_pointer_t<int>>(_a[1]))); break;
        case 59: _t->setNoiseFloorThreshold((*reinterpret_cast< std::add_pointer_t<double>>(_a[1]))); break;
        case 60: _t->setSilenceThreshold((*reinterpret_cast< std::add_pointer_t<double>>(_a[1]))); break;
        case 61: _t->setSilenceMinDuration((*reinterpret_cast< std::add_pointer_t<double>>(_a[1]))); break;
        case 62: _t->setModelSelection((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1]))); break;
        case 63: _t->setEnableSpectralFeatures((*reinterpret_cast< std::add_pointer_t<bool>>(_a[1]))); break;
        case 64: _t->setEnableTemporalFeatures((*reinterpret_cast< std::add_pointer_t<bool>>(_a[1]))); break;
        case 65: _t->setTemporalSmoothingWindow((*reinterpret_cast< std::add_pointer_t<double>>(_a[1]))); break;
        case 66: _t->setContextWindowSize((*reinterpret_cast< std::add_pointer_t<double>>(_a[1]))); break;
        case 67: _t->setErrorHandlingStrategy((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1]))); break;
        case 68: _t->setLowConfidenceBehavior((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1]))); break;
        case 69: _t->setEnableValidationRules((*reinterpret_cast< std::add_pointer_t<bool>>(_a[1]))); break;
        case 70: _t->setEnableQualityAssurance((*reinterpret_cast< std::add_pointer_t<bool>>(_a[1]))); break;
        case 71: _t->setSpeedAccuracyBalance((*reinterpret_cast< std::add_pointer_t<double>>(_a[1]))); break;
        case 72: _t->setEnableMultiPassAnalysis((*reinterpret_cast< std::add_pointer_t<bool>>(_a[1]))); break;
        case 73: _t->setEnableCrossValidation((*reinterpret_cast< std::add_pointer_t<bool>>(_a[1]))); break;
        case 74: _t->setBatchChunkSize((*reinterpret_cast< std::add_pointer_t<int>>(_a[1]))); break;
        case 75: _t->setBatchOverlapRatio((*reinterpret_cast< std::add_pointer_t<double>>(_a[1]))); break;
        case 76: _t->loadSettings(); break;
        case 77: _t->saveSettings(); break;
        case 78: _t->resetToDefaults(); break;
        case 79: _t->applyQualityPreset((*reinterpret_cast< std::add_pointer_t<QualityLevel>>(_a[1]))); break;
        case 80: { QVariantMap _r = _t->getAllSettings();
            if (_a[0]) *reinterpret_cast< QVariantMap*>(_a[0]) = std::move(_r); }  break;
        case 81: _t->applySettings((*reinterpret_cast< std::add_pointer_t<QVariantMap>>(_a[1]))); break;
        case 82: { double _r = _t->getProcessingTimeMultiplier();
            if (_a[0]) *reinterpret_cast< double*>(_a[0]) = std::move(_r); }  break;
        case 83: { qint64 _r = _t->getOptimizedChunkSize((*reinterpret_cast< std::add_pointer_t<qint64>>(_a[1])));
            if (_a[0]) *reinterpret_cast< qint64*>(_a[0]) = std::move(_r); }  break;
        case 84: { bool _r = _t->savePreset((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1])));
            if (_a[0]) *reinterpret_cast< bool*>(_a[0]) = std::move(_r); }  break;
        case 85: { bool _r = _t->loadPreset((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1])));
            if (_a[0]) *reinterpret_cast< bool*>(_a[0]) = std::move(_r); }  break;
        case 86: { bool _r = _t->deletePreset((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1])));
            if (_a[0]) *reinterpret_cast< bool*>(_a[0]) = std::move(_r); }  break;
        case 87: { QStringList _r = _t->getAvailablePresets();
            if (_a[0]) *reinterpret_cast< QStringList*>(_a[0]) = std::move(_r); }  break;
        case 88: { QString _r = _t->getPresetDescription((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1])));
            if (_a[0]) *reinterpret_cast< QString*>(_a[0]) = std::move(_r); }  break;
        case 89: { bool _r = _t->isBuiltInPreset((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1])));
            if (_a[0]) *reinterpret_cast< bool*>(_a[0]) = std::move(_r); }  break;
        default: ;
        }
    }
    if (_c == QMetaObject::IndexOfMethod) {
        if (QtMocHelpers::indexOfMethod<void (AnalysisSettings::*)()>(_a, &AnalysisSettings::chunkSkipFactorChanged, 0))
            return;
        if (QtMocHelpers::indexOfMethod<void (AnalysisSettings::*)()>(_a, &AnalysisSettings::maxConcurrentAnalysisChanged, 1))
            return;
        if (QtMocHelpers::indexOfMethod<void (AnalysisSettings::*)()>(_a, &AnalysisSettings::enableFastModeChanged, 2))
            return;
        if (QtMocHelpers::indexOfMethod<void (AnalysisSettings::*)()>(_a, &AnalysisSettings::analysisQualityChanged, 3))
            return;
        if (QtMocHelpers::indexOfMethod<void (AnalysisSettings::*)()>(_a, &AnalysisSettings::confidenceThresholdChanged, 4))
            return;
        if (QtMocHelpers::indexOfMethod<void (AnalysisSettings::*)()>(_a, &AnalysisSettings::enableSpeakerDiarizationChanged, 5))
            return;
        if (QtMocHelpers::indexOfMethod<void (AnalysisSettings::*)()>(_a, &AnalysisSettings::enableMusicDetectionChanged, 6))
            return;
        if (QtMocHelpers::indexOfMethod<void (AnalysisSettings::*)()>(_a, &AnalysisSettings::enableSFXDetectionChanged, 7))
            return;
        if (QtMocHelpers::indexOfMethod<void (AnalysisSettings::*)()>(_a, &AnalysisSettings::sampleRateChanged, 8))
            return;
        if (QtMocHelpers::indexOfMethod<void (AnalysisSettings::*)()>(_a, &AnalysisSettings::bitDepthChanged, 9))
            return;
        if (QtMocHelpers::indexOfMethod<void (AnalysisSettings::*)()>(_a, &AnalysisSettings::mfccCoefficientsChanged, 10))
            return;
        if (QtMocHelpers::indexOfMethod<void (AnalysisSettings::*)()>(_a, &AnalysisSettings::windowSizeChanged, 11))
            return;
        if (QtMocHelpers::indexOfMethod<void (AnalysisSettings::*)()>(_a, &AnalysisSettings::hopLengthChanged, 12))
            return;
        if (QtMocHelpers::indexOfMethod<void (AnalysisSettings::*)()>(_a, &AnalysisSettings::spectralOverlapChanged, 13))
            return;
        if (QtMocHelpers::indexOfMethod<void (AnalysisSettings::*)()>(_a, &AnalysisSettings::dialogueFreqMinChanged, 14))
            return;
        if (QtMocHelpers::indexOfMethod<void (AnalysisSettings::*)()>(_a, &AnalysisSettings::dialogueFreqMaxChanged, 15))
            return;
        if (QtMocHelpers::indexOfMethod<void (AnalysisSettings::*)()>(_a, &AnalysisSettings::musicFreqMinChanged, 16))
            return;
        if (QtMocHelpers::indexOfMethod<void (AnalysisSettings::*)()>(_a, &AnalysisSettings::musicFreqMaxChanged, 17))
            return;
        if (QtMocHelpers::indexOfMethod<void (AnalysisSettings::*)()>(_a, &AnalysisSettings::sfxFreqMinChanged, 18))
            return;
        if (QtMocHelpers::indexOfMethod<void (AnalysisSettings::*)()>(_a, &AnalysisSettings::sfxFreqMaxChanged, 19))
            return;
        if (QtMocHelpers::indexOfMethod<void (AnalysisSettings::*)()>(_a, &AnalysisSettings::noiseFloorThresholdChanged, 20))
            return;
        if (QtMocHelpers::indexOfMethod<void (AnalysisSettings::*)()>(_a, &AnalysisSettings::silenceThresholdChanged, 21))
            return;
        if (QtMocHelpers::indexOfMethod<void (AnalysisSettings::*)()>(_a, &AnalysisSettings::silenceMinDurationChanged, 22))
            return;
        if (QtMocHelpers::indexOfMethod<void (AnalysisSettings::*)()>(_a, &AnalysisSettings::modelSelectionChanged, 23))
            return;
        if (QtMocHelpers::indexOfMethod<void (AnalysisSettings::*)()>(_a, &AnalysisSettings::enableSpectralFeaturesChanged, 24))
            return;
        if (QtMocHelpers::indexOfMethod<void (AnalysisSettings::*)()>(_a, &AnalysisSettings::enableTemporalFeaturesChanged, 25))
            return;
        if (QtMocHelpers::indexOfMethod<void (AnalysisSettings::*)()>(_a, &AnalysisSettings::temporalSmoothingWindowChanged, 26))
            return;
        if (QtMocHelpers::indexOfMethod<void (AnalysisSettings::*)()>(_a, &AnalysisSettings::contextWindowSizeChanged, 27))
            return;
        if (QtMocHelpers::indexOfMethod<void (AnalysisSettings::*)()>(_a, &AnalysisSettings::errorHandlingStrategyChanged, 28))
            return;
        if (QtMocHelpers::indexOfMethod<void (AnalysisSettings::*)()>(_a, &AnalysisSettings::lowConfidenceBehaviorChanged, 29))
            return;
        if (QtMocHelpers::indexOfMethod<void (AnalysisSettings::*)()>(_a, &AnalysisSettings::enableValidationRulesChanged, 30))
            return;
        if (QtMocHelpers::indexOfMethod<void (AnalysisSettings::*)()>(_a, &AnalysisSettings::enableQualityAssuranceChanged, 31))
            return;
        if (QtMocHelpers::indexOfMethod<void (AnalysisSettings::*)()>(_a, &AnalysisSettings::speedAccuracyBalanceChanged, 32))
            return;
        if (QtMocHelpers::indexOfMethod<void (AnalysisSettings::*)()>(_a, &AnalysisSettings::enableMultiPassAnalysisChanged, 33))
            return;
        if (QtMocHelpers::indexOfMethod<void (AnalysisSettings::*)()>(_a, &AnalysisSettings::enableCrossValidationChanged, 34))
            return;
        if (QtMocHelpers::indexOfMethod<void (AnalysisSettings::*)()>(_a, &AnalysisSettings::batchChunkSizeChanged, 35))
            return;
        if (QtMocHelpers::indexOfMethod<void (AnalysisSettings::*)()>(_a, &AnalysisSettings::batchOverlapRatioChanged, 36))
            return;
        if (QtMocHelpers::indexOfMethod<void (AnalysisSettings::*)()>(_a, &AnalysisSettings::settingsChanged, 37))
            return;
        if (QtMocHelpers::indexOfMethod<void (AnalysisSettings::*)()>(_a, &AnalysisSettings::presetsChanged, 38))
            return;
    }
    if (_c == QMetaObject::ReadProperty) {
        void *_v = _a[0];
        switch (_id) {
        case 0: *reinterpret_cast<int*>(_v) = _t->chunkSkipFactor(); break;
        case 1: *reinterpret_cast<int*>(_v) = _t->maxConcurrentAnalysis(); break;
        case 2: *reinterpret_cast<bool*>(_v) = _t->enableFastMode(); break;
        case 3: *reinterpret_cast<int*>(_v) = _t->analysisQuality(); break;
        case 4: *reinterpret_cast<double*>(_v) = _t->confidenceThreshold(); break;
        case 5: *reinterpret_cast<bool*>(_v) = _t->enableSpeakerDiarization(); break;
        case 6: *reinterpret_cast<bool*>(_v) = _t->enableMusicDetection(); break;
        case 7: *reinterpret_cast<bool*>(_v) = _t->enableSFXDetection(); break;
        case 8: *reinterpret_cast<int*>(_v) = _t->sampleRate(); break;
        case 9: *reinterpret_cast<int*>(_v) = _t->bitDepth(); break;
        case 10: *reinterpret_cast<int*>(_v) = _t->mfccCoefficients(); break;
        case 11: *reinterpret_cast<double*>(_v) = _t->windowSize(); break;
        case 12: *reinterpret_cast<double*>(_v) = _t->hopLength(); break;
        case 13: *reinterpret_cast<double*>(_v) = _t->spectralOverlap(); break;
        case 14: *reinterpret_cast<int*>(_v) = _t->dialogueFreqMin(); break;
        case 15: *reinterpret_cast<int*>(_v) = _t->dialogueFreqMax(); break;
        case 16: *reinterpret_cast<int*>(_v) = _t->musicFreqMin(); break;
        case 17: *reinterpret_cast<int*>(_v) = _t->musicFreqMax(); break;
        case 18: *reinterpret_cast<int*>(_v) = _t->sfxFreqMin(); break;
        case 19: *reinterpret_cast<int*>(_v) = _t->sfxFreqMax(); break;
        case 20: *reinterpret_cast<double*>(_v) = _t->noiseFloorThreshold(); break;
        case 21: *reinterpret_cast<double*>(_v) = _t->silenceThreshold(); break;
        case 22: *reinterpret_cast<double*>(_v) = _t->silenceMinDuration(); break;
        case 23: *reinterpret_cast<QString*>(_v) = _t->modelSelection(); break;
        case 24: *reinterpret_cast<bool*>(_v) = _t->enableSpectralFeatures(); break;
        case 25: *reinterpret_cast<bool*>(_v) = _t->enableTemporalFeatures(); break;
        case 26: *reinterpret_cast<double*>(_v) = _t->temporalSmoothingWindow(); break;
        case 27: *reinterpret_cast<double*>(_v) = _t->contextWindowSize(); break;
        case 28: *reinterpret_cast<QString*>(_v) = _t->errorHandlingStrategy(); break;
        case 29: *reinterpret_cast<QString*>(_v) = _t->lowConfidenceBehavior(); break;
        case 30: *reinterpret_cast<bool*>(_v) = _t->enableValidationRules(); break;
        case 31: *reinterpret_cast<bool*>(_v) = _t->enableQualityAssurance(); break;
        case 32: *reinterpret_cast<double*>(_v) = _t->speedAccuracyBalance(); break;
        case 33: *reinterpret_cast<bool*>(_v) = _t->enableMultiPassAnalysis(); break;
        case 34: *reinterpret_cast<bool*>(_v) = _t->enableCrossValidation(); break;
        case 35: *reinterpret_cast<int*>(_v) = _t->batchChunkSize(); break;
        case 36: *reinterpret_cast<double*>(_v) = _t->batchOverlapRatio(); break;
        default: break;
        }
    }
    if (_c == QMetaObject::WriteProperty) {
        void *_v = _a[0];
        switch (_id) {
        case 0: _t->setChunkSkipFactor(*reinterpret_cast<int*>(_v)); break;
        case 1: _t->setMaxConcurrentAnalysis(*reinterpret_cast<int*>(_v)); break;
        case 2: _t->setEnableFastMode(*reinterpret_cast<bool*>(_v)); break;
        case 3: _t->setAnalysisQuality(*reinterpret_cast<int*>(_v)); break;
        case 4: _t->setConfidenceThreshold(*reinterpret_cast<double*>(_v)); break;
        case 5: _t->setEnableSpeakerDiarization(*reinterpret_cast<bool*>(_v)); break;
        case 6: _t->setEnableMusicDetection(*reinterpret_cast<bool*>(_v)); break;
        case 7: _t->setEnableSFXDetection(*reinterpret_cast<bool*>(_v)); break;
        case 8: _t->setSampleRate(*reinterpret_cast<int*>(_v)); break;
        case 9: _t->setBitDepth(*reinterpret_cast<int*>(_v)); break;
        case 10: _t->setMfccCoefficients(*reinterpret_cast<int*>(_v)); break;
        case 11: _t->setWindowSize(*reinterpret_cast<double*>(_v)); break;
        case 12: _t->setHopLength(*reinterpret_cast<double*>(_v)); break;
        case 13: _t->setSpectralOverlap(*reinterpret_cast<double*>(_v)); break;
        case 14: _t->setDialogueFreqMin(*reinterpret_cast<int*>(_v)); break;
        case 15: _t->setDialogueFreqMax(*reinterpret_cast<int*>(_v)); break;
        case 16: _t->setMusicFreqMin(*reinterpret_cast<int*>(_v)); break;
        case 17: _t->setMusicFreqMax(*reinterpret_cast<int*>(_v)); break;
        case 18: _t->setSfxFreqMin(*reinterpret_cast<int*>(_v)); break;
        case 19: _t->setSfxFreqMax(*reinterpret_cast<int*>(_v)); break;
        case 20: _t->setNoiseFloorThreshold(*reinterpret_cast<double*>(_v)); break;
        case 21: _t->setSilenceThreshold(*reinterpret_cast<double*>(_v)); break;
        case 22: _t->setSilenceMinDuration(*reinterpret_cast<double*>(_v)); break;
        case 23: _t->setModelSelection(*reinterpret_cast<QString*>(_v)); break;
        case 24: _t->setEnableSpectralFeatures(*reinterpret_cast<bool*>(_v)); break;
        case 25: _t->setEnableTemporalFeatures(*reinterpret_cast<bool*>(_v)); break;
        case 26: _t->setTemporalSmoothingWindow(*reinterpret_cast<double*>(_v)); break;
        case 27: _t->setContextWindowSize(*reinterpret_cast<double*>(_v)); break;
        case 28: _t->setErrorHandlingStrategy(*reinterpret_cast<QString*>(_v)); break;
        case 29: _t->setLowConfidenceBehavior(*reinterpret_cast<QString*>(_v)); break;
        case 30: _t->setEnableValidationRules(*reinterpret_cast<bool*>(_v)); break;
        case 31: _t->setEnableQualityAssurance(*reinterpret_cast<bool*>(_v)); break;
        case 32: _t->setSpeedAccuracyBalance(*reinterpret_cast<double*>(_v)); break;
        case 33: _t->setEnableMultiPassAnalysis(*reinterpret_cast<bool*>(_v)); break;
        case 34: _t->setEnableCrossValidation(*reinterpret_cast<bool*>(_v)); break;
        case 35: _t->setBatchChunkSize(*reinterpret_cast<int*>(_v)); break;
        case 36: _t->setBatchOverlapRatio(*reinterpret_cast<double*>(_v)); break;
        default: break;
        }
    }
}

const QMetaObject *AnalysisSettings::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *AnalysisSettings::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_staticMetaObjectStaticContent<qt_meta_tag_ZN16AnalysisSettingsE_t>.strings))
        return static_cast<void*>(this);
    return QObject::qt_metacast(_clname);
}

int AnalysisSettings::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QObject::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 90)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 90;
    }
    if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 90)
            *reinterpret_cast<QMetaType *>(_a[0]) = QMetaType();
        _id -= 90;
    }
    if (_c == QMetaObject::ReadProperty || _c == QMetaObject::WriteProperty
            || _c == QMetaObject::ResetProperty || _c == QMetaObject::BindableProperty
            || _c == QMetaObject::RegisterPropertyMetaType) {
        qt_static_metacall(this, _c, _id, _a);
        _id -= 37;
    }
    return _id;
}

// SIGNAL 0
void AnalysisSettings::chunkSkipFactorChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 0, nullptr);
}

// SIGNAL 1
void AnalysisSettings::maxConcurrentAnalysisChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 1, nullptr);
}

// SIGNAL 2
void AnalysisSettings::enableFastModeChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 2, nullptr);
}

// SIGNAL 3
void AnalysisSettings::analysisQualityChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 3, nullptr);
}

// SIGNAL 4
void AnalysisSettings::confidenceThresholdChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 4, nullptr);
}

// SIGNAL 5
void AnalysisSettings::enableSpeakerDiarizationChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 5, nullptr);
}

// SIGNAL 6
void AnalysisSettings::enableMusicDetectionChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 6, nullptr);
}

// SIGNAL 7
void AnalysisSettings::enableSFXDetectionChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 7, nullptr);
}

// SIGNAL 8
void AnalysisSettings::sampleRateChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 8, nullptr);
}

// SIGNAL 9
void AnalysisSettings::bitDepthChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 9, nullptr);
}

// SIGNAL 10
void AnalysisSettings::mfccCoefficientsChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 10, nullptr);
}

// SIGNAL 11
void AnalysisSettings::windowSizeChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 11, nullptr);
}

// SIGNAL 12
void AnalysisSettings::hopLengthChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 12, nullptr);
}

// SIGNAL 13
void AnalysisSettings::spectralOverlapChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 13, nullptr);
}

// SIGNAL 14
void AnalysisSettings::dialogueFreqMinChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 14, nullptr);
}

// SIGNAL 15
void AnalysisSettings::dialogueFreqMaxChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 15, nullptr);
}

// SIGNAL 16
void AnalysisSettings::musicFreqMinChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 16, nullptr);
}

// SIGNAL 17
void AnalysisSettings::musicFreqMaxChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 17, nullptr);
}

// SIGNAL 18
void AnalysisSettings::sfxFreqMinChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 18, nullptr);
}

// SIGNAL 19
void AnalysisSettings::sfxFreqMaxChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 19, nullptr);
}

// SIGNAL 20
void AnalysisSettings::noiseFloorThresholdChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 20, nullptr);
}

// SIGNAL 21
void AnalysisSettings::silenceThresholdChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 21, nullptr);
}

// SIGNAL 22
void AnalysisSettings::silenceMinDurationChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 22, nullptr);
}

// SIGNAL 23
void AnalysisSettings::modelSelectionChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 23, nullptr);
}

// SIGNAL 24
void AnalysisSettings::enableSpectralFeaturesChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 24, nullptr);
}

// SIGNAL 25
void AnalysisSettings::enableTemporalFeaturesChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 25, nullptr);
}

// SIGNAL 26
void AnalysisSettings::temporalSmoothingWindowChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 26, nullptr);
}

// SIGNAL 27
void AnalysisSettings::contextWindowSizeChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 27, nullptr);
}

// SIGNAL 28
void AnalysisSettings::errorHandlingStrategyChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 28, nullptr);
}

// SIGNAL 29
void AnalysisSettings::lowConfidenceBehaviorChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 29, nullptr);
}

// SIGNAL 30
void AnalysisSettings::enableValidationRulesChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 30, nullptr);
}

// SIGNAL 31
void AnalysisSettings::enableQualityAssuranceChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 31, nullptr);
}

// SIGNAL 32
void AnalysisSettings::speedAccuracyBalanceChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 32, nullptr);
}

// SIGNAL 33
void AnalysisSettings::enableMultiPassAnalysisChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 33, nullptr);
}

// SIGNAL 34
void AnalysisSettings::enableCrossValidationChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 34, nullptr);
}

// SIGNAL 35
void AnalysisSettings::batchChunkSizeChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 35, nullptr);
}

// SIGNAL 36
void AnalysisSettings::batchOverlapRatioChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 36, nullptr);
}

// SIGNAL 37
void AnalysisSettings::settingsChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 37, nullptr);
}

// SIGNAL 38
void AnalysisSettings::presetsChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 38, nullptr);
}
QT_WARNING_POP
