/****************************************************************************
** Meta object code from reading C++ file 'TrackOrganizer.h'
**
** Created by: The Qt Meta Object Compiler version 69 (Qt 6.9.0)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include "../../../src/core/TrackOrganizer.h"
#include <QtCore/qmetatype.h>

#include <QtCore/qtmochelpers.h>

#include <memory>


#include <QtCore/qxptype_traits.h>
#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'TrackOrganizer.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 69
#error "This file was generated using the moc from 6.9.0. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

#ifndef Q_CONSTINIT
#define Q_CONSTINIT
#endif

QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
QT_WARNING_DISABLE_GCC("-Wuseless-cast")
namespace {
struct qt_meta_tag_ZN14TrackOrganizerE_t {};
} // unnamed namespace

template <> constexpr inline auto TrackOrganizer::qt_create_metaobjectdata<qt_meta_tag_ZN14TrackOrganizerE_t>()
{
    namespace QMC = QtMocConstants;
    QtMocHelpers::StringRefStorage qt_stringData {
        "TrackOrganizer",
        "availableTemplatesChanged",
        "",
        "currentTemplateChanged",
        "preserveOriginalTimingChanged",
        "organizationCompleted",
        "QVariantList",
        "assignments",
        "QVariantMap",
        "stats",
        "conflictsDetected",
        "conflicts",
        "organizeRegions",
        "regions",
        "originalTracks",
        "createCustomTemplate",
        "templateName",
        "templateConfig",
        "deleteTemplate",
        "optimizeOrganization",
        "updateOrganization",
        "updatedRegions",
        "getDetailedAnalytics",
        "getTemplateConfig",
        "previewOrganization",
        "resolveConflicts",
        "getOrganizationStats",
        "availableTemplates",
        "currentTemplate",
        "preserveOriginalTiming"
    };

    QtMocHelpers::UintData qt_methods {
        // Signal 'availableTemplatesChanged'
        QtMocHelpers::SignalData<void()>(1, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'currentTemplateChanged'
        QtMocHelpers::SignalData<void()>(3, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'preserveOriginalTimingChanged'
        QtMocHelpers::SignalData<void()>(4, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'organizationCompleted'
        QtMocHelpers::SignalData<void(const QVariantList &, const QVariantMap &)>(5, 2, QMC::AccessPublic, QMetaType::Void, {{
            { 0x80000000 | 6, 7 }, { 0x80000000 | 8, 9 },
        }}),
        // Signal 'conflictsDetected'
        QtMocHelpers::SignalData<void(const QVariantList &)>(10, 2, QMC::AccessPublic, QMetaType::Void, {{
            { 0x80000000 | 6, 11 },
        }}),
        // Slot 'organizeRegions'
        QtMocHelpers::SlotData<QVariantList(const QVariantList &, const QVariantList &)>(12, 2, QMC::AccessPublic, 0x80000000 | 6, {{
            { 0x80000000 | 6, 13 }, { 0x80000000 | 6, 14 },
        }}),
        // Slot 'createCustomTemplate'
        QtMocHelpers::SlotData<void(const QString &, const QVariantMap &)>(15, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::QString, 16 }, { 0x80000000 | 8, 17 },
        }}),
        // Slot 'deleteTemplate'
        QtMocHelpers::SlotData<void(const QString &)>(18, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::QString, 16 },
        }}),
        // Slot 'optimizeOrganization'
        QtMocHelpers::SlotData<QVariantList(const QVariantList &)>(19, 2, QMC::AccessPublic, 0x80000000 | 6, {{
            { 0x80000000 | 6, 13 },
        }}),
        // Slot 'updateOrganization'
        QtMocHelpers::SlotData<QVariantList(const QVariantList &)>(20, 2, QMC::AccessPublic, 0x80000000 | 6, {{
            { 0x80000000 | 6, 21 },
        }}),
        // Slot 'getDetailedAnalytics'
        QtMocHelpers::SlotData<QVariantMap(const QVariantList &) const>(22, 2, QMC::AccessPublic, 0x80000000 | 8, {{
            { 0x80000000 | 6, 7 },
        }}),
        // Slot 'getTemplateConfig'
        QtMocHelpers::SlotData<QVariantMap(const QString &) const>(23, 2, QMC::AccessPublic, 0x80000000 | 8, {{
            { QMetaType::QString, 16 },
        }}),
        // Slot 'previewOrganization'
        QtMocHelpers::SlotData<QVariantList(const QVariantList &, const QString &) const>(24, 2, QMC::AccessPublic, 0x80000000 | 6, {{
            { 0x80000000 | 6, 13 }, { QMetaType::QString, 16 },
        }}),
        // Slot 'resolveConflicts'
        QtMocHelpers::SlotData<QVariantList(const QVariantList &)>(25, 2, QMC::AccessPublic, 0x80000000 | 6, {{
            { 0x80000000 | 6, 7 },
        }}),
        // Slot 'getOrganizationStats'
        QtMocHelpers::SlotData<QVariantMap(const QVariantList &) const>(26, 2, QMC::AccessPublic, 0x80000000 | 8, {{
            { 0x80000000 | 6, 7 },
        }}),
    };
    QtMocHelpers::UintData qt_properties {
        // property 'availableTemplates'
        QtMocHelpers::PropertyData<QStringList>(27, QMetaType::QStringList, QMC::DefaultPropertyFlags, 0),
        // property 'currentTemplate'
        QtMocHelpers::PropertyData<QString>(28, QMetaType::QString, QMC::DefaultPropertyFlags | QMC::Writable | QMC::StdCppSet, 1),
        // property 'preserveOriginalTiming'
        QtMocHelpers::PropertyData<bool>(29, QMetaType::Bool, QMC::DefaultPropertyFlags | QMC::Writable | QMC::StdCppSet, 2),
    };
    QtMocHelpers::UintData qt_enums {
    };
    return QtMocHelpers::metaObjectData<TrackOrganizer, qt_meta_tag_ZN14TrackOrganizerE_t>(QMC::MetaObjectFlag{}, qt_stringData,
            qt_methods, qt_properties, qt_enums);
}
Q_CONSTINIT const QMetaObject TrackOrganizer::staticMetaObject = { {
    QMetaObject::SuperData::link<QObject::staticMetaObject>(),
    qt_staticMetaObjectStaticContent<qt_meta_tag_ZN14TrackOrganizerE_t>.stringdata,
    qt_staticMetaObjectStaticContent<qt_meta_tag_ZN14TrackOrganizerE_t>.data,
    qt_static_metacall,
    nullptr,
    qt_staticMetaObjectRelocatingContent<qt_meta_tag_ZN14TrackOrganizerE_t>.metaTypes,
    nullptr
} };

void TrackOrganizer::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    auto *_t = static_cast<TrackOrganizer *>(_o);
    if (_c == QMetaObject::InvokeMetaMethod) {
        switch (_id) {
        case 0: _t->availableTemplatesChanged(); break;
        case 1: _t->currentTemplateChanged(); break;
        case 2: _t->preserveOriginalTimingChanged(); break;
        case 3: _t->organizationCompleted((*reinterpret_cast< std::add_pointer_t<QVariantList>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<QVariantMap>>(_a[2]))); break;
        case 4: _t->conflictsDetected((*reinterpret_cast< std::add_pointer_t<QVariantList>>(_a[1]))); break;
        case 5: { QVariantList _r = _t->organizeRegions((*reinterpret_cast< std::add_pointer_t<QVariantList>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<QVariantList>>(_a[2])));
            if (_a[0]) *reinterpret_cast< QVariantList*>(_a[0]) = std::move(_r); }  break;
        case 6: _t->createCustomTemplate((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<QVariantMap>>(_a[2]))); break;
        case 7: _t->deleteTemplate((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1]))); break;
        case 8: { QVariantList _r = _t->optimizeOrganization((*reinterpret_cast< std::add_pointer_t<QVariantList>>(_a[1])));
            if (_a[0]) *reinterpret_cast< QVariantList*>(_a[0]) = std::move(_r); }  break;
        case 9: { QVariantList _r = _t->updateOrganization((*reinterpret_cast< std::add_pointer_t<QVariantList>>(_a[1])));
            if (_a[0]) *reinterpret_cast< QVariantList*>(_a[0]) = std::move(_r); }  break;
        case 10: { QVariantMap _r = _t->getDetailedAnalytics((*reinterpret_cast< std::add_pointer_t<QVariantList>>(_a[1])));
            if (_a[0]) *reinterpret_cast< QVariantMap*>(_a[0]) = std::move(_r); }  break;
        case 11: { QVariantMap _r = _t->getTemplateConfig((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1])));
            if (_a[0]) *reinterpret_cast< QVariantMap*>(_a[0]) = std::move(_r); }  break;
        case 12: { QVariantList _r = _t->previewOrganization((*reinterpret_cast< std::add_pointer_t<QVariantList>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<QString>>(_a[2])));
            if (_a[0]) *reinterpret_cast< QVariantList*>(_a[0]) = std::move(_r); }  break;
        case 13: { QVariantList _r = _t->resolveConflicts((*reinterpret_cast< std::add_pointer_t<QVariantList>>(_a[1])));
            if (_a[0]) *reinterpret_cast< QVariantList*>(_a[0]) = std::move(_r); }  break;
        case 14: { QVariantMap _r = _t->getOrganizationStats((*reinterpret_cast< std::add_pointer_t<QVariantList>>(_a[1])));
            if (_a[0]) *reinterpret_cast< QVariantMap*>(_a[0]) = std::move(_r); }  break;
        default: ;
        }
    }
    if (_c == QMetaObject::IndexOfMethod) {
        if (QtMocHelpers::indexOfMethod<void (TrackOrganizer::*)()>(_a, &TrackOrganizer::availableTemplatesChanged, 0))
            return;
        if (QtMocHelpers::indexOfMethod<void (TrackOrganizer::*)()>(_a, &TrackOrganizer::currentTemplateChanged, 1))
            return;
        if (QtMocHelpers::indexOfMethod<void (TrackOrganizer::*)()>(_a, &TrackOrganizer::preserveOriginalTimingChanged, 2))
            return;
        if (QtMocHelpers::indexOfMethod<void (TrackOrganizer::*)(const QVariantList & , const QVariantMap & )>(_a, &TrackOrganizer::organizationCompleted, 3))
            return;
        if (QtMocHelpers::indexOfMethod<void (TrackOrganizer::*)(const QVariantList & )>(_a, &TrackOrganizer::conflictsDetected, 4))
            return;
    }
    if (_c == QMetaObject::ReadProperty) {
        void *_v = _a[0];
        switch (_id) {
        case 0: *reinterpret_cast<QStringList*>(_v) = _t->availableTemplates(); break;
        case 1: *reinterpret_cast<QString*>(_v) = _t->currentTemplate(); break;
        case 2: *reinterpret_cast<bool*>(_v) = _t->preserveOriginalTiming(); break;
        default: break;
        }
    }
    if (_c == QMetaObject::WriteProperty) {
        void *_v = _a[0];
        switch (_id) {
        case 1: _t->setCurrentTemplate(*reinterpret_cast<QString*>(_v)); break;
        case 2: _t->setPreserveOriginalTiming(*reinterpret_cast<bool*>(_v)); break;
        default: break;
        }
    }
}

const QMetaObject *TrackOrganizer::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *TrackOrganizer::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_staticMetaObjectStaticContent<qt_meta_tag_ZN14TrackOrganizerE_t>.strings))
        return static_cast<void*>(this);
    return QObject::qt_metacast(_clname);
}

int TrackOrganizer::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QObject::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 15)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 15;
    }
    if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 15)
            *reinterpret_cast<QMetaType *>(_a[0]) = QMetaType();
        _id -= 15;
    }
    if (_c == QMetaObject::ReadProperty || _c == QMetaObject::WriteProperty
            || _c == QMetaObject::ResetProperty || _c == QMetaObject::BindableProperty
            || _c == QMetaObject::RegisterPropertyMetaType) {
        qt_static_metacall(this, _c, _id, _a);
        _id -= 3;
    }
    return _id;
}

// SIGNAL 0
void TrackOrganizer::availableTemplatesChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 0, nullptr);
}

// SIGNAL 1
void TrackOrganizer::currentTemplateChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 1, nullptr);
}

// SIGNAL 2
void TrackOrganizer::preserveOriginalTimingChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 2, nullptr);
}

// SIGNAL 3
void TrackOrganizer::organizationCompleted(const QVariantList & _t1, const QVariantMap & _t2)
{
    QMetaObject::activate<void>(this, &staticMetaObject, 3, nullptr, _t1, _t2);
}

// SIGNAL 4
void TrackOrganizer::conflictsDetected(const QVariantList & _t1)
{
    QMetaObject::activate<void>(this, &staticMetaObject, 4, nullptr, _t1);
}
QT_WARNING_POP
