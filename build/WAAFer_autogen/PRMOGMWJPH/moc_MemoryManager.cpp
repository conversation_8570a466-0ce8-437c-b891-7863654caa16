/****************************************************************************
** Meta object code from reading C++ file 'MemoryManager.h'
**
** Created by: The Qt Meta Object Compiler version 69 (Qt 6.9.0)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include "../../../src/core/MemoryManager.h"
#include <QtCore/qmetatype.h>

#include <QtCore/qtmochelpers.h>

#include <memory>


#include <QtCore/qxptype_traits.h>
#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'MemoryManager.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 69
#error "This file was generated using the moc from 6.9.0. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

#ifndef Q_CONSTINIT
#define Q_CONSTINIT
#endif

QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
QT_WARNING_DISABLE_GCC("-Wuseless-cast")
namespace {
struct qt_meta_tag_ZN13MemoryManagerE_t {};
} // unnamed namespace

template <> constexpr inline auto MemoryManager::qt_create_metaobjectdata<qt_meta_tag_ZN13MemoryManagerE_t>()
{
    namespace QMC = QtMocConstants;
    QtMocHelpers::StringRefStorage qt_stringData {
        "MemoryManager",
        "memoryUsageChanged",
        "",
        "maxMemoryLimitChanged",
        "cacheHitRateChanged",
        "isOptimizedChanged",
        "memoryWarning",
        "usage",
        "message",
        "memoryCritical",
        "chunkEvicted",
        "chunkId",
        "initialize",
        "maxMemoryGB",
        "chunkSizeMB",
        "allocateChunk",
        "sizeBytes",
        "deallocateChunk",
        "isChunkLoaded",
        "getChunkData",
        "QVariant",
        "setChunkData",
        "data",
        "forceGarbageCollection",
        "optimizeMemoryUsage",
        "clearCache",
        "getSystemMemoryInfo",
        "QVariantMap",
        "monitorMemoryUsage",
        "totalMemoryUsage",
        "maxMemoryLimit",
        "cacheHitRate",
        "isOptimized"
    };

    QtMocHelpers::UintData qt_methods {
        // Signal 'memoryUsageChanged'
        QtMocHelpers::SignalData<void()>(1, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'maxMemoryLimitChanged'
        QtMocHelpers::SignalData<void()>(3, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'cacheHitRateChanged'
        QtMocHelpers::SignalData<void()>(4, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'isOptimizedChanged'
        QtMocHelpers::SignalData<void()>(5, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'memoryWarning'
        QtMocHelpers::SignalData<void(int, const QString &)>(6, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::Int, 7 }, { QMetaType::QString, 8 },
        }}),
        // Signal 'memoryCritical'
        QtMocHelpers::SignalData<void(int)>(9, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::Int, 7 },
        }}),
        // Signal 'chunkEvicted'
        QtMocHelpers::SignalData<void(const QString &)>(10, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::QString, 11 },
        }}),
        // Slot 'initialize'
        QtMocHelpers::SlotData<bool(int, int)>(12, 2, QMC::AccessPublic, QMetaType::Bool, {{
            { QMetaType::Int, 13 }, { QMetaType::Int, 14 },
        }}),
        // Slot 'initialize'
        QtMocHelpers::SlotData<bool(int)>(12, 2, QMC::AccessPublic | QMC::MethodCloned, QMetaType::Bool, {{
            { QMetaType::Int, 13 },
        }}),
        // Slot 'initialize'
        QtMocHelpers::SlotData<bool()>(12, 2, QMC::AccessPublic | QMC::MethodCloned, QMetaType::Bool),
        // Slot 'allocateChunk'
        QtMocHelpers::SlotData<bool(const QString &, qint64)>(15, 2, QMC::AccessPublic, QMetaType::Bool, {{
            { QMetaType::QString, 11 }, { QMetaType::LongLong, 16 },
        }}),
        // Slot 'deallocateChunk'
        QtMocHelpers::SlotData<bool(const QString &)>(17, 2, QMC::AccessPublic, QMetaType::Bool, {{
            { QMetaType::QString, 11 },
        }}),
        // Slot 'isChunkLoaded'
        QtMocHelpers::SlotData<bool(const QString &)>(18, 2, QMC::AccessPublic, QMetaType::Bool, {{
            { QMetaType::QString, 11 },
        }}),
        // Slot 'getChunkData'
        QtMocHelpers::SlotData<QVariant(const QString &)>(19, 2, QMC::AccessPublic, 0x80000000 | 20, {{
            { QMetaType::QString, 11 },
        }}),
        // Slot 'setChunkData'
        QtMocHelpers::SlotData<bool(const QString &, const QVariant &)>(21, 2, QMC::AccessPublic, QMetaType::Bool, {{
            { QMetaType::QString, 11 }, { 0x80000000 | 20, 22 },
        }}),
        // Slot 'forceGarbageCollection'
        QtMocHelpers::SlotData<qint64()>(23, 2, QMC::AccessPublic, QMetaType::LongLong),
        // Slot 'optimizeMemoryUsage'
        QtMocHelpers::SlotData<bool()>(24, 2, QMC::AccessPublic, QMetaType::Bool),
        // Slot 'clearCache'
        QtMocHelpers::SlotData<void()>(25, 2, QMC::AccessPublic, QMetaType::Void),
        // Slot 'getSystemMemoryInfo'
        QtMocHelpers::SlotData<QVariantMap()>(26, 2, QMC::AccessPublic, 0x80000000 | 27),
        // Slot 'monitorMemoryUsage'
        QtMocHelpers::SlotData<void()>(28, 2, QMC::AccessPrivate, QMetaType::Void),
    };
    QtMocHelpers::UintData qt_properties {
        // property 'totalMemoryUsage'
        QtMocHelpers::PropertyData<qint64>(29, QMetaType::LongLong, QMC::DefaultPropertyFlags, 0),
        // property 'maxMemoryLimit'
        QtMocHelpers::PropertyData<qint64>(30, QMetaType::LongLong, QMC::DefaultPropertyFlags | QMC::Writable | QMC::StdCppSet, 1),
        // property 'cacheHitRate'
        QtMocHelpers::PropertyData<int>(31, QMetaType::Int, QMC::DefaultPropertyFlags, 2),
        // property 'isOptimized'
        QtMocHelpers::PropertyData<bool>(32, QMetaType::Bool, QMC::DefaultPropertyFlags, 3),
    };
    QtMocHelpers::UintData qt_enums {
    };
    return QtMocHelpers::metaObjectData<MemoryManager, qt_meta_tag_ZN13MemoryManagerE_t>(QMC::MetaObjectFlag{}, qt_stringData,
            qt_methods, qt_properties, qt_enums);
}
Q_CONSTINIT const QMetaObject MemoryManager::staticMetaObject = { {
    QMetaObject::SuperData::link<QObject::staticMetaObject>(),
    qt_staticMetaObjectStaticContent<qt_meta_tag_ZN13MemoryManagerE_t>.stringdata,
    qt_staticMetaObjectStaticContent<qt_meta_tag_ZN13MemoryManagerE_t>.data,
    qt_static_metacall,
    nullptr,
    qt_staticMetaObjectRelocatingContent<qt_meta_tag_ZN13MemoryManagerE_t>.metaTypes,
    nullptr
} };

void MemoryManager::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    auto *_t = static_cast<MemoryManager *>(_o);
    if (_c == QMetaObject::InvokeMetaMethod) {
        switch (_id) {
        case 0: _t->memoryUsageChanged(); break;
        case 1: _t->maxMemoryLimitChanged(); break;
        case 2: _t->cacheHitRateChanged(); break;
        case 3: _t->isOptimizedChanged(); break;
        case 4: _t->memoryWarning((*reinterpret_cast< std::add_pointer_t<int>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<QString>>(_a[2]))); break;
        case 5: _t->memoryCritical((*reinterpret_cast< std::add_pointer_t<int>>(_a[1]))); break;
        case 6: _t->chunkEvicted((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1]))); break;
        case 7: { bool _r = _t->initialize((*reinterpret_cast< std::add_pointer_t<int>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<int>>(_a[2])));
            if (_a[0]) *reinterpret_cast< bool*>(_a[0]) = std::move(_r); }  break;
        case 8: { bool _r = _t->initialize((*reinterpret_cast< std::add_pointer_t<int>>(_a[1])));
            if (_a[0]) *reinterpret_cast< bool*>(_a[0]) = std::move(_r); }  break;
        case 9: { bool _r = _t->initialize();
            if (_a[0]) *reinterpret_cast< bool*>(_a[0]) = std::move(_r); }  break;
        case 10: { bool _r = _t->allocateChunk((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<qint64>>(_a[2])));
            if (_a[0]) *reinterpret_cast< bool*>(_a[0]) = std::move(_r); }  break;
        case 11: { bool _r = _t->deallocateChunk((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1])));
            if (_a[0]) *reinterpret_cast< bool*>(_a[0]) = std::move(_r); }  break;
        case 12: { bool _r = _t->isChunkLoaded((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1])));
            if (_a[0]) *reinterpret_cast< bool*>(_a[0]) = std::move(_r); }  break;
        case 13: { QVariant _r = _t->getChunkData((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1])));
            if (_a[0]) *reinterpret_cast< QVariant*>(_a[0]) = std::move(_r); }  break;
        case 14: { bool _r = _t->setChunkData((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<QVariant>>(_a[2])));
            if (_a[0]) *reinterpret_cast< bool*>(_a[0]) = std::move(_r); }  break;
        case 15: { qint64 _r = _t->forceGarbageCollection();
            if (_a[0]) *reinterpret_cast< qint64*>(_a[0]) = std::move(_r); }  break;
        case 16: { bool _r = _t->optimizeMemoryUsage();
            if (_a[0]) *reinterpret_cast< bool*>(_a[0]) = std::move(_r); }  break;
        case 17: _t->clearCache(); break;
        case 18: { QVariantMap _r = _t->getSystemMemoryInfo();
            if (_a[0]) *reinterpret_cast< QVariantMap*>(_a[0]) = std::move(_r); }  break;
        case 19: _t->monitorMemoryUsage(); break;
        default: ;
        }
    }
    if (_c == QMetaObject::IndexOfMethod) {
        if (QtMocHelpers::indexOfMethod<void (MemoryManager::*)()>(_a, &MemoryManager::memoryUsageChanged, 0))
            return;
        if (QtMocHelpers::indexOfMethod<void (MemoryManager::*)()>(_a, &MemoryManager::maxMemoryLimitChanged, 1))
            return;
        if (QtMocHelpers::indexOfMethod<void (MemoryManager::*)()>(_a, &MemoryManager::cacheHitRateChanged, 2))
            return;
        if (QtMocHelpers::indexOfMethod<void (MemoryManager::*)()>(_a, &MemoryManager::isOptimizedChanged, 3))
            return;
        if (QtMocHelpers::indexOfMethod<void (MemoryManager::*)(int , const QString & )>(_a, &MemoryManager::memoryWarning, 4))
            return;
        if (QtMocHelpers::indexOfMethod<void (MemoryManager::*)(int )>(_a, &MemoryManager::memoryCritical, 5))
            return;
        if (QtMocHelpers::indexOfMethod<void (MemoryManager::*)(const QString & )>(_a, &MemoryManager::chunkEvicted, 6))
            return;
    }
    if (_c == QMetaObject::ReadProperty) {
        void *_v = _a[0];
        switch (_id) {
        case 0: *reinterpret_cast<qint64*>(_v) = _t->totalMemoryUsage(); break;
        case 1: *reinterpret_cast<qint64*>(_v) = _t->maxMemoryLimit(); break;
        case 2: *reinterpret_cast<int*>(_v) = _t->cacheHitRate(); break;
        case 3: *reinterpret_cast<bool*>(_v) = _t->isOptimized(); break;
        default: break;
        }
    }
    if (_c == QMetaObject::WriteProperty) {
        void *_v = _a[0];
        switch (_id) {
        case 1: _t->setMaxMemoryLimit(*reinterpret_cast<qint64*>(_v)); break;
        default: break;
        }
    }
}

const QMetaObject *MemoryManager::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *MemoryManager::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_staticMetaObjectStaticContent<qt_meta_tag_ZN13MemoryManagerE_t>.strings))
        return static_cast<void*>(this);
    return QObject::qt_metacast(_clname);
}

int MemoryManager::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QObject::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 20)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 20;
    }
    if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 20)
            *reinterpret_cast<QMetaType *>(_a[0]) = QMetaType();
        _id -= 20;
    }
    if (_c == QMetaObject::ReadProperty || _c == QMetaObject::WriteProperty
            || _c == QMetaObject::ResetProperty || _c == QMetaObject::BindableProperty
            || _c == QMetaObject::RegisterPropertyMetaType) {
        qt_static_metacall(this, _c, _id, _a);
        _id -= 4;
    }
    return _id;
}

// SIGNAL 0
void MemoryManager::memoryUsageChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 0, nullptr);
}

// SIGNAL 1
void MemoryManager::maxMemoryLimitChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 1, nullptr);
}

// SIGNAL 2
void MemoryManager::cacheHitRateChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 2, nullptr);
}

// SIGNAL 3
void MemoryManager::isOptimizedChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 3, nullptr);
}

// SIGNAL 4
void MemoryManager::memoryWarning(int _t1, const QString & _t2)
{
    QMetaObject::activate<void>(this, &staticMetaObject, 4, nullptr, _t1, _t2);
}

// SIGNAL 5
void MemoryManager::memoryCritical(int _t1)
{
    QMetaObject::activate<void>(this, &staticMetaObject, 5, nullptr, _t1);
}

// SIGNAL 6
void MemoryManager::chunkEvicted(const QString & _t1)
{
    QMetaObject::activate<void>(this, &staticMetaObject, 6, nullptr, _t1);
}
QT_WARNING_POP
