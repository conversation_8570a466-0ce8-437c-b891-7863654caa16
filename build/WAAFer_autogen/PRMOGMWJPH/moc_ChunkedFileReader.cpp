/****************************************************************************
** Meta object code from reading C++ file 'ChunkedFileReader.h'
**
** Created by: The Qt Meta Object Compiler version 69 (Qt 6.9.0)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include "../../../src/core/ChunkedFileReader.h"
#include <QtCore/qmetatype.h>

#include <QtCore/qtmochelpers.h>

#include <memory>


#include <QtCore/qxptype_traits.h>
#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'ChunkedFileReader.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 69
#error "This file was generated using the moc from 6.9.0. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

#ifndef Q_CONSTINIT
#define Q_CONSTINIT
#endif

QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
QT_WARNING_DISABLE_GCC("-Wuseless-cast")
namespace {
struct qt_meta_tag_ZN17ChunkedFileReaderE_t {};
} // unnamed namespace

template <> constexpr inline auto ChunkedFileReader::qt_create_metaobjectdata<qt_meta_tag_ZN17ChunkedFileReaderE_t>()
{
    namespace QMC = QtMocConstants;
    QtMocHelpers::StringRefStorage qt_stringData {
        "ChunkedFileReader",
        "fileNameChanged",
        "",
        "fileSizeChanged",
        "chunkSizeChanged",
        "isOpenChanged",
        "currentPositionChanged",
        "chunkRead",
        "chunkIndex",
        "size",
        "readingProgress",
        "progress",
        "error",
        "openFile",
        "filePath",
        "closeFile",
        "readNextChunk",
        "readChunk",
        "readAt",
        "position",
        "seek",
        "seekToChunk",
        "atEnd",
        "getFileInfo",
        "QVariantMap",
        "getReadingStats",
        "resetStats",
        "streamAudioChunk",
        "startOffset",
        "chunkSize",
        "getOptimalAudioChunkSize",
        "sampleRate",
        "channels",
        "durationSeconds",
        "isAudioFile",
        "fileName",
        "fileSize",
        "isOpen",
        "currentPosition"
    };

    QtMocHelpers::UintData qt_methods {
        // Signal 'fileNameChanged'
        QtMocHelpers::SignalData<void()>(1, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'fileSizeChanged'
        QtMocHelpers::SignalData<void()>(3, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'chunkSizeChanged'
        QtMocHelpers::SignalData<void()>(4, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'isOpenChanged'
        QtMocHelpers::SignalData<void()>(5, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'currentPositionChanged'
        QtMocHelpers::SignalData<void()>(6, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'chunkRead'
        QtMocHelpers::SignalData<void(qint64, qint64)>(7, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::LongLong, 8 }, { QMetaType::LongLong, 9 },
        }}),
        // Signal 'readingProgress'
        QtMocHelpers::SignalData<void(int)>(10, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::Int, 11 },
        }}),
        // Signal 'error'
        QtMocHelpers::SignalData<void(const QString &)>(12, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::QString, 12 },
        }}),
        // Slot 'openFile'
        QtMocHelpers::SlotData<bool(const QString &)>(13, 2, QMC::AccessPublic, QMetaType::Bool, {{
            { QMetaType::QString, 14 },
        }}),
        // Slot 'closeFile'
        QtMocHelpers::SlotData<void()>(15, 2, QMC::AccessPublic, QMetaType::Void),
        // Slot 'readNextChunk'
        QtMocHelpers::SlotData<QByteArray()>(16, 2, QMC::AccessPublic, QMetaType::QByteArray),
        // Slot 'readChunk'
        QtMocHelpers::SlotData<QByteArray(qint64)>(17, 2, QMC::AccessPublic, QMetaType::QByteArray, {{
            { QMetaType::LongLong, 8 },
        }}),
        // Slot 'readAt'
        QtMocHelpers::SlotData<QByteArray(qint64, qint64)>(18, 2, QMC::AccessPublic, QMetaType::QByteArray, {{
            { QMetaType::LongLong, 19 }, { QMetaType::LongLong, 9 },
        }}),
        // Slot 'readAt'
        QtMocHelpers::SlotData<QByteArray(qint64)>(18, 2, QMC::AccessPublic | QMC::MethodCloned, QMetaType::QByteArray, {{
            { QMetaType::LongLong, 19 },
        }}),
        // Slot 'seek'
        QtMocHelpers::SlotData<bool(qint64)>(20, 2, QMC::AccessPublic, QMetaType::Bool, {{
            { QMetaType::LongLong, 19 },
        }}),
        // Slot 'seekToChunk'
        QtMocHelpers::SlotData<bool(qint64)>(21, 2, QMC::AccessPublic, QMetaType::Bool, {{
            { QMetaType::LongLong, 8 },
        }}),
        // Slot 'atEnd'
        QtMocHelpers::SlotData<bool() const>(22, 2, QMC::AccessPublic, QMetaType::Bool),
        // Slot 'getFileInfo'
        QtMocHelpers::SlotData<QVariantMap() const>(23, 2, QMC::AccessPublic, 0x80000000 | 24),
        // Slot 'getReadingStats'
        QtMocHelpers::SlotData<QVariantMap() const>(25, 2, QMC::AccessPublic, 0x80000000 | 24),
        // Slot 'resetStats'
        QtMocHelpers::SlotData<void()>(26, 2, QMC::AccessPublic, QMetaType::Void),
        // Slot 'streamAudioChunk'
        QtMocHelpers::SlotData<QByteArray(qint64, qint64)>(27, 2, QMC::AccessPublic, QMetaType::QByteArray, {{
            { QMetaType::LongLong, 28 }, { QMetaType::LongLong, 29 },
        }}),
        // Slot 'getOptimalAudioChunkSize'
        QtMocHelpers::SlotData<qint64(int, int, double)>(30, 2, QMC::AccessPublic, QMetaType::LongLong, {{
            { QMetaType::Int, 31 }, { QMetaType::Int, 32 }, { QMetaType::Double, 33 },
        }}),
        // Slot 'getOptimalAudioChunkSize'
        QtMocHelpers::SlotData<qint64(int, int)>(30, 2, QMC::AccessPublic | QMC::MethodCloned, QMetaType::LongLong, {{
            { QMetaType::Int, 31 }, { QMetaType::Int, 32 },
        }}),
        // Slot 'getOptimalAudioChunkSize'
        QtMocHelpers::SlotData<qint64(int)>(30, 2, QMC::AccessPublic | QMC::MethodCloned, QMetaType::LongLong, {{
            { QMetaType::Int, 31 },
        }}),
        // Slot 'getOptimalAudioChunkSize'
        QtMocHelpers::SlotData<qint64()>(30, 2, QMC::AccessPublic | QMC::MethodCloned, QMetaType::LongLong),
        // Slot 'isAudioFile'
        QtMocHelpers::SlotData<bool() const>(34, 2, QMC::AccessPublic, QMetaType::Bool),
    };
    QtMocHelpers::UintData qt_properties {
        // property 'fileName'
        QtMocHelpers::PropertyData<QString>(35, QMetaType::QString, QMC::DefaultPropertyFlags, 0),
        // property 'fileSize'
        QtMocHelpers::PropertyData<qint64>(36, QMetaType::LongLong, QMC::DefaultPropertyFlags, 1),
        // property 'chunkSize'
        QtMocHelpers::PropertyData<qint64>(29, QMetaType::LongLong, QMC::DefaultPropertyFlags | QMC::Writable | QMC::StdCppSet, 2),
        // property 'isOpen'
        QtMocHelpers::PropertyData<bool>(37, QMetaType::Bool, QMC::DefaultPropertyFlags, 3),
        // property 'currentPosition'
        QtMocHelpers::PropertyData<qint64>(38, QMetaType::LongLong, QMC::DefaultPropertyFlags, 4),
    };
    QtMocHelpers::UintData qt_enums {
    };
    return QtMocHelpers::metaObjectData<ChunkedFileReader, qt_meta_tag_ZN17ChunkedFileReaderE_t>(QMC::MetaObjectFlag{}, qt_stringData,
            qt_methods, qt_properties, qt_enums);
}
Q_CONSTINIT const QMetaObject ChunkedFileReader::staticMetaObject = { {
    QMetaObject::SuperData::link<QObject::staticMetaObject>(),
    qt_staticMetaObjectStaticContent<qt_meta_tag_ZN17ChunkedFileReaderE_t>.stringdata,
    qt_staticMetaObjectStaticContent<qt_meta_tag_ZN17ChunkedFileReaderE_t>.data,
    qt_static_metacall,
    nullptr,
    qt_staticMetaObjectRelocatingContent<qt_meta_tag_ZN17ChunkedFileReaderE_t>.metaTypes,
    nullptr
} };

void ChunkedFileReader::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    auto *_t = static_cast<ChunkedFileReader *>(_o);
    if (_c == QMetaObject::InvokeMetaMethod) {
        switch (_id) {
        case 0: _t->fileNameChanged(); break;
        case 1: _t->fileSizeChanged(); break;
        case 2: _t->chunkSizeChanged(); break;
        case 3: _t->isOpenChanged(); break;
        case 4: _t->currentPositionChanged(); break;
        case 5: _t->chunkRead((*reinterpret_cast< std::add_pointer_t<qint64>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<qint64>>(_a[2]))); break;
        case 6: _t->readingProgress((*reinterpret_cast< std::add_pointer_t<int>>(_a[1]))); break;
        case 7: _t->error((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1]))); break;
        case 8: { bool _r = _t->openFile((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1])));
            if (_a[0]) *reinterpret_cast< bool*>(_a[0]) = std::move(_r); }  break;
        case 9: _t->closeFile(); break;
        case 10: { QByteArray _r = _t->readNextChunk();
            if (_a[0]) *reinterpret_cast< QByteArray*>(_a[0]) = std::move(_r); }  break;
        case 11: { QByteArray _r = _t->readChunk((*reinterpret_cast< std::add_pointer_t<qint64>>(_a[1])));
            if (_a[0]) *reinterpret_cast< QByteArray*>(_a[0]) = std::move(_r); }  break;
        case 12: { QByteArray _r = _t->readAt((*reinterpret_cast< std::add_pointer_t<qint64>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<qint64>>(_a[2])));
            if (_a[0]) *reinterpret_cast< QByteArray*>(_a[0]) = std::move(_r); }  break;
        case 13: { QByteArray _r = _t->readAt((*reinterpret_cast< std::add_pointer_t<qint64>>(_a[1])));
            if (_a[0]) *reinterpret_cast< QByteArray*>(_a[0]) = std::move(_r); }  break;
        case 14: { bool _r = _t->seek((*reinterpret_cast< std::add_pointer_t<qint64>>(_a[1])));
            if (_a[0]) *reinterpret_cast< bool*>(_a[0]) = std::move(_r); }  break;
        case 15: { bool _r = _t->seekToChunk((*reinterpret_cast< std::add_pointer_t<qint64>>(_a[1])));
            if (_a[0]) *reinterpret_cast< bool*>(_a[0]) = std::move(_r); }  break;
        case 16: { bool _r = _t->atEnd();
            if (_a[0]) *reinterpret_cast< bool*>(_a[0]) = std::move(_r); }  break;
        case 17: { QVariantMap _r = _t->getFileInfo();
            if (_a[0]) *reinterpret_cast< QVariantMap*>(_a[0]) = std::move(_r); }  break;
        case 18: { QVariantMap _r = _t->getReadingStats();
            if (_a[0]) *reinterpret_cast< QVariantMap*>(_a[0]) = std::move(_r); }  break;
        case 19: _t->resetStats(); break;
        case 20: { QByteArray _r = _t->streamAudioChunk((*reinterpret_cast< std::add_pointer_t<qint64>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<qint64>>(_a[2])));
            if (_a[0]) *reinterpret_cast< QByteArray*>(_a[0]) = std::move(_r); }  break;
        case 21: { qint64 _r = _t->getOptimalAudioChunkSize((*reinterpret_cast< std::add_pointer_t<int>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<int>>(_a[2])),(*reinterpret_cast< std::add_pointer_t<double>>(_a[3])));
            if (_a[0]) *reinterpret_cast< qint64*>(_a[0]) = std::move(_r); }  break;
        case 22: { qint64 _r = _t->getOptimalAudioChunkSize((*reinterpret_cast< std::add_pointer_t<int>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<int>>(_a[2])));
            if (_a[0]) *reinterpret_cast< qint64*>(_a[0]) = std::move(_r); }  break;
        case 23: { qint64 _r = _t->getOptimalAudioChunkSize((*reinterpret_cast< std::add_pointer_t<int>>(_a[1])));
            if (_a[0]) *reinterpret_cast< qint64*>(_a[0]) = std::move(_r); }  break;
        case 24: { qint64 _r = _t->getOptimalAudioChunkSize();
            if (_a[0]) *reinterpret_cast< qint64*>(_a[0]) = std::move(_r); }  break;
        case 25: { bool _r = _t->isAudioFile();
            if (_a[0]) *reinterpret_cast< bool*>(_a[0]) = std::move(_r); }  break;
        default: ;
        }
    }
    if (_c == QMetaObject::IndexOfMethod) {
        if (QtMocHelpers::indexOfMethod<void (ChunkedFileReader::*)()>(_a, &ChunkedFileReader::fileNameChanged, 0))
            return;
        if (QtMocHelpers::indexOfMethod<void (ChunkedFileReader::*)()>(_a, &ChunkedFileReader::fileSizeChanged, 1))
            return;
        if (QtMocHelpers::indexOfMethod<void (ChunkedFileReader::*)()>(_a, &ChunkedFileReader::chunkSizeChanged, 2))
            return;
        if (QtMocHelpers::indexOfMethod<void (ChunkedFileReader::*)()>(_a, &ChunkedFileReader::isOpenChanged, 3))
            return;
        if (QtMocHelpers::indexOfMethod<void (ChunkedFileReader::*)()>(_a, &ChunkedFileReader::currentPositionChanged, 4))
            return;
        if (QtMocHelpers::indexOfMethod<void (ChunkedFileReader::*)(qint64 , qint64 )>(_a, &ChunkedFileReader::chunkRead, 5))
            return;
        if (QtMocHelpers::indexOfMethod<void (ChunkedFileReader::*)(int )>(_a, &ChunkedFileReader::readingProgress, 6))
            return;
        if (QtMocHelpers::indexOfMethod<void (ChunkedFileReader::*)(const QString & )>(_a, &ChunkedFileReader::error, 7))
            return;
    }
    if (_c == QMetaObject::ReadProperty) {
        void *_v = _a[0];
        switch (_id) {
        case 0: *reinterpret_cast<QString*>(_v) = _t->fileName(); break;
        case 1: *reinterpret_cast<qint64*>(_v) = _t->fileSize(); break;
        case 2: *reinterpret_cast<qint64*>(_v) = _t->chunkSize(); break;
        case 3: *reinterpret_cast<bool*>(_v) = _t->isOpen(); break;
        case 4: *reinterpret_cast<qint64*>(_v) = _t->currentPosition(); break;
        default: break;
        }
    }
    if (_c == QMetaObject::WriteProperty) {
        void *_v = _a[0];
        switch (_id) {
        case 2: _t->setChunkSize(*reinterpret_cast<qint64*>(_v)); break;
        default: break;
        }
    }
}

const QMetaObject *ChunkedFileReader::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *ChunkedFileReader::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_staticMetaObjectStaticContent<qt_meta_tag_ZN17ChunkedFileReaderE_t>.strings))
        return static_cast<void*>(this);
    return QObject::qt_metacast(_clname);
}

int ChunkedFileReader::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QObject::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 26)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 26;
    }
    if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 26)
            *reinterpret_cast<QMetaType *>(_a[0]) = QMetaType();
        _id -= 26;
    }
    if (_c == QMetaObject::ReadProperty || _c == QMetaObject::WriteProperty
            || _c == QMetaObject::ResetProperty || _c == QMetaObject::BindableProperty
            || _c == QMetaObject::RegisterPropertyMetaType) {
        qt_static_metacall(this, _c, _id, _a);
        _id -= 5;
    }
    return _id;
}

// SIGNAL 0
void ChunkedFileReader::fileNameChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 0, nullptr);
}

// SIGNAL 1
void ChunkedFileReader::fileSizeChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 1, nullptr);
}

// SIGNAL 2
void ChunkedFileReader::chunkSizeChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 2, nullptr);
}

// SIGNAL 3
void ChunkedFileReader::isOpenChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 3, nullptr);
}

// SIGNAL 4
void ChunkedFileReader::currentPositionChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 4, nullptr);
}

// SIGNAL 5
void ChunkedFileReader::chunkRead(qint64 _t1, qint64 _t2)
{
    QMetaObject::activate<void>(this, &staticMetaObject, 5, nullptr, _t1, _t2);
}

// SIGNAL 6
void ChunkedFileReader::readingProgress(int _t1)
{
    QMetaObject::activate<void>(this, &staticMetaObject, 6, nullptr, _t1);
}

// SIGNAL 7
void ChunkedFileReader::error(const QString & _t1)
{
    QMetaObject::activate<void>(this, &staticMetaObject, 7, nullptr, _t1);
}
QT_WARNING_POP
