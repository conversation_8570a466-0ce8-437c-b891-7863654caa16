/****************************************************************************
** Meta object code from reading C++ file 'AudioFileManager.h'
**
** Created by: The Qt Meta Object Compiler version 69 (Qt 6.9.0)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include "../../../src/audio/AudioFileManager.h"
#include <QtCore/qmetatype.h>

#include <QtCore/qtmochelpers.h>

#include <memory>


#include <QtCore/qxptype_traits.h>
#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'AudioFileManager.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 69
#error "This file was generated using the moc from 6.9.0. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

#ifndef Q_CONSTINIT
#define Q_CONSTINIT
#endif

QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
QT_WARNING_DISABLE_GCC("-Wuseless-cast")
namespace {
struct qt_meta_tag_ZN16AudioFileManagerE_t {};
} // unnamed namespace

template <> constexpr inline auto AudioFileManager::qt_create_metaobjectdata<qt_meta_tag_ZN16AudioFileManagerE_t>()
{
    namespace QMC = QtMocConstants;
    QtMocHelpers::StringRefStorage qt_stringData {
        "AudioFileManager",
        "cacheDirectoryChanged",
        "",
        "cacheSizeChanged",
        "cachedFileCountChanged",
        "audioExtractionCompleted",
        "aafFilePath",
        "extractedFiles",
        "audioExtractionError",
        "error",
        "fileCached",
        "originalPath",
        "cachedPath",
        "extractionProgress",
        "progress",
        "message",
        "extractAudioFromAAF",
        "outputDirectory",
        "getAudioFileForRegion",
        "QVariantMap",
        "regionInfo",
        "cacheAudioFile",
        "filePath",
        "isAudioFileCached",
        "getCachedAudioFilePath",
        "clearCache",
        "cleanupOldCachedFiles",
        "maxAgeHours",
        "getSupportedAudioFormats",
        "validateAudioFile",
        "convertToWAV",
        "inputPath",
        "outputPath",
        "resolveAudioFilePath",
        "audioFilePath",
        "updateCacheStats",
        "cacheDirectory",
        "cacheSize",
        "cachedFileCount"
    };

    QtMocHelpers::UintData qt_methods {
        // Signal 'cacheDirectoryChanged'
        QtMocHelpers::SignalData<void()>(1, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'cacheSizeChanged'
        QtMocHelpers::SignalData<void()>(3, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'cachedFileCountChanged'
        QtMocHelpers::SignalData<void()>(4, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'audioExtractionCompleted'
        QtMocHelpers::SignalData<void(const QString &, const QStringList &)>(5, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::QString, 6 }, { QMetaType::QStringList, 7 },
        }}),
        // Signal 'audioExtractionError'
        QtMocHelpers::SignalData<void(const QString &, const QString &)>(8, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::QString, 6 }, { QMetaType::QString, 9 },
        }}),
        // Signal 'fileCached'
        QtMocHelpers::SignalData<void(const QString &, const QString &)>(10, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::QString, 11 }, { QMetaType::QString, 12 },
        }}),
        // Signal 'extractionProgress'
        QtMocHelpers::SignalData<void(int, const QString &)>(13, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::Int, 14 }, { QMetaType::QString, 15 },
        }}),
        // Slot 'extractAudioFromAAF'
        QtMocHelpers::SlotData<void(const QString &, const QString &)>(16, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::QString, 6 }, { QMetaType::QString, 17 },
        }}),
        // Slot 'extractAudioFromAAF'
        QtMocHelpers::SlotData<void(const QString &)>(16, 2, QMC::AccessPublic | QMC::MethodCloned, QMetaType::Void, {{
            { QMetaType::QString, 6 },
        }}),
        // Slot 'getAudioFileForRegion'
        QtMocHelpers::SlotData<QString(const QString &, const QVariantMap &)>(18, 2, QMC::AccessPublic, QMetaType::QString, {{
            { QMetaType::QString, 6 }, { 0x80000000 | 19, 20 },
        }}),
        // Slot 'cacheAudioFile'
        QtMocHelpers::SlotData<QString(const QString &)>(21, 2, QMC::AccessPublic, QMetaType::QString, {{
            { QMetaType::QString, 22 },
        }}),
        // Slot 'isAudioFileCached'
        QtMocHelpers::SlotData<bool(const QString &)>(23, 2, QMC::AccessPublic, QMetaType::Bool, {{
            { QMetaType::QString, 22 },
        }}),
        // Slot 'getCachedAudioFilePath'
        QtMocHelpers::SlotData<QString(const QString &)>(24, 2, QMC::AccessPublic, QMetaType::QString, {{
            { QMetaType::QString, 11 },
        }}),
        // Slot 'clearCache'
        QtMocHelpers::SlotData<void()>(25, 2, QMC::AccessPublic, QMetaType::Void),
        // Slot 'cleanupOldCachedFiles'
        QtMocHelpers::SlotData<void(int)>(26, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::Int, 27 },
        }}),
        // Slot 'cleanupOldCachedFiles'
        QtMocHelpers::SlotData<void()>(26, 2, QMC::AccessPublic | QMC::MethodCloned, QMetaType::Void),
        // Slot 'getSupportedAudioFormats'
        QtMocHelpers::SlotData<QStringList()>(28, 2, QMC::AccessPublic, QMetaType::QStringList),
        // Slot 'validateAudioFile'
        QtMocHelpers::SlotData<bool(const QString &)>(29, 2, QMC::AccessPublic, QMetaType::Bool, {{
            { QMetaType::QString, 22 },
        }}),
        // Slot 'convertToWAV'
        QtMocHelpers::SlotData<bool(const QString &, const QString &)>(30, 2, QMC::AccessPublic, QMetaType::Bool, {{
            { QMetaType::QString, 31 }, { QMetaType::QString, 32 },
        }}),
        // Slot 'resolveAudioFilePath'
        QtMocHelpers::SlotData<QString(const QString &, const QString &)>(33, 2, QMC::AccessPublic, QMetaType::QString, {{
            { QMetaType::QString, 6 }, { QMetaType::QString, 34 },
        }}),
        // Slot 'updateCacheStats'
        QtMocHelpers::SlotData<void()>(35, 2, QMC::AccessPrivate, QMetaType::Void),
    };
    QtMocHelpers::UintData qt_properties {
        // property 'cacheDirectory'
        QtMocHelpers::PropertyData<QString>(36, QMetaType::QString, QMC::DefaultPropertyFlags | QMC::Writable | QMC::StdCppSet, 0),
        // property 'cacheSize'
        QtMocHelpers::PropertyData<qint64>(37, QMetaType::LongLong, QMC::DefaultPropertyFlags, 1),
        // property 'cachedFileCount'
        QtMocHelpers::PropertyData<int>(38, QMetaType::Int, QMC::DefaultPropertyFlags, 2),
    };
    QtMocHelpers::UintData qt_enums {
    };
    return QtMocHelpers::metaObjectData<AudioFileManager, qt_meta_tag_ZN16AudioFileManagerE_t>(QMC::MetaObjectFlag{}, qt_stringData,
            qt_methods, qt_properties, qt_enums);
}
Q_CONSTINIT const QMetaObject AudioFileManager::staticMetaObject = { {
    QMetaObject::SuperData::link<QObject::staticMetaObject>(),
    qt_staticMetaObjectStaticContent<qt_meta_tag_ZN16AudioFileManagerE_t>.stringdata,
    qt_staticMetaObjectStaticContent<qt_meta_tag_ZN16AudioFileManagerE_t>.data,
    qt_static_metacall,
    nullptr,
    qt_staticMetaObjectRelocatingContent<qt_meta_tag_ZN16AudioFileManagerE_t>.metaTypes,
    nullptr
} };

void AudioFileManager::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    auto *_t = static_cast<AudioFileManager *>(_o);
    if (_c == QMetaObject::InvokeMetaMethod) {
        switch (_id) {
        case 0: _t->cacheDirectoryChanged(); break;
        case 1: _t->cacheSizeChanged(); break;
        case 2: _t->cachedFileCountChanged(); break;
        case 3: _t->audioExtractionCompleted((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<QStringList>>(_a[2]))); break;
        case 4: _t->audioExtractionError((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<QString>>(_a[2]))); break;
        case 5: _t->fileCached((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<QString>>(_a[2]))); break;
        case 6: _t->extractionProgress((*reinterpret_cast< std::add_pointer_t<int>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<QString>>(_a[2]))); break;
        case 7: _t->extractAudioFromAAF((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<QString>>(_a[2]))); break;
        case 8: _t->extractAudioFromAAF((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1]))); break;
        case 9: { QString _r = _t->getAudioFileForRegion((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<QVariantMap>>(_a[2])));
            if (_a[0]) *reinterpret_cast< QString*>(_a[0]) = std::move(_r); }  break;
        case 10: { QString _r = _t->cacheAudioFile((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1])));
            if (_a[0]) *reinterpret_cast< QString*>(_a[0]) = std::move(_r); }  break;
        case 11: { bool _r = _t->isAudioFileCached((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1])));
            if (_a[0]) *reinterpret_cast< bool*>(_a[0]) = std::move(_r); }  break;
        case 12: { QString _r = _t->getCachedAudioFilePath((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1])));
            if (_a[0]) *reinterpret_cast< QString*>(_a[0]) = std::move(_r); }  break;
        case 13: _t->clearCache(); break;
        case 14: _t->cleanupOldCachedFiles((*reinterpret_cast< std::add_pointer_t<int>>(_a[1]))); break;
        case 15: _t->cleanupOldCachedFiles(); break;
        case 16: { QStringList _r = _t->getSupportedAudioFormats();
            if (_a[0]) *reinterpret_cast< QStringList*>(_a[0]) = std::move(_r); }  break;
        case 17: { bool _r = _t->validateAudioFile((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1])));
            if (_a[0]) *reinterpret_cast< bool*>(_a[0]) = std::move(_r); }  break;
        case 18: { bool _r = _t->convertToWAV((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<QString>>(_a[2])));
            if (_a[0]) *reinterpret_cast< bool*>(_a[0]) = std::move(_r); }  break;
        case 19: { QString _r = _t->resolveAudioFilePath((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<QString>>(_a[2])));
            if (_a[0]) *reinterpret_cast< QString*>(_a[0]) = std::move(_r); }  break;
        case 20: _t->updateCacheStats(); break;
        default: ;
        }
    }
    if (_c == QMetaObject::IndexOfMethod) {
        if (QtMocHelpers::indexOfMethod<void (AudioFileManager::*)()>(_a, &AudioFileManager::cacheDirectoryChanged, 0))
            return;
        if (QtMocHelpers::indexOfMethod<void (AudioFileManager::*)()>(_a, &AudioFileManager::cacheSizeChanged, 1))
            return;
        if (QtMocHelpers::indexOfMethod<void (AudioFileManager::*)()>(_a, &AudioFileManager::cachedFileCountChanged, 2))
            return;
        if (QtMocHelpers::indexOfMethod<void (AudioFileManager::*)(const QString & , const QStringList & )>(_a, &AudioFileManager::audioExtractionCompleted, 3))
            return;
        if (QtMocHelpers::indexOfMethod<void (AudioFileManager::*)(const QString & , const QString & )>(_a, &AudioFileManager::audioExtractionError, 4))
            return;
        if (QtMocHelpers::indexOfMethod<void (AudioFileManager::*)(const QString & , const QString & )>(_a, &AudioFileManager::fileCached, 5))
            return;
        if (QtMocHelpers::indexOfMethod<void (AudioFileManager::*)(int , const QString & )>(_a, &AudioFileManager::extractionProgress, 6))
            return;
    }
    if (_c == QMetaObject::ReadProperty) {
        void *_v = _a[0];
        switch (_id) {
        case 0: *reinterpret_cast<QString*>(_v) = _t->cacheDirectory(); break;
        case 1: *reinterpret_cast<qint64*>(_v) = _t->cacheSize(); break;
        case 2: *reinterpret_cast<int*>(_v) = _t->cachedFileCount(); break;
        default: break;
        }
    }
    if (_c == QMetaObject::WriteProperty) {
        void *_v = _a[0];
        switch (_id) {
        case 0: _t->setCacheDirectory(*reinterpret_cast<QString*>(_v)); break;
        default: break;
        }
    }
}

const QMetaObject *AudioFileManager::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *AudioFileManager::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_staticMetaObjectStaticContent<qt_meta_tag_ZN16AudioFileManagerE_t>.strings))
        return static_cast<void*>(this);
    return QObject::qt_metacast(_clname);
}

int AudioFileManager::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QObject::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 21)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 21;
    }
    if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 21)
            *reinterpret_cast<QMetaType *>(_a[0]) = QMetaType();
        _id -= 21;
    }
    if (_c == QMetaObject::ReadProperty || _c == QMetaObject::WriteProperty
            || _c == QMetaObject::ResetProperty || _c == QMetaObject::BindableProperty
            || _c == QMetaObject::RegisterPropertyMetaType) {
        qt_static_metacall(this, _c, _id, _a);
        _id -= 3;
    }
    return _id;
}

// SIGNAL 0
void AudioFileManager::cacheDirectoryChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 0, nullptr);
}

// SIGNAL 1
void AudioFileManager::cacheSizeChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 1, nullptr);
}

// SIGNAL 2
void AudioFileManager::cachedFileCountChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 2, nullptr);
}

// SIGNAL 3
void AudioFileManager::audioExtractionCompleted(const QString & _t1, const QStringList & _t2)
{
    QMetaObject::activate<void>(this, &staticMetaObject, 3, nullptr, _t1, _t2);
}

// SIGNAL 4
void AudioFileManager::audioExtractionError(const QString & _t1, const QString & _t2)
{
    QMetaObject::activate<void>(this, &staticMetaObject, 4, nullptr, _t1, _t2);
}

// SIGNAL 5
void AudioFileManager::fileCached(const QString & _t1, const QString & _t2)
{
    QMetaObject::activate<void>(this, &staticMetaObject, 5, nullptr, _t1, _t2);
}

// SIGNAL 6
void AudioFileManager::extractionProgress(int _t1, const QString & _t2)
{
    QMetaObject::activate<void>(this, &staticMetaObject, 6, nullptr, _t1, _t2);
}
QT_WARNING_POP
