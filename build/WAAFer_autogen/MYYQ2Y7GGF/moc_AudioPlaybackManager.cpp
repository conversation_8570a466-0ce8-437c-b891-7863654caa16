/****************************************************************************
** Meta object code from reading C++ file 'AudioPlaybackManager.h'
**
** Created by: The Qt Meta Object Compiler version 69 (Qt 6.9.0)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include "../../../src/audio/AudioPlaybackManager.h"
#include <QtCore/qmetatype.h>

#include <QtCore/qtmochelpers.h>

#include <memory>


#include <QtCore/qxptype_traits.h>
#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'AudioPlaybackManager.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 69
#error "This file was generated using the moc from 6.9.0. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

#ifndef Q_CONSTINIT
#define Q_CONSTINIT
#endif

QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
QT_WARNING_DISABLE_GCC("-Wuseless-cast")
namespace {
struct qt_meta_tag_ZN20AudioPlaybackManagerE_t {};
} // unnamed namespace

template <> constexpr inline auto AudioPlaybackManager::qt_create_metaobjectdata<qt_meta_tag_ZN20AudioPlaybackManagerE_t>()
{
    namespace QMC = QtMocConstants;
    QtMocHelpers::StringRefStorage qt_stringData {
        "AudioPlaybackManager",
        "isPlayingChanged",
        "",
        "playing",
        "positionChanged",
        "position",
        "durationChanged",
        "duration",
        "currentRegionChanged",
        "regionId",
        "playbackFinished",
        "errorOccurred",
        "error",
        "play",
        "pause",
        "stop",
        "setPosition",
        "playRegion",
        "QVariantMap",
        "regionInfo",
        "loadAudioFile",
        "filePath",
        "startTime",
        "setVolume",
        "volume",
        "getAvailableAudioDevices",
        "QVariantList",
        "setAudioDevice",
        "deviceId",
        "getCurrentAudioDevice",
        "onMediaPlayerStateChanged",
        "QMediaPlayer::PlaybackState",
        "state",
        "onMediaPlayerPositionChanged",
        "onMediaPlayerDurationChanged",
        "onMediaPlayerError",
        "QMediaPlayer::Error",
        "errorString",
        "updatePosition",
        "checkRegionPlaybackEnd",
        "isPlaying",
        "currentRegion"
    };

    QtMocHelpers::UintData qt_methods {
        // Signal 'isPlayingChanged'
        QtMocHelpers::SignalData<void(bool)>(1, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::Bool, 3 },
        }}),
        // Signal 'positionChanged'
        QtMocHelpers::SignalData<void(double)>(4, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::Double, 5 },
        }}),
        // Signal 'durationChanged'
        QtMocHelpers::SignalData<void(double)>(6, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::Double, 7 },
        }}),
        // Signal 'currentRegionChanged'
        QtMocHelpers::SignalData<void(const QString &)>(8, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::QString, 9 },
        }}),
        // Signal 'playbackFinished'
        QtMocHelpers::SignalData<void()>(10, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'errorOccurred'
        QtMocHelpers::SignalData<void(const QString &)>(11, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::QString, 12 },
        }}),
        // Slot 'play'
        QtMocHelpers::SlotData<void()>(13, 2, QMC::AccessPublic, QMetaType::Void),
        // Slot 'pause'
        QtMocHelpers::SlotData<void()>(14, 2, QMC::AccessPublic, QMetaType::Void),
        // Slot 'stop'
        QtMocHelpers::SlotData<void()>(15, 2, QMC::AccessPublic, QMetaType::Void),
        // Slot 'setPosition'
        QtMocHelpers::SlotData<void(double)>(16, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::Double, 5 },
        }}),
        // Slot 'playRegion'
        QtMocHelpers::SlotData<void(const QVariantMap &)>(17, 2, QMC::AccessPublic, QMetaType::Void, {{
            { 0x80000000 | 18, 19 },
        }}),
        // Slot 'loadAudioFile'
        QtMocHelpers::SlotData<void(const QString &, double, double)>(20, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::QString, 21 }, { QMetaType::Double, 22 }, { QMetaType::Double, 7 },
        }}),
        // Slot 'loadAudioFile'
        QtMocHelpers::SlotData<void(const QString &, double)>(20, 2, QMC::AccessPublic | QMC::MethodCloned, QMetaType::Void, {{
            { QMetaType::QString, 21 }, { QMetaType::Double, 22 },
        }}),
        // Slot 'loadAudioFile'
        QtMocHelpers::SlotData<void(const QString &)>(20, 2, QMC::AccessPublic | QMC::MethodCloned, QMetaType::Void, {{
            { QMetaType::QString, 21 },
        }}),
        // Slot 'setVolume'
        QtMocHelpers::SlotData<void(double)>(23, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::Double, 24 },
        }}),
        // Slot 'volume'
        QtMocHelpers::SlotData<double() const>(24, 2, QMC::AccessPublic, QMetaType::Double),
        // Slot 'getAvailableAudioDevices'
        QtMocHelpers::SlotData<QVariantList() const>(25, 2, QMC::AccessPublic, 0x80000000 | 26),
        // Slot 'setAudioDevice'
        QtMocHelpers::SlotData<bool(const QString &)>(27, 2, QMC::AccessPublic, QMetaType::Bool, {{
            { QMetaType::QString, 28 },
        }}),
        // Slot 'getCurrentAudioDevice'
        QtMocHelpers::SlotData<QVariantMap() const>(29, 2, QMC::AccessPublic, 0x80000000 | 18),
        // Slot 'onMediaPlayerStateChanged'
        QtMocHelpers::SlotData<void(QMediaPlayer::PlaybackState)>(30, 2, QMC::AccessPrivate, QMetaType::Void, {{
            { 0x80000000 | 31, 32 },
        }}),
        // Slot 'onMediaPlayerPositionChanged'
        QtMocHelpers::SlotData<void(qint64)>(33, 2, QMC::AccessPrivate, QMetaType::Void, {{
            { QMetaType::LongLong, 5 },
        }}),
        // Slot 'onMediaPlayerDurationChanged'
        QtMocHelpers::SlotData<void(qint64)>(34, 2, QMC::AccessPrivate, QMetaType::Void, {{
            { QMetaType::LongLong, 7 },
        }}),
        // Slot 'onMediaPlayerError'
        QtMocHelpers::SlotData<void(QMediaPlayer::Error, const QString &)>(35, 2, QMC::AccessPrivate, QMetaType::Void, {{
            { 0x80000000 | 36, 12 }, { QMetaType::QString, 37 },
        }}),
        // Slot 'updatePosition'
        QtMocHelpers::SlotData<void()>(38, 2, QMC::AccessPrivate, QMetaType::Void),
        // Slot 'checkRegionPlaybackEnd'
        QtMocHelpers::SlotData<void()>(39, 2, QMC::AccessPrivate, QMetaType::Void),
    };
    QtMocHelpers::UintData qt_properties {
        // property 'isPlaying'
        QtMocHelpers::PropertyData<bool>(40, QMetaType::Bool, QMC::DefaultPropertyFlags, 0),
        // property 'position'
        QtMocHelpers::PropertyData<double>(5, QMetaType::Double, QMC::DefaultPropertyFlags, 1),
        // property 'duration'
        QtMocHelpers::PropertyData<double>(7, QMetaType::Double, QMC::DefaultPropertyFlags, 2),
        // property 'currentRegion'
        QtMocHelpers::PropertyData<QString>(41, QMetaType::QString, QMC::DefaultPropertyFlags, 3),
    };
    QtMocHelpers::UintData qt_enums {
    };
    return QtMocHelpers::metaObjectData<AudioPlaybackManager, qt_meta_tag_ZN20AudioPlaybackManagerE_t>(QMC::MetaObjectFlag{}, qt_stringData,
            qt_methods, qt_properties, qt_enums);
}
Q_CONSTINIT const QMetaObject AudioPlaybackManager::staticMetaObject = { {
    QMetaObject::SuperData::link<QObject::staticMetaObject>(),
    qt_staticMetaObjectStaticContent<qt_meta_tag_ZN20AudioPlaybackManagerE_t>.stringdata,
    qt_staticMetaObjectStaticContent<qt_meta_tag_ZN20AudioPlaybackManagerE_t>.data,
    qt_static_metacall,
    nullptr,
    qt_staticMetaObjectRelocatingContent<qt_meta_tag_ZN20AudioPlaybackManagerE_t>.metaTypes,
    nullptr
} };

void AudioPlaybackManager::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    auto *_t = static_cast<AudioPlaybackManager *>(_o);
    if (_c == QMetaObject::InvokeMetaMethod) {
        switch (_id) {
        case 0: _t->isPlayingChanged((*reinterpret_cast< std::add_pointer_t<bool>>(_a[1]))); break;
        case 1: _t->positionChanged((*reinterpret_cast< std::add_pointer_t<double>>(_a[1]))); break;
        case 2: _t->durationChanged((*reinterpret_cast< std::add_pointer_t<double>>(_a[1]))); break;
        case 3: _t->currentRegionChanged((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1]))); break;
        case 4: _t->playbackFinished(); break;
        case 5: _t->errorOccurred((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1]))); break;
        case 6: _t->play(); break;
        case 7: _t->pause(); break;
        case 8: _t->stop(); break;
        case 9: _t->setPosition((*reinterpret_cast< std::add_pointer_t<double>>(_a[1]))); break;
        case 10: _t->playRegion((*reinterpret_cast< std::add_pointer_t<QVariantMap>>(_a[1]))); break;
        case 11: _t->loadAudioFile((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<double>>(_a[2])),(*reinterpret_cast< std::add_pointer_t<double>>(_a[3]))); break;
        case 12: _t->loadAudioFile((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<double>>(_a[2]))); break;
        case 13: _t->loadAudioFile((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1]))); break;
        case 14: _t->setVolume((*reinterpret_cast< std::add_pointer_t<double>>(_a[1]))); break;
        case 15: { double _r = _t->volume();
            if (_a[0]) *reinterpret_cast< double*>(_a[0]) = std::move(_r); }  break;
        case 16: { QVariantList _r = _t->getAvailableAudioDevices();
            if (_a[0]) *reinterpret_cast< QVariantList*>(_a[0]) = std::move(_r); }  break;
        case 17: { bool _r = _t->setAudioDevice((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1])));
            if (_a[0]) *reinterpret_cast< bool*>(_a[0]) = std::move(_r); }  break;
        case 18: { QVariantMap _r = _t->getCurrentAudioDevice();
            if (_a[0]) *reinterpret_cast< QVariantMap*>(_a[0]) = std::move(_r); }  break;
        case 19: _t->onMediaPlayerStateChanged((*reinterpret_cast< std::add_pointer_t<QMediaPlayer::PlaybackState>>(_a[1]))); break;
        case 20: _t->onMediaPlayerPositionChanged((*reinterpret_cast< std::add_pointer_t<qint64>>(_a[1]))); break;
        case 21: _t->onMediaPlayerDurationChanged((*reinterpret_cast< std::add_pointer_t<qint64>>(_a[1]))); break;
        case 22: _t->onMediaPlayerError((*reinterpret_cast< std::add_pointer_t<QMediaPlayer::Error>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<QString>>(_a[2]))); break;
        case 23: _t->updatePosition(); break;
        case 24: _t->checkRegionPlaybackEnd(); break;
        default: ;
        }
    }
    if (_c == QMetaObject::IndexOfMethod) {
        if (QtMocHelpers::indexOfMethod<void (AudioPlaybackManager::*)(bool )>(_a, &AudioPlaybackManager::isPlayingChanged, 0))
            return;
        if (QtMocHelpers::indexOfMethod<void (AudioPlaybackManager::*)(double )>(_a, &AudioPlaybackManager::positionChanged, 1))
            return;
        if (QtMocHelpers::indexOfMethod<void (AudioPlaybackManager::*)(double )>(_a, &AudioPlaybackManager::durationChanged, 2))
            return;
        if (QtMocHelpers::indexOfMethod<void (AudioPlaybackManager::*)(const QString & )>(_a, &AudioPlaybackManager::currentRegionChanged, 3))
            return;
        if (QtMocHelpers::indexOfMethod<void (AudioPlaybackManager::*)()>(_a, &AudioPlaybackManager::playbackFinished, 4))
            return;
        if (QtMocHelpers::indexOfMethod<void (AudioPlaybackManager::*)(const QString & )>(_a, &AudioPlaybackManager::errorOccurred, 5))
            return;
    }
    if (_c == QMetaObject::ReadProperty) {
        void *_v = _a[0];
        switch (_id) {
        case 0: *reinterpret_cast<bool*>(_v) = _t->isPlaying(); break;
        case 1: *reinterpret_cast<double*>(_v) = _t->position(); break;
        case 2: *reinterpret_cast<double*>(_v) = _t->duration(); break;
        case 3: *reinterpret_cast<QString*>(_v) = _t->currentRegion(); break;
        default: break;
        }
    }
}

const QMetaObject *AudioPlaybackManager::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *AudioPlaybackManager::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_staticMetaObjectStaticContent<qt_meta_tag_ZN20AudioPlaybackManagerE_t>.strings))
        return static_cast<void*>(this);
    return QObject::qt_metacast(_clname);
}

int AudioPlaybackManager::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QObject::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 25)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 25;
    }
    if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 25)
            *reinterpret_cast<QMetaType *>(_a[0]) = QMetaType();
        _id -= 25;
    }
    if (_c == QMetaObject::ReadProperty || _c == QMetaObject::WriteProperty
            || _c == QMetaObject::ResetProperty || _c == QMetaObject::BindableProperty
            || _c == QMetaObject::RegisterPropertyMetaType) {
        qt_static_metacall(this, _c, _id, _a);
        _id -= 4;
    }
    return _id;
}

// SIGNAL 0
void AudioPlaybackManager::isPlayingChanged(bool _t1)
{
    QMetaObject::activate<void>(this, &staticMetaObject, 0, nullptr, _t1);
}

// SIGNAL 1
void AudioPlaybackManager::positionChanged(double _t1)
{
    QMetaObject::activate<void>(this, &staticMetaObject, 1, nullptr, _t1);
}

// SIGNAL 2
void AudioPlaybackManager::durationChanged(double _t1)
{
    QMetaObject::activate<void>(this, &staticMetaObject, 2, nullptr, _t1);
}

// SIGNAL 3
void AudioPlaybackManager::currentRegionChanged(const QString & _t1)
{
    QMetaObject::activate<void>(this, &staticMetaObject, 3, nullptr, _t1);
}

// SIGNAL 4
void AudioPlaybackManager::playbackFinished()
{
    QMetaObject::activate(this, &staticMetaObject, 4, nullptr);
}

// SIGNAL 5
void AudioPlaybackManager::errorOccurred(const QString & _t1)
{
    QMetaObject::activate<void>(this, &staticMetaObject, 5, nullptr, _t1);
}
QT_WARNING_POP
