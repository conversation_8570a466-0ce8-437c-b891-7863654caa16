/****************************************************************************
** Meta object code from reading C++ file 'AudioAnalyzer.h'
**
** Created by: The Qt Meta Object Compiler version 69 (Qt 6.9.0)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include "../../../src/audio/AudioAnalyzer.h"
#include <QtCore/qmetatype.h>

#include <QtCore/qtmochelpers.h>

#include <memory>


#include <QtCore/qxptype_traits.h>
#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'AudioAnalyzer.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 69
#error "This file was generated using the moc from 6.9.0. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

#ifndef Q_CONSTINIT
#define Q_CONSTINIT
#endif

QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
QT_WARNING_DISABLE_GCC("-Wuseless-cast")
namespace {
struct qt_meta_tag_ZN13AudioAnalyzerE_t {};
} // unnamed namespace

template <> constexpr inline auto AudioAnalyzer::qt_create_metaobjectdata<qt_meta_tag_ZN13AudioAnalyzerE_t>()
{
    namespace QMC = QtMocConstants;
    QtMocHelpers::StringRefStorage qt_stringData {
        "AudioAnalyzer",
        "isAnalyzingChanged",
        "",
        "currentFileChanged",
        "progressChanged",
        "analysisCompleted",
        "filePath",
        "QVariantMap",
        "results",
        "mfccExtractionCompleted",
        "QVariantList",
        "mfccData",
        "speakerDiarizationCompleted",
        "speakers",
        "audioClassificationCompleted",
        "classification",
        "spectralAnalysisCompleted",
        "spectralData",
        "analysisError",
        "error",
        "analyzeAudioFile",
        "regionInfo",
        "extractMFCCFeatures",
        "startTime",
        "duration",
        "performSpeakerDiarization",
        "classifyAudioContent",
        "extractSpectralFeatures",
        "cancelAnalysis",
        "onAnalysisProgress",
        "progress",
        "message",
        "isAnalyzing",
        "currentFile"
    };

    QtMocHelpers::UintData qt_methods {
        // Signal 'isAnalyzingChanged'
        QtMocHelpers::SignalData<void()>(1, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'currentFileChanged'
        QtMocHelpers::SignalData<void()>(3, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'progressChanged'
        QtMocHelpers::SignalData<void()>(4, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'analysisCompleted'
        QtMocHelpers::SignalData<void(const QString &, const QVariantMap &)>(5, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::QString, 6 }, { 0x80000000 | 7, 8 },
        }}),
        // Signal 'mfccExtractionCompleted'
        QtMocHelpers::SignalData<void(const QString &, const QVariantList &)>(9, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::QString, 6 }, { 0x80000000 | 10, 11 },
        }}),
        // Signal 'speakerDiarizationCompleted'
        QtMocHelpers::SignalData<void(const QString &, const QVariantList &)>(12, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::QString, 6 }, { 0x80000000 | 10, 13 },
        }}),
        // Signal 'audioClassificationCompleted'
        QtMocHelpers::SignalData<void(const QString &, const QVariantMap &)>(14, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::QString, 6 }, { 0x80000000 | 7, 15 },
        }}),
        // Signal 'spectralAnalysisCompleted'
        QtMocHelpers::SignalData<void(const QString &, const QVariantMap &)>(16, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::QString, 6 }, { 0x80000000 | 7, 17 },
        }}),
        // Signal 'analysisError'
        QtMocHelpers::SignalData<void(const QString &, const QString &)>(18, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::QString, 6 }, { QMetaType::QString, 19 },
        }}),
        // Slot 'analyzeAudioFile'
        QtMocHelpers::SlotData<void(const QString &, const QVariantMap &)>(20, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::QString, 6 }, { 0x80000000 | 7, 21 },
        }}),
        // Slot 'extractMFCCFeatures'
        QtMocHelpers::SlotData<void(const QString &, double, double)>(22, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::QString, 6 }, { QMetaType::Double, 23 }, { QMetaType::Double, 24 },
        }}),
        // Slot 'extractMFCCFeatures'
        QtMocHelpers::SlotData<void(const QString &, double)>(22, 2, QMC::AccessPublic | QMC::MethodCloned, QMetaType::Void, {{
            { QMetaType::QString, 6 }, { QMetaType::Double, 23 },
        }}),
        // Slot 'extractMFCCFeatures'
        QtMocHelpers::SlotData<void(const QString &)>(22, 2, QMC::AccessPublic | QMC::MethodCloned, QMetaType::Void, {{
            { QMetaType::QString, 6 },
        }}),
        // Slot 'performSpeakerDiarization'
        QtMocHelpers::SlotData<void(const QString &)>(25, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::QString, 6 },
        }}),
        // Slot 'classifyAudioContent'
        QtMocHelpers::SlotData<void(const QString &)>(26, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::QString, 6 },
        }}),
        // Slot 'extractSpectralFeatures'
        QtMocHelpers::SlotData<void(const QString &)>(27, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::QString, 6 },
        }}),
        // Slot 'cancelAnalysis'
        QtMocHelpers::SlotData<void()>(28, 2, QMC::AccessPublic, QMetaType::Void),
        // Slot 'onAnalysisProgress'
        QtMocHelpers::SlotData<void(double, const QString &)>(29, 2, QMC::AccessPrivate, QMetaType::Void, {{
            { QMetaType::Double, 30 }, { QMetaType::QString, 31 },
        }}),
    };
    QtMocHelpers::UintData qt_properties {
        // property 'isAnalyzing'
        QtMocHelpers::PropertyData<bool>(32, QMetaType::Bool, QMC::DefaultPropertyFlags, 0),
        // property 'currentFile'
        QtMocHelpers::PropertyData<QString>(33, QMetaType::QString, QMC::DefaultPropertyFlags, 1),
        // property 'progress'
        QtMocHelpers::PropertyData<double>(30, QMetaType::Double, QMC::DefaultPropertyFlags, 2),
    };
    QtMocHelpers::UintData qt_enums {
    };
    return QtMocHelpers::metaObjectData<AudioAnalyzer, qt_meta_tag_ZN13AudioAnalyzerE_t>(QMC::MetaObjectFlag{}, qt_stringData,
            qt_methods, qt_properties, qt_enums);
}
Q_CONSTINIT const QMetaObject AudioAnalyzer::staticMetaObject = { {
    QMetaObject::SuperData::link<QObject::staticMetaObject>(),
    qt_staticMetaObjectStaticContent<qt_meta_tag_ZN13AudioAnalyzerE_t>.stringdata,
    qt_staticMetaObjectStaticContent<qt_meta_tag_ZN13AudioAnalyzerE_t>.data,
    qt_static_metacall,
    nullptr,
    qt_staticMetaObjectRelocatingContent<qt_meta_tag_ZN13AudioAnalyzerE_t>.metaTypes,
    nullptr
} };

void AudioAnalyzer::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    auto *_t = static_cast<AudioAnalyzer *>(_o);
    if (_c == QMetaObject::InvokeMetaMethod) {
        switch (_id) {
        case 0: _t->isAnalyzingChanged(); break;
        case 1: _t->currentFileChanged(); break;
        case 2: _t->progressChanged(); break;
        case 3: _t->analysisCompleted((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<QVariantMap>>(_a[2]))); break;
        case 4: _t->mfccExtractionCompleted((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<QVariantList>>(_a[2]))); break;
        case 5: _t->speakerDiarizationCompleted((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<QVariantList>>(_a[2]))); break;
        case 6: _t->audioClassificationCompleted((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<QVariantMap>>(_a[2]))); break;
        case 7: _t->spectralAnalysisCompleted((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<QVariantMap>>(_a[2]))); break;
        case 8: _t->analysisError((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<QString>>(_a[2]))); break;
        case 9: _t->analyzeAudioFile((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<QVariantMap>>(_a[2]))); break;
        case 10: _t->extractMFCCFeatures((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<double>>(_a[2])),(*reinterpret_cast< std::add_pointer_t<double>>(_a[3]))); break;
        case 11: _t->extractMFCCFeatures((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<double>>(_a[2]))); break;
        case 12: _t->extractMFCCFeatures((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1]))); break;
        case 13: _t->performSpeakerDiarization((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1]))); break;
        case 14: _t->classifyAudioContent((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1]))); break;
        case 15: _t->extractSpectralFeatures((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1]))); break;
        case 16: _t->cancelAnalysis(); break;
        case 17: _t->onAnalysisProgress((*reinterpret_cast< std::add_pointer_t<double>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<QString>>(_a[2]))); break;
        default: ;
        }
    }
    if (_c == QMetaObject::IndexOfMethod) {
        if (QtMocHelpers::indexOfMethod<void (AudioAnalyzer::*)()>(_a, &AudioAnalyzer::isAnalyzingChanged, 0))
            return;
        if (QtMocHelpers::indexOfMethod<void (AudioAnalyzer::*)()>(_a, &AudioAnalyzer::currentFileChanged, 1))
            return;
        if (QtMocHelpers::indexOfMethod<void (AudioAnalyzer::*)()>(_a, &AudioAnalyzer::progressChanged, 2))
            return;
        if (QtMocHelpers::indexOfMethod<void (AudioAnalyzer::*)(const QString & , const QVariantMap & )>(_a, &AudioAnalyzer::analysisCompleted, 3))
            return;
        if (QtMocHelpers::indexOfMethod<void (AudioAnalyzer::*)(const QString & , const QVariantList & )>(_a, &AudioAnalyzer::mfccExtractionCompleted, 4))
            return;
        if (QtMocHelpers::indexOfMethod<void (AudioAnalyzer::*)(const QString & , const QVariantList & )>(_a, &AudioAnalyzer::speakerDiarizationCompleted, 5))
            return;
        if (QtMocHelpers::indexOfMethod<void (AudioAnalyzer::*)(const QString & , const QVariantMap & )>(_a, &AudioAnalyzer::audioClassificationCompleted, 6))
            return;
        if (QtMocHelpers::indexOfMethod<void (AudioAnalyzer::*)(const QString & , const QVariantMap & )>(_a, &AudioAnalyzer::spectralAnalysisCompleted, 7))
            return;
        if (QtMocHelpers::indexOfMethod<void (AudioAnalyzer::*)(const QString & , const QString & )>(_a, &AudioAnalyzer::analysisError, 8))
            return;
    }
    if (_c == QMetaObject::ReadProperty) {
        void *_v = _a[0];
        switch (_id) {
        case 0: *reinterpret_cast<bool*>(_v) = _t->isAnalyzing(); break;
        case 1: *reinterpret_cast<QString*>(_v) = _t->currentFile(); break;
        case 2: *reinterpret_cast<double*>(_v) = _t->progress(); break;
        default: break;
        }
    }
}

const QMetaObject *AudioAnalyzer::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *AudioAnalyzer::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_staticMetaObjectStaticContent<qt_meta_tag_ZN13AudioAnalyzerE_t>.strings))
        return static_cast<void*>(this);
    return QObject::qt_metacast(_clname);
}

int AudioAnalyzer::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QObject::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 18)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 18;
    }
    if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 18)
            *reinterpret_cast<QMetaType *>(_a[0]) = QMetaType();
        _id -= 18;
    }
    if (_c == QMetaObject::ReadProperty || _c == QMetaObject::WriteProperty
            || _c == QMetaObject::ResetProperty || _c == QMetaObject::BindableProperty
            || _c == QMetaObject::RegisterPropertyMetaType) {
        qt_static_metacall(this, _c, _id, _a);
        _id -= 3;
    }
    return _id;
}

// SIGNAL 0
void AudioAnalyzer::isAnalyzingChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 0, nullptr);
}

// SIGNAL 1
void AudioAnalyzer::currentFileChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 1, nullptr);
}

// SIGNAL 2
void AudioAnalyzer::progressChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 2, nullptr);
}

// SIGNAL 3
void AudioAnalyzer::analysisCompleted(const QString & _t1, const QVariantMap & _t2)
{
    QMetaObject::activate<void>(this, &staticMetaObject, 3, nullptr, _t1, _t2);
}

// SIGNAL 4
void AudioAnalyzer::mfccExtractionCompleted(const QString & _t1, const QVariantList & _t2)
{
    QMetaObject::activate<void>(this, &staticMetaObject, 4, nullptr, _t1, _t2);
}

// SIGNAL 5
void AudioAnalyzer::speakerDiarizationCompleted(const QString & _t1, const QVariantList & _t2)
{
    QMetaObject::activate<void>(this, &staticMetaObject, 5, nullptr, _t1, _t2);
}

// SIGNAL 6
void AudioAnalyzer::audioClassificationCompleted(const QString & _t1, const QVariantMap & _t2)
{
    QMetaObject::activate<void>(this, &staticMetaObject, 6, nullptr, _t1, _t2);
}

// SIGNAL 7
void AudioAnalyzer::spectralAnalysisCompleted(const QString & _t1, const QVariantMap & _t2)
{
    QMetaObject::activate<void>(this, &staticMetaObject, 7, nullptr, _t1, _t2);
}

// SIGNAL 8
void AudioAnalyzer::analysisError(const QString & _t1, const QString & _t2)
{
    QMetaObject::activate<void>(this, &staticMetaObject, 8, nullptr, _t1, _t2);
}
QT_WARNING_POP
