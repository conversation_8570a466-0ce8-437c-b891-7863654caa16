/****************************************************************************
** Meta object code from reading C++ file 'WebRTCVAD.h'
**
** Created by: The Qt Meta Object Compiler version 69 (Qt 6.9.0)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include "../../../src/audio/WebRTCVAD.h"
#include <QtCore/qmetatype.h>
#include <QtCore/QList>

#include <QtCore/qtmochelpers.h>

#include <memory>


#include <QtCore/qxptype_traits.h>
#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'WebRTCVAD.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 69
#error "This file was generated using the moc from 6.9.0. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

#ifndef Q_CONSTINIT
#define Q_CONSTINIT
#endif

QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
QT_WARNING_DISABLE_GCC("-Wuseless-cast")
namespace {
struct qt_meta_tag_ZN9WebRTCVADE_t {};
} // unnamed namespace

template <> constexpr inline auto WebRTCVAD::qt_create_metaobjectdata<qt_meta_tag_ZN9WebRTCVADE_t>()
{
    namespace QMC = QtMocConstants;
    QtMocHelpers::StringRefStorage qt_stringData {
        "WebRTCVAD",
        "analysisProgress",
        "",
        "progress",
        "message",
        "analysisCompleted",
        "QList<VADResult>",
        "results",
        "analysisError",
        "error",
        "Aggressiveness",
        "Least",
        "Low",
        "Medium",
        "High"
    };

    QtMocHelpers::UintData qt_methods {
        // Signal 'analysisProgress'
        QtMocHelpers::SignalData<void(double, const QString &)>(1, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::Double, 3 }, { QMetaType::QString, 4 },
        }}),
        // Signal 'analysisCompleted'
        QtMocHelpers::SignalData<void(const QVector<VADResult> &)>(5, 2, QMC::AccessPublic, QMetaType::Void, {{
            { 0x80000000 | 6, 7 },
        }}),
        // Signal 'analysisError'
        QtMocHelpers::SignalData<void(const QString &)>(8, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::QString, 9 },
        }}),
    };
    QtMocHelpers::UintData qt_properties {
    };
    QtMocHelpers::UintData qt_enums {
        // enum 'Aggressiveness'
        QtMocHelpers::EnumData<Aggressiveness>(10, 10, QMC::EnumFlags{}).add({
            {   11, Aggressiveness::Least },
            {   12, Aggressiveness::Low },
            {   13, Aggressiveness::Medium },
            {   14, Aggressiveness::High },
        }),
    };
    return QtMocHelpers::metaObjectData<WebRTCVAD, qt_meta_tag_ZN9WebRTCVADE_t>(QMC::MetaObjectFlag{}, qt_stringData,
            qt_methods, qt_properties, qt_enums);
}
Q_CONSTINIT const QMetaObject WebRTCVAD::staticMetaObject = { {
    QMetaObject::SuperData::link<QObject::staticMetaObject>(),
    qt_staticMetaObjectStaticContent<qt_meta_tag_ZN9WebRTCVADE_t>.stringdata,
    qt_staticMetaObjectStaticContent<qt_meta_tag_ZN9WebRTCVADE_t>.data,
    qt_static_metacall,
    nullptr,
    qt_staticMetaObjectRelocatingContent<qt_meta_tag_ZN9WebRTCVADE_t>.metaTypes,
    nullptr
} };

void WebRTCVAD::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    auto *_t = static_cast<WebRTCVAD *>(_o);
    if (_c == QMetaObject::InvokeMetaMethod) {
        switch (_id) {
        case 0: _t->analysisProgress((*reinterpret_cast< std::add_pointer_t<double>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<QString>>(_a[2]))); break;
        case 1: _t->analysisCompleted((*reinterpret_cast< std::add_pointer_t<QList<VADResult>>>(_a[1]))); break;
        case 2: _t->analysisError((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1]))); break;
        default: ;
        }
    }
    if (_c == QMetaObject::IndexOfMethod) {
        if (QtMocHelpers::indexOfMethod<void (WebRTCVAD::*)(double , const QString & )>(_a, &WebRTCVAD::analysisProgress, 0))
            return;
        if (QtMocHelpers::indexOfMethod<void (WebRTCVAD::*)(const QVector<VADResult> & )>(_a, &WebRTCVAD::analysisCompleted, 1))
            return;
        if (QtMocHelpers::indexOfMethod<void (WebRTCVAD::*)(const QString & )>(_a, &WebRTCVAD::analysisError, 2))
            return;
    }
}

const QMetaObject *WebRTCVAD::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *WebRTCVAD::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_staticMetaObjectStaticContent<qt_meta_tag_ZN9WebRTCVADE_t>.strings))
        return static_cast<void*>(this);
    return QObject::qt_metacast(_clname);
}

int WebRTCVAD::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QObject::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 3)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 3;
    }
    if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 3)
            *reinterpret_cast<QMetaType *>(_a[0]) = QMetaType();
        _id -= 3;
    }
    return _id;
}

// SIGNAL 0
void WebRTCVAD::analysisProgress(double _t1, const QString & _t2)
{
    QMetaObject::activate<void>(this, &staticMetaObject, 0, nullptr, _t1, _t2);
}

// SIGNAL 1
void WebRTCVAD::analysisCompleted(const QVector<VADResult> & _t1)
{
    QMetaObject::activate<void>(this, &staticMetaObject, 1, nullptr, _t1);
}

// SIGNAL 2
void WebRTCVAD::analysisError(const QString & _t1)
{
    QMetaObject::activate<void>(this, &staticMetaObject, 2, nullptr, _t1);
}
QT_WARNING_POP
