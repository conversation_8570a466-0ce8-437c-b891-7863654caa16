/****************************************************************************
** Meta object code from reading C++ file 'ClassificationEngine.h'
**
** Created by: The Qt Meta Object Compiler version 69 (Qt 6.9.0)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include "../../../src/audio/ClassificationEngine.h"
#include <QtCore/qmetatype.h>

#include <QtCore/qtmochelpers.h>

#include <memory>


#include <QtCore/qxptype_traits.h>
#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'ClassificationEngine.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 69
#error "This file was generated using the moc from 6.9.0. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

#ifndef Q_CONSTINIT
#define Q_CONSTINIT
#endif

QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
QT_WARNING_DISABLE_GCC("-Wuseless-cast")
namespace {
struct qt_meta_tag_ZN20ClassificationEngineE_t {};
} // unnamed namespace

template <> constexpr inline auto ClassificationEngine::qt_create_metaobjectdata<qt_meta_tag_ZN20ClassificationEngineE_t>()
{
    namespace QMC = QtMocConstants;
    QtMocHelpers::StringRefStorage qt_stringData {
        "ClassificationEngine",
        "isClassifyingChanged",
        "",
        "currentRegionChanged",
        "overallProgressChanged",
        "regionClassified",
        "regionId",
        "QVariantMap",
        "result",
        "classificationCompleted",
        "QVariantList",
        "results",
        "classificationError",
        "error",
        "regionsNeedReview",
        "regions",
        "classifyRegions",
        "audioFilePaths",
        "classifyRegion",
        "regionInfo",
        "audioFilePath",
        "cancelClassification",
        "pauseClassification",
        "resumeClassification",
        "getClassificationResults",
        "getRegionsNeedingReview",
        "updateRegionClassification",
        "newClassification",
        "confidence",
        "classifyRegionInternal",
        "ClassificationResult",
        "getSpeakerStatistics",
        "getDetectedSpeakers",
        "clearSpeakerData",
        "processNextRegion",
        "processRegionsParallel",
        "onRegionCompleted",
        "checkParallelCompletion",
        "isClassifying",
        "currentRegion",
        "overallProgress"
    };

    QtMocHelpers::UintData qt_methods {
        // Signal 'isClassifyingChanged'
        QtMocHelpers::SignalData<void()>(1, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'currentRegionChanged'
        QtMocHelpers::SignalData<void()>(3, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'overallProgressChanged'
        QtMocHelpers::SignalData<void()>(4, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'regionClassified'
        QtMocHelpers::SignalData<void(const QString &, const QVariantMap &)>(5, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::QString, 6 }, { 0x80000000 | 7, 8 },
        }}),
        // Signal 'classificationCompleted'
        QtMocHelpers::SignalData<void(const QVariantList &)>(9, 2, QMC::AccessPublic, QMetaType::Void, {{
            { 0x80000000 | 10, 11 },
        }}),
        // Signal 'classificationError'
        QtMocHelpers::SignalData<void(const QString &, const QString &)>(12, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::QString, 6 }, { QMetaType::QString, 13 },
        }}),
        // Signal 'regionsNeedReview'
        QtMocHelpers::SignalData<void(const QVariantList &)>(14, 2, QMC::AccessPublic, QMetaType::Void, {{
            { 0x80000000 | 10, 15 },
        }}),
        // Slot 'classifyRegions'
        QtMocHelpers::SlotData<void(const QVariantList &, const QVariantMap &)>(16, 2, QMC::AccessPublic, QMetaType::Void, {{
            { 0x80000000 | 10, 15 }, { 0x80000000 | 7, 17 },
        }}),
        // Slot 'classifyRegion'
        QtMocHelpers::SlotData<void(const QVariantMap &, const QString &)>(18, 2, QMC::AccessPublic, QMetaType::Void, {{
            { 0x80000000 | 7, 19 }, { QMetaType::QString, 20 },
        }}),
        // Slot 'cancelClassification'
        QtMocHelpers::SlotData<void()>(21, 2, QMC::AccessPublic, QMetaType::Void),
        // Slot 'pauseClassification'
        QtMocHelpers::SlotData<void()>(22, 2, QMC::AccessPublic, QMetaType::Void),
        // Slot 'resumeClassification'
        QtMocHelpers::SlotData<void()>(23, 2, QMC::AccessPublic, QMetaType::Void),
        // Slot 'getClassificationResults'
        QtMocHelpers::SlotData<QVariantList() const>(24, 2, QMC::AccessPublic, 0x80000000 | 10),
        // Slot 'getRegionsNeedingReview'
        QtMocHelpers::SlotData<QVariantList() const>(25, 2, QMC::AccessPublic, 0x80000000 | 10),
        // Slot 'updateRegionClassification'
        QtMocHelpers::SlotData<void(const QString &, const QString &, double)>(26, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::QString, 6 }, { QMetaType::QString, 27 }, { QMetaType::Double, 28 },
        }}),
        // Slot 'updateRegionClassification'
        QtMocHelpers::SlotData<void(const QString &, const QString &)>(26, 2, QMC::AccessPublic | QMC::MethodCloned, QMetaType::Void, {{
            { QMetaType::QString, 6 }, { QMetaType::QString, 27 },
        }}),
        // Slot 'classifyRegionInternal'
        QtMocHelpers::SlotData<ClassificationResult(const QVariantMap &, const QString &)>(29, 2, QMC::AccessPublic, 0x80000000 | 30, {{
            { 0x80000000 | 7, 19 }, { QMetaType::QString, 20 },
        }}),
        // Slot 'getSpeakerStatistics'
        QtMocHelpers::SlotData<QVariantMap() const>(31, 2, QMC::AccessPublic, 0x80000000 | 7),
        // Slot 'getDetectedSpeakers'
        QtMocHelpers::SlotData<QVariantList() const>(32, 2, QMC::AccessPublic, 0x80000000 | 10),
        // Slot 'clearSpeakerData'
        QtMocHelpers::SlotData<void()>(33, 2, QMC::AccessPublic, QMetaType::Void),
        // Slot 'processNextRegion'
        QtMocHelpers::SlotData<void()>(34, 2, QMC::AccessPrivate, QMetaType::Void),
        // Slot 'processRegionsParallel'
        QtMocHelpers::SlotData<void()>(35, 2, QMC::AccessPrivate, QMetaType::Void),
        // Slot 'onRegionCompleted'
        QtMocHelpers::SlotData<void(const QString &, const ClassificationResult &)>(36, 2, QMC::AccessPrivate, QMetaType::Void, {{
            { QMetaType::QString, 6 }, { 0x80000000 | 30, 8 },
        }}),
        // Slot 'checkParallelCompletion'
        QtMocHelpers::SlotData<void()>(37, 2, QMC::AccessPrivate, QMetaType::Void),
    };
    QtMocHelpers::UintData qt_properties {
        // property 'isClassifying'
        QtMocHelpers::PropertyData<bool>(38, QMetaType::Bool, QMC::DefaultPropertyFlags, 0),
        // property 'currentRegion'
        QtMocHelpers::PropertyData<QString>(39, QMetaType::QString, QMC::DefaultPropertyFlags, 1),
        // property 'overallProgress'
        QtMocHelpers::PropertyData<double>(40, QMetaType::Double, QMC::DefaultPropertyFlags, 2),
    };
    QtMocHelpers::UintData qt_enums {
    };
    return QtMocHelpers::metaObjectData<ClassificationEngine, qt_meta_tag_ZN20ClassificationEngineE_t>(QMC::MetaObjectFlag{}, qt_stringData,
            qt_methods, qt_properties, qt_enums);
}
Q_CONSTINIT const QMetaObject ClassificationEngine::staticMetaObject = { {
    QMetaObject::SuperData::link<QObject::staticMetaObject>(),
    qt_staticMetaObjectStaticContent<qt_meta_tag_ZN20ClassificationEngineE_t>.stringdata,
    qt_staticMetaObjectStaticContent<qt_meta_tag_ZN20ClassificationEngineE_t>.data,
    qt_static_metacall,
    nullptr,
    qt_staticMetaObjectRelocatingContent<qt_meta_tag_ZN20ClassificationEngineE_t>.metaTypes,
    nullptr
} };

void ClassificationEngine::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    auto *_t = static_cast<ClassificationEngine *>(_o);
    if (_c == QMetaObject::InvokeMetaMethod) {
        switch (_id) {
        case 0: _t->isClassifyingChanged(); break;
        case 1: _t->currentRegionChanged(); break;
        case 2: _t->overallProgressChanged(); break;
        case 3: _t->regionClassified((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<QVariantMap>>(_a[2]))); break;
        case 4: _t->classificationCompleted((*reinterpret_cast< std::add_pointer_t<QVariantList>>(_a[1]))); break;
        case 5: _t->classificationError((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<QString>>(_a[2]))); break;
        case 6: _t->regionsNeedReview((*reinterpret_cast< std::add_pointer_t<QVariantList>>(_a[1]))); break;
        case 7: _t->classifyRegions((*reinterpret_cast< std::add_pointer_t<QVariantList>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<QVariantMap>>(_a[2]))); break;
        case 8: _t->classifyRegion((*reinterpret_cast< std::add_pointer_t<QVariantMap>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<QString>>(_a[2]))); break;
        case 9: _t->cancelClassification(); break;
        case 10: _t->pauseClassification(); break;
        case 11: _t->resumeClassification(); break;
        case 12: { QVariantList _r = _t->getClassificationResults();
            if (_a[0]) *reinterpret_cast< QVariantList*>(_a[0]) = std::move(_r); }  break;
        case 13: { QVariantList _r = _t->getRegionsNeedingReview();
            if (_a[0]) *reinterpret_cast< QVariantList*>(_a[0]) = std::move(_r); }  break;
        case 14: _t->updateRegionClassification((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<QString>>(_a[2])),(*reinterpret_cast< std::add_pointer_t<double>>(_a[3]))); break;
        case 15: _t->updateRegionClassification((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<QString>>(_a[2]))); break;
        case 16: { ClassificationResult _r = _t->classifyRegionInternal((*reinterpret_cast< std::add_pointer_t<QVariantMap>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<QString>>(_a[2])));
            if (_a[0]) *reinterpret_cast< ClassificationResult*>(_a[0]) = std::move(_r); }  break;
        case 17: { QVariantMap _r = _t->getSpeakerStatistics();
            if (_a[0]) *reinterpret_cast< QVariantMap*>(_a[0]) = std::move(_r); }  break;
        case 18: { QVariantList _r = _t->getDetectedSpeakers();
            if (_a[0]) *reinterpret_cast< QVariantList*>(_a[0]) = std::move(_r); }  break;
        case 19: _t->clearSpeakerData(); break;
        case 20: _t->processNextRegion(); break;
        case 21: _t->processRegionsParallel(); break;
        case 22: _t->onRegionCompleted((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<ClassificationResult>>(_a[2]))); break;
        case 23: _t->checkParallelCompletion(); break;
        default: ;
        }
    }
    if (_c == QMetaObject::IndexOfMethod) {
        if (QtMocHelpers::indexOfMethod<void (ClassificationEngine::*)()>(_a, &ClassificationEngine::isClassifyingChanged, 0))
            return;
        if (QtMocHelpers::indexOfMethod<void (ClassificationEngine::*)()>(_a, &ClassificationEngine::currentRegionChanged, 1))
            return;
        if (QtMocHelpers::indexOfMethod<void (ClassificationEngine::*)()>(_a, &ClassificationEngine::overallProgressChanged, 2))
            return;
        if (QtMocHelpers::indexOfMethod<void (ClassificationEngine::*)(const QString & , const QVariantMap & )>(_a, &ClassificationEngine::regionClassified, 3))
            return;
        if (QtMocHelpers::indexOfMethod<void (ClassificationEngine::*)(const QVariantList & )>(_a, &ClassificationEngine::classificationCompleted, 4))
            return;
        if (QtMocHelpers::indexOfMethod<void (ClassificationEngine::*)(const QString & , const QString & )>(_a, &ClassificationEngine::classificationError, 5))
            return;
        if (QtMocHelpers::indexOfMethod<void (ClassificationEngine::*)(const QVariantList & )>(_a, &ClassificationEngine::regionsNeedReview, 6))
            return;
    }
    if (_c == QMetaObject::ReadProperty) {
        void *_v = _a[0];
        switch (_id) {
        case 0: *reinterpret_cast<bool*>(_v) = _t->isClassifying(); break;
        case 1: *reinterpret_cast<QString*>(_v) = _t->currentRegion(); break;
        case 2: *reinterpret_cast<double*>(_v) = _t->overallProgress(); break;
        default: break;
        }
    }
}

const QMetaObject *ClassificationEngine::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *ClassificationEngine::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_staticMetaObjectStaticContent<qt_meta_tag_ZN20ClassificationEngineE_t>.strings))
        return static_cast<void*>(this);
    return QObject::qt_metacast(_clname);
}

int ClassificationEngine::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QObject::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 24)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 24;
    }
    if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 24)
            *reinterpret_cast<QMetaType *>(_a[0]) = QMetaType();
        _id -= 24;
    }
    if (_c == QMetaObject::ReadProperty || _c == QMetaObject::WriteProperty
            || _c == QMetaObject::ResetProperty || _c == QMetaObject::BindableProperty
            || _c == QMetaObject::RegisterPropertyMetaType) {
        qt_static_metacall(this, _c, _id, _a);
        _id -= 3;
    }
    return _id;
}

// SIGNAL 0
void ClassificationEngine::isClassifyingChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 0, nullptr);
}

// SIGNAL 1
void ClassificationEngine::currentRegionChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 1, nullptr);
}

// SIGNAL 2
void ClassificationEngine::overallProgressChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 2, nullptr);
}

// SIGNAL 3
void ClassificationEngine::regionClassified(const QString & _t1, const QVariantMap & _t2)
{
    QMetaObject::activate<void>(this, &staticMetaObject, 3, nullptr, _t1, _t2);
}

// SIGNAL 4
void ClassificationEngine::classificationCompleted(const QVariantList & _t1)
{
    QMetaObject::activate<void>(this, &staticMetaObject, 4, nullptr, _t1);
}

// SIGNAL 5
void ClassificationEngine::classificationError(const QString & _t1, const QString & _t2)
{
    QMetaObject::activate<void>(this, &staticMetaObject, 5, nullptr, _t1, _t2);
}

// SIGNAL 6
void ClassificationEngine::regionsNeedReview(const QVariantList & _t1)
{
    QMetaObject::activate<void>(this, &staticMetaObject, 6, nullptr, _t1);
}
QT_WARNING_POP
