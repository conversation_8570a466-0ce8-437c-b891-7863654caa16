=== BASIC FILE INFO ===
File: /Volumes/Projects/Waafer Export/WAAFer_Export_4.aaf
File size: 729,088 bytes

=== HEADER INFO ===
Header: <aaf2.content.Header Header at 0x106de40f0>
Version: Unknown
Object Model Version: Unknown
Operational Pattern: Unknown

=== CONTENT INFO ===
Content: <aaf2.content.ContentStorage ContentStorage at 0x106016df0>

=== MOBS (1 total) ===

Mob 1:
  Type: CompositionMob
  Name: WAAFer Export
  Mob ID: urn:smpte:umid:060a2b34.01010105.01010f20.13000000.c8149b34.f9924c78.b29f519a.6bd25ba4
  Usage Code: No usage code
  Slots: 1
    Slot 1:
      Type: TimelineMobSlot
      Slot ID: 1
      Name: Main Audio Track
      Physical Track Number: None
      Segment: Sequence
      Segment Length: 0
      Components: 700
        Component 1: SourceClip
          Length: 48000
          Start Time: Unknown
        Component 2: SourceClip
          Length: 48000
          Start Time: Unknown
        Component 3: SourceClip
          Length: 48000
          Start Time: Unknown
        Component 4: SourceClip
          Length: 48000
          Start Time: Unknown
        Component 5: SourceClip
          Length: 48000
          Start Time: Unknown
        ... and 695 more components

=== DICTIONARY ===
Dictionary: <aaf2.dictionary.Dictionary Dictionary at 0x106015450>
Class definitions: Unknown
Type definitions: Unknown

=== ESSENCE DATA ===
No essence data found